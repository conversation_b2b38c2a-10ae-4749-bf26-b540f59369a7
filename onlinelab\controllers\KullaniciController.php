<?php
/**
 * <PERSON><PERSON>ı<PERSON>ı Controller Sınıfı
 */

class KullaniciController {
    private $userModel;
    
    public function __construct() {
        AuthController::checkSession();
        AuthController::requirePermission('TANIMLAMA_YAPABILIR');
        
        $this->userModel = new User();
    }
    
    // Kullanıcı listesi
    public function index() {
        $page = $_GET['page_num'] ?? 1;
        $search = $_GET['search'] ?? '';
        
        $result = $this->userModel->getUserList($page, $search);
        
        $data = [
            'users' => $result['users'],
            'pagination' => [
                'total' => $result['total'],
                'pages' => $result['pages'],
                'current_page' => $result['current_page']
            ],
            'search' => $search,
            'user' => AuthController::getCurrentUser()
        ];
        
        require_once 'views/kullanicilar/index.php';
    }
    
    // Kullanıcı ekleme formu
    public function add() {
        $data = [
            'user' => null,
            'yetkiler' => YETKILER,
            'current_user' => AuthController::getCurrentUser()
        ];
        
        require_once 'views/kullanicilar/form.php';
    }
    
    // Kullanıcı düzenleme formu
    public function edit($id) {
        $user = $this->userModel->getUserDetails($id);
        
        if (!$user) {
            $_SESSION['error'] = ERROR_MESSAGES['record_not_found'];
            header('Location: index.php?page=kullanicilar');
            exit;
        }
        
        $data = [
            'user' => $user,
            'yetkiler' => YETKILER,
            'current_user' => AuthController::getCurrentUser()
        ];
        
        require_once 'views/kullanicilar/form.php';
    }
    
    // Kullanıcı kaydet
    public function save() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Location: index.php?page=kullanicilar');
            exit;
        }
        
        $data = [
            'id' => $_POST['id'] ?? 0,
            'kullaniciadi' => $_POST['kullaniciadi'] ?? '',
            'sifresi' => $_POST['sifresi'] ?? '',
            'adisoyadi' => $_POST['adisoyadi'] ?? '',
            'email' => $_POST['email'] ?? '',
            'telefon' => $_POST['telefon'] ?? '',
            'aktif' => isset($_POST['aktif']) ? 1 : 0
        ];
        
        // Yetkileri ekle
        foreach (YETKILER as $key => $label) {
            $data[$key] = isset($_POST[$key]) ? 1 : 0;
        }
        
        $result = $this->userModel->saveUser($data);
        
        if ($result['success']) {
            $_SESSION['success'] = SUCCESS_MESSAGES['save_success'];
            header('Location: index.php?page=kullanicilar');
        } else {
            if (isset($result['errors'])) {
                $_SESSION['form_errors'] = $result['errors'];
                $_SESSION['form_data'] = $data;
            } else {
                $_SESSION['error'] = $result['error'] ?? ERROR_MESSAGES['save_error'];
            }
            
            $redirectUrl = $data['id'] > 0 ? 
                "index.php?page=kullanicilar&action=edit&id={$data['id']}" : 
                "index.php?page=kullanicilar&action=add";
            header("Location: $redirectUrl");
        }
        exit;
    }
    
    // Kullanıcı durumunu değiştir
    public function toggleStatus() {
        $id = $_GET['id'] ?? 0;
        
        if ($id <= 0) {
            $_SESSION['error'] = ERROR_MESSAGES['record_not_found'];
        } else {
            $result = $this->userModel->toggleStatus($id);
            if ($result) {
                $_SESSION['success'] = 'Kullanıcı durumu başarıyla değiştirildi.';
            } else {
                $_SESSION['error'] = 'Durum değiştirme sırasında hata oluştu.';
            }
        }
        
        header('Location: index.php?page=kullanicilar');
        exit;
    }
}
?>

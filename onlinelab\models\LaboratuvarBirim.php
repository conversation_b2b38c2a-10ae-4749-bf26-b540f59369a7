<?php
/**
 * Laboratuvar Birim Model Sınıfı
 */

class LaboratuvarBirim extends BaseModel {
    protected $table = 'laboratuvar_birimler';
    
    // Aktif birimleri getir
    public function getActiveBirimler() {
        return $this->getWhere('aktif = 1', [], 'birimadi');
    }
    
    // Birim listesi (sayfalama ile)
    public function getBirimList($page = 1, $search = '') {
        $where = '1=1';
        $params = [];
        
        if (!empty($search)) {
            $where .= ' AND birimadi LIKE ?';
            $params[] = "%$search%";
        }
        
        $birimler = $this->getPaginated($page, RECORDS_PER_PAGE, $where, $params, 'birimadi');
        $total = $this->getCount($where, $params);
        
        return [
            'birimler' => $birimler,
            'total' => $total,
            'pages' => ceil($total / RECORDS_PER_PAGE),
            'current_page' => $page
        ];
    }
    
    // Birim kaydet
    public function saveBirim($data) {
        // Validation
        $rules = [
            'birimadi' => ['required' => true, 'max_length' => 100]
        ];
        
        $errors = $this->validate($data, $rules);
        
        // Birim adı benzersizlik kontrolü
        if (!$this->isBirimAdiUnique($data['birimadi'], $data['id'] ?? null)) {
            $errors['birimadi'] = 'Bu birim adı zaten kullanılmaktadır.';
        }
        
        if (!empty($errors)) {
            return ['success' => false, 'errors' => $errors];
        }
        
        try {
            if (isset($data['id']) && $data['id'] > 0) {
                // Güncelleme
                $id = $data['id'];
                unset($data['id']);
                $this->update($id, $data);
                return ['success' => true, 'id' => $id];
            } else {
                // Yeni kayıt
                unset($data['id']);
                $data['aktif'] = $data['aktif'] ?? 1;
                $id = $this->insert($data);
                return ['success' => true, 'id' => $id];
            }
        } catch (Exception $e) {
            return ['success' => false, 'error' => 'Kayıt sırasında hata oluştu.'];
        }
    }
    
    // Birim adı benzersizlik kontrolü
    public function isBirimAdiUnique($birimAdi, $excludeId = null) {
        $where = 'birimadi = ?';
        $params = [$birimAdi];
        
        if ($excludeId) {
            $where .= ' AND id != ?';
            $params[] = $excludeId;
        }
        
        return !$this->exists($where, $params);
    }
    
    // Birim durumunu değiştir (aktif/pasif)
    public function toggleStatus($id) {
        $birim = $this->getById($id);
        if ($birim) {
            $newStatus = $birim['aktif'] == 1 ? 0 : 1;
            return $this->update($id, ['aktif' => $newStatus]);
        }
        return false;
    }
    
    // Dropdown için birim seçenekleri
    public function getOptionsForSelect() {
        $birimler = $this->getActiveBirimler();
        $options = [];
        
        foreach ($birimler as $birim) {
            $options[$birim['id']] = $birim['birimadi'];
        }
        
        return $options;
    }
    
    // Birim atama istatistikleri
    public function getAtamaStats($id) {
        $sql = "SELECT COUNT(*) as atama_sayisi 
                FROM lab_atamalar 
                WHERE birim_id = ?";
        
        $result = $this->db->fetch($sql, [$id]);
        return $result['atama_sayisi'] ?? 0;
    }
    
    // Birime atanan numuneler
    public function getAtananNumuneler($birimId, $page = 1) {
        $sql = "SELECT n.*, a.atama_tarihi, a.analiz_tarihi, a.analiz_sonucu,
                       u.urunadi_t as urun_adi
                FROM lab_atamalar a
                INNER JOIN lab_numuneler n ON a.numune_id = n.id
                LEFT JOIN urunler u ON n.urun_id = u.id
                WHERE a.birim_id = ? AND n.silindi = 0
                ORDER BY a.atama_tarihi DESC";
        
        $numuneler = $this->db->getPaginated($sql, [$birimId], $page, RECORDS_PER_PAGE);
        
        $countSql = "SELECT COUNT(*) as total 
                     FROM lab_atamalar a
                     INNER JOIN lab_numuneler n ON a.numune_id = n.id
                     WHERE a.birim_id = ? AND n.silindi = 0";
        $totalResult = $this->db->fetch($countSql, [$birimId]);
        $total = $totalResult['total'] ?? 0;
        
        return [
            'numuneler' => $numuneler,
            'total' => $total,
            'pages' => ceil($total / RECORDS_PER_PAGE),
            'current_page' => $page
        ];
    }
    
    // En çok atama alan birimler
    public function getMostAssignedBirimler($limit = 10) {
        $sql = "SELECT b.*, COUNT(a.id) as atama_sayisi
                FROM {$this->table} b
                LEFT JOIN lab_atamalar a ON b.id = a.birim_id
                WHERE b.aktif = 1
                GROUP BY b.id
                ORDER BY atama_sayisi DESC, b.birimadi
                LIMIT ?";
        
        return $this->db->fetchAll($sql, [$limit]);
    }
    
    // Birim iş yükü (bekleyen analizler)
    public function getWorkload($birimId) {
        $sql = "SELECT COUNT(*) as bekleyen_analiz
                FROM lab_atamalar a
                INNER JOIN lab_numuneler n ON a.numune_id = n.id
                WHERE a.birim_id = ? 
                AND a.analiz_tarihi IS NULL 
                AND n.silindi = 0";
        
        $result = $this->db->fetch($sql, [$birimId]);
        return $result['bekleyen_analiz'] ?? 0;
    }
}
?>

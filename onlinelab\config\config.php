<?php
/**
 * Sistem Konfigürasyon Dosyası
 */

// Temel ayarlar
define('APP_NAME', 'Online Laboratuvar Yönetim Sistemi');
define('APP_VERSION', '1.0.0');
define('BASE_URL', 'http://localhost/laboratuvar/onlinelab/');

// Veritabanı ayarları
define('DB_HOST', 'localhost');
define('DB_NAME', 'laboratuvar_db');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');

// Güvenlik ayarları
define('SESSION_TIMEOUT', 3600); // 1 saat
define('PASSWORD_HASH_ALGO', PASSWORD_DEFAULT);

// Dosya yükleme ayarları
define('UPLOAD_MAX_SIZE', 5 * 1024 * 1024); // 5MB
define('UPLOAD_ALLOWED_TYPES', ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'jpg', 'jpeg', 'png']);
define('UPLOAD_PATH', 'uploads/');

// Sayfalama ayarları
define('RECORDS_PER_PAGE', 25);

// Tarih formatları
define('DATE_FORMAT', 'd.m.Y');
define('DATETIME_FORMAT', 'd.m.Y H:i');

// Numune durumları
define('NUMUNE_DURUMLARI', [
    'Numune Kabul',
    'Ön Hazırlık',
    'Analiz Aşamasında',
    'Analiz Tamamlandı',
    'Rapor Hazırlandı',
    'Teslim Edildi'
]);

// Numune türleri
define('NUMUNE_TURLERI', [
    'İç Karantina',
    'Dış Karantina'
]);

// Yetki seviyeleri
define('YETKILER', [
    'NUMUNE_KABUL_YAPABILIR' => 'Numune Kabul Yapabilir',
    'NUMUNE_LISTESI_GOREBILIR' => 'Numune Listesi Görebilir',
    'NUMUNE_ONHAZIRLIK_YAPABILIR' => 'Numune Ön Hazırlık Yapabilir',
    'NUMUNE_SONUC_GIREBILIR' => 'Numune Sonuç Girebilir',
    'TANIMLAMA_YAPABILIR' => 'Tanımlama Yapabilir',
    'NUMUNE_GENEL_GORUNUM' => 'Numune Genel Görünüm',
    'NUMUNE_ATAMA_YAPABILIR' => 'Numune Atama Yapabilir'
]);

// Zaman dilimi
date_default_timezone_set('Europe/Istanbul');

// Utility fonksiyonlarını dahil et
require_once __DIR__ . '/../includes/functions.php';

// Hata mesajları
define('ERROR_MESSAGES', [
    'login_failed' => 'Kullanıcı adı veya şifre hatalı!',
    'access_denied' => 'Bu işlem için yetkiniz bulunmamaktadır!',
    'record_not_found' => 'Kayıt bulunamadı!',
    'save_error' => 'Kayıt sırasında hata oluştu!',
    'delete_error' => 'Silme işlemi sırasında hata oluştu!',
    'upload_error' => 'Dosya yükleme sırasında hata oluştu!',
    'invalid_file_type' => 'Geçersiz dosya türü!',
    'file_too_large' => 'Dosya boyutu çok büyük!'
]);

// Başarı mesajları
define('SUCCESS_MESSAGES', [
    'save_success' => 'Kayıt başarıyla kaydedildi!',
    'delete_success' => 'Kayıt başarıyla silindi!',
    'upload_success' => 'Dosya başarıyla yüklendi!',
    'login_success' => 'Giriş başarılı!',
    'logout_success' => 'Çıkış yapıldı!'
]);
?>

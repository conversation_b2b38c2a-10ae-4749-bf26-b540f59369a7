-- <PERSON><PERSON><PERSON><PERSON> - Firebird Veritabanı Şeması
-- Mevcut IHRACAT_YENI.FDB veritabanına eklenecek tablolar

-- <PERSON>llanı<PERSON><PERSON>lar tablosu
CREATE TABLE KULLANICILAR (
    ID INTEGER NOT NULL,
    <PERSON><PERSON><PERSON>AN<PERSON><PERSON>DI VARCHAR(50) NOT NULL,
    SIFRESI VARCHAR(255) NOT NULL,
    ADISOYADI VARCHAR(100) NOT NULL,
    EMAIL VARCHAR(100),
    TELEFON VARCHAR(20),
    AKTIF SMALLINT DEFAULT 1,
    KAYIT_TARIHI TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- <PERSON><PERSON> (Delphi sisteminden)
    NUMUNE_KABUL_YAPABILIR SMALLINT DEFAULT 0,
    NUMUNE_LISTESI_GOREBILIR SMALLINT DEFAULT 0,
    NUMUNE_ONHAZIRLIK_YAPABILIR SMALLINT DEFAULT 0,
    NUMUNE_SONUC_GIREBILIR SMALLINT DEFAULT 0,
    <PERSON>ANIMLAMA_YAPABILIR SMALLINT DEFAULT 0,
    NUMUNE_GENEL_GORUNUM SMALLINT DEFAULT 0,
    NUMUNE_ATAMA_YAPABILIR SMALLINT DEFAULT 0,
    
    CONSTRAINT PK_KULLANICILAR PRIMARY KEY (ID),
    CONSTRAINT UQ_KULLANICILAR_KULLANICIADI UNIQUE (KULLANICIADI)
);

-- Generator for KULLANICILAR
CREATE GENERATOR KULLANICILAR_ID_GEN;
SET GENERATOR KULLANICILAR_ID_GEN TO 0;

-- Trigger for auto-increment
CREATE TRIGGER KULLANICILAR_BI FOR KULLANICILAR
ACTIVE BEFORE INSERT POSITION 0
AS
BEGIN
  IF (NEW.ID IS NULL) THEN
    NEW.ID = GEN_ID(KULLANICILAR_ID_GEN, 1);
END;

-- Genel tanımlar tablosu (zaten mevcut olabilir)
-- CREATE TABLE GENELTANIM (
--     ID INTEGER NOT NULL,
--     SAHIBI INTEGER NOT NULL,
--     IZAHAT VARCHAR(255) NOT NULL,
--     ACIKLAMA BLOB SUB_TYPE TEXT,
--     CONSTRAINT PK_GENELTANIM PRIMARY KEY (ID)
-- );

-- Ürünler tablosu (zaten mevcut olabilir)
-- CREATE TABLE URUNLER (
--     ID INTEGER NOT NULL,
--     URUNADI_T VARCHAR(255) NOT NULL,
--     ACIKLAMA BLOB SUB_TYPE TEXT,
--     AKTIF SMALLINT DEFAULT 1,
--     CONSTRAINT PK_URUNLER PRIMARY KEY (ID)
-- );

-- Laboratuvar birimleri tablosu
CREATE TABLE LABORATUVAR_BIRIMLER (
    ID INTEGER NOT NULL,
    BIRIMADI VARCHAR(100) NOT NULL,
    ACIKLAMA BLOB SUB_TYPE TEXT,
    AKTIF SMALLINT DEFAULT 1,
    CONSTRAINT PK_LABORATUVAR_BIRIMLER PRIMARY KEY (ID)
);

-- Generator for LABORATUVAR_BIRIMLER
CREATE GENERATOR LABORATUVAR_BIRIMLER_ID_GEN;
SET GENERATOR LABORATUVAR_BIRIMLER_ID_GEN TO 0;

-- Trigger for auto-increment
CREATE TRIGGER LABORATUVAR_BIRIMLER_BI FOR LABORATUVAR_BIRIMLER
ACTIVE BEFORE INSERT POSITION 0
AS
BEGIN
  IF (NEW.ID IS NULL) THEN
    NEW.ID = GEN_ID(LABORATUVAR_BIRIMLER_ID_GEN, 1);
END;

-- Numuneler ana tablosu
CREATE TABLE LAB_NUMUNELER (
    ID INTEGER NOT NULL,
    LAB_KAYITNO VARCHAR(50),
    NUMUNE_TURU VARCHAR(20) NOT NULL,
    NUMUNE_ALT_TURU VARCHAR(100),
    URUN_ID INTEGER,
    FIRMA_ID INTEGER,
    GONDEREN VARCHAR(255),
    GONDEREN_PERSONEL VARCHAR(100),
    MENSEI VARCHAR(100),
    BASVURU_NO VARCHAR(50),
    MUHUR_NO VARCHAR(50),
    ETIKET_NO VARCHAR(50),
    MIKTAR DECIMAL(10,2),
    BIRIM VARCHAR(20),
    ACIKLAMA BLOB SUB_TYPE TEXT,
    BARKOD VARCHAR(20),
    DURUMU VARCHAR(100) DEFAULT 'Numune Kabul',
    UCRET_DURUMU VARCHAR(50),
    GELIS_TARIHI TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    SILINDI SMALLINT DEFAULT 0,
    
    CONSTRAINT PK_LAB_NUMUNELER PRIMARY KEY (ID),
    CONSTRAINT FK_LAB_NUMUNELER_URUN FOREIGN KEY (URUN_ID) REFERENCES URUNLER(ID),
    CONSTRAINT FK_LAB_NUMUNELER_FIRMA FOREIGN KEY (FIRMA_ID) REFERENCES GENELTANIM(ID)
);

-- Generator for LAB_NUMUNELER
CREATE GENERATOR LAB_NUMUNELER_ID_GEN;
SET GENERATOR LAB_NUMUNELER_ID_GEN TO 0;

-- Trigger for auto-increment
CREATE TRIGGER LAB_NUMUNELER_BI FOR LAB_NUMUNELER
ACTIVE BEFORE INSERT POSITION 0
AS
BEGIN
  IF (NEW.ID IS NULL) THEN
    NEW.ID = GEN_ID(LAB_NUMUNELER_ID_GEN, 1);
END;

-- Indexes for LAB_NUMUNELER
CREATE INDEX IDX_LAB_NUMUNELER_TURU ON LAB_NUMUNELER (NUMUNE_TURU);
CREATE INDEX IDX_LAB_NUMUNELER_DURUMU ON LAB_NUMUNELER (DURUMU);
CREATE INDEX IDX_LAB_NUMUNELER_TARIH ON LAB_NUMUNELER (GELIS_TARIHI);
CREATE INDEX IDX_LAB_NUMUNELER_KAYITNO ON LAB_NUMUNELER (LAB_KAYITNO);
CREATE INDEX IDX_LAB_NUMUNELER_BARKOD ON LAB_NUMUNELER (BARKOD);

-- Laboratuvar atamalar tablosu
CREATE TABLE LAB_ATAMALAR (
    ID INTEGER NOT NULL,
    NUMUNE_ID INTEGER NOT NULL,
    BIRIM_ID INTEGER NOT NULL,
    ATAMA_TARIHI TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ANALIZ_TARIHI TIMESTAMP,
    ANALIZ_SONUCU VARCHAR(100),
    ANALIZ_METHODU VARCHAR(255),
    ATAYAN_KULLANICI INTEGER,
    
    CONSTRAINT PK_LAB_ATAMALAR PRIMARY KEY (ID),
    CONSTRAINT FK_LAB_ATAMALAR_NUMUNE FOREIGN KEY (NUMUNE_ID) REFERENCES LAB_NUMUNELER(ID) ON DELETE CASCADE,
    CONSTRAINT FK_LAB_ATAMALAR_BIRIM FOREIGN KEY (BIRIM_ID) REFERENCES LABORATUVAR_BIRIMLER(ID),
    CONSTRAINT FK_LAB_ATAMALAR_KULLANICI FOREIGN KEY (ATAYAN_KULLANICI) REFERENCES KULLANICILAR(ID),
    CONSTRAINT UQ_LAB_ATAMALAR UNIQUE (NUMUNE_ID, BIRIM_ID)
);

-- Generator for LAB_ATAMALAR
CREATE GENERATOR LAB_ATAMALAR_ID_GEN;
SET GENERATOR LAB_ATAMALAR_ID_GEN TO 0;

-- Trigger for auto-increment
CREATE TRIGGER LAB_ATAMALAR_BI FOR LAB_ATAMALAR
ACTIVE BEFORE INSERT POSITION 0
AS
BEGIN
  IF (NEW.ID IS NULL) THEN
    NEW.ID = GEN_ID(LAB_ATAMALAR_ID_GEN, 1);
END;

-- Sonuç etmenleri tablosu
CREATE TABLE LAB_SONUC_ETMENLER (
    ID INTEGER NOT NULL,
    NUMUNE_ID INTEGER NOT NULL,
    ETMEN_ADI VARCHAR(255) NOT NULL,
    BULASIKMI VARCHAR(10) DEFAULT 'Hayır',
    DEGER VARCHAR(100),
    BIRIM VARCHAR(50),
    
    CONSTRAINT PK_LAB_SONUC_ETMENLER PRIMARY KEY (ID),
    CONSTRAINT FK_LAB_SONUC_ETMENLER_NUMUNE FOREIGN KEY (NUMUNE_ID) REFERENCES LAB_NUMUNELER(ID) ON DELETE CASCADE
);

-- Generator for LAB_SONUC_ETMENLER
CREATE GENERATOR LAB_SONUC_ETMENLER_ID_GEN;
SET GENERATOR LAB_SONUC_ETMENLER_ID_GEN TO 0;

-- Trigger for auto-increment
CREATE TRIGGER LAB_SONUC_ETMENLER_BI FOR LAB_SONUC_ETMENLER
ACTIVE BEFORE INSERT POSITION 0
AS
BEGIN
  IF (NEW.ID IS NULL) THEN
    NEW.ID = GEN_ID(LAB_SONUC_ETMENLER_ID_GEN, 1);
END;

-- Log tablosu
CREATE TABLE LAB_LOG (
    ID INTEGER NOT NULL,
    NUMUNE_ID INTEGER,
    ISLEM VARCHAR(255) NOT NULL,
    ISLEMI_YAPAN VARCHAR(100),
    ONCEKI_DEGER BLOB SUB_TYPE TEXT,
    TARIH TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT PK_LAB_LOG PRIMARY KEY (ID),
    CONSTRAINT FK_LAB_LOG_NUMUNE FOREIGN KEY (NUMUNE_ID) REFERENCES LAB_NUMUNELER(ID) ON DELETE SET NULL
);

-- Generator for LAB_LOG
CREATE GENERATOR LAB_LOG_ID_GEN;
SET GENERATOR LAB_LOG_ID_GEN TO 0;

-- Trigger for auto-increment
CREATE TRIGGER LAB_LOG_BI FOR LAB_LOG
ACTIVE BEFORE INSERT POSITION 0
AS
BEGIN
  IF (NEW.ID IS NULL) THEN
    NEW.ID = GEN_ID(LAB_LOG_ID_GEN, 1);
END;

-- Indexes for LAB_LOG
CREATE INDEX IDX_LAB_LOG_NUMUNE ON LAB_LOG (NUMUNE_ID);
CREATE INDEX IDX_LAB_LOG_TARIH ON LAB_LOG (TARIH);

-- Parametreler tablosu (sayaçlar için)
CREATE TABLE PARAMETRELER (
    ID INTEGER NOT NULL,
    LAB_IC_NO INTEGER DEFAULT 1,
    LAB_DIS_NO INTEGER DEFAULT 1,
    CONSTRAINT PK_PARAMETRELER PRIMARY KEY (ID)
);

-- Generator for PARAMETRELER
CREATE GENERATOR PARAMETRELER_ID_GEN;
SET GENERATOR PARAMETRELER_ID_GEN TO 0;

-- Trigger for auto-increment
CREATE TRIGGER PARAMETRELER_BI FOR PARAMETRELER
ACTIVE BEFORE INSERT POSITION 0
AS
BEGIN
  IF (NEW.ID IS NULL) THEN
    NEW.ID = GEN_ID(PARAMETRELER_ID_GEN, 1);
END;

-- Varsayılan veriler
INSERT INTO PARAMETRELER (LAB_IC_NO, LAB_DIS_NO) VALUES (1, 1);

-- Varsayılan admin kullanıcısı (şifre: admin123 -> MD5: 0192023a7bbd73250516f069df18b500)
INSERT INTO KULLANICILAR (
    KULLANICIADI, SIFRESI, ADISOYADI, 
    NUMUNE_KABUL_YAPABILIR, NUMUNE_LISTESI_GOREBILIR, 
    NUMUNE_ONHAZIRLIK_YAPABILIR, NUMUNE_SONUC_GIREBILIR, 
    TANIMLAMA_YAPABILIR, NUMUNE_GENEL_GORUNUM, NUMUNE_ATAMA_YAPABILIR
) VALUES (
    'admin', '0192023a7bbd73250516f069df18b500', 'Sistem Yöneticisi',
    1, 1, 1, 1, 1, 1, 1
);

-- Örnek laboratuvar birimleri
INSERT INTO LABORATUVAR_BIRIMLER (BIRIMADI, ACIKLAMA) VALUES 
('Mikrobiyoloji', 'Mikrobiyal analizler');
INSERT INTO LABORATUVAR_BIRIMLER (BIRIMADI, ACIKLAMA) VALUES 
('Kimya', 'Kimyasal analizler');
INSERT INTO LABORATUVAR_BIRIMLER (BIRIMADI, ACIKLAMA) VALUES 
('Pestisit', 'Pestisit kalıntı analizleri');
INSERT INTO LABORATUVAR_BIRIMLER (BIRIMADI, ACIKLAMA) VALUES 
('Aflatoksin', 'Aflatoksin analizleri');
INSERT INTO LABORATUVAR_BIRIMLER (BIRIMADI, ACIKLAMA) VALUES 
('Fiziksel', 'Fiziksel özellik analizleri');

COMMIT;

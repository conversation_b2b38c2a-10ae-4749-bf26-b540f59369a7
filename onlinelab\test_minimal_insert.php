<?php
require_once 'config/database.php';

try {
    $db = new Database();
    
    echo "<h2>Minimal Insert Test</h2>";
    
    // Sadece z<PERSON>unlu alanlarla test
    $minimalData = [
        'numune_turu' => 'İç Karantina',
        'LAB_KAYITNO' => 'TEST-2025-001',
        'gonderen' => 'Test Gönderen',
        'urun_id' => 1,
        'miktar' => 10,
        'birim' => 'kg',
        'gelis_tarihi' => date('Y-m-d H:i:s'),
        'durumu' => 'Numune Kabul'
    ];
    
    echo "<h3>Minimal Test Verisi:</h3>";
    echo "<pre>" . print_r($minimalData, true) . "</pre>";
    
    // Tablo alanlarını kontrol et
    $sql = "DESCRIBE lab_numuneler";
    $columns = $db->fetchAll($sql);
    $validFields = array_column($columns, 'Field');
    
    echo "<h3>Tab<PERSON> Alan<PERSON>ı:</h3>";
    echo "<p>" . implode(', ', $validFields) . "</p>";
    
    // Sadece geçerli alanları kullan
    $cleanData = [];
    foreach ($minimalData as $key => $value) {
        if (in_array($key, $validFields)) {
            $cleanData[$key] = $value;
        } else {
            echo "<p style='color: red;'>Geçersiz alan: $key</p>";
        }
    }
    
    echo "<h3>Temizlenmiş Veri:</h3>";
    echo "<pre>" . print_r($cleanData, true) . "</pre>";
    
    // Insert SQL oluştur
    $fields = array_keys($cleanData);
    $placeholders = array_fill(0, count($fields), '?');
    
    $sql = "INSERT INTO lab_numuneler (" . implode(', ', $fields) . ") 
            VALUES (" . implode(', ', $placeholders) . ")";
    
    echo "<h3>SQL:</h3>";
    echo "<pre>" . htmlspecialchars($sql) . "</pre>";
    
    echo "<h3>Values:</h3>";
    echo "<pre>" . print_r(array_values($cleanData), true) . "</pre>";
    
    // Test insert
    try {
        $id = $db->insert($sql, array_values($cleanData));
        echo "<div style='color: green; background: #e8f5e8; padding: 10px; border: 1px solid #4caf50;'>";
        echo "✅ Başarılı! Eklenen ID: $id";
        echo "</div>";
        
        // Eklenen kaydı kontrol et
        $checkSql = "SELECT * FROM lab_numuneler WHERE id = ?";
        $record = $db->fetch($checkSql, [$id]);
        echo "<h4>Eklenen Kayıt:</h4>";
        echo "<pre>" . print_r($record, true) . "</pre>";
        
    } catch (Exception $e) {
        echo "<div style='color: red; background: #ffe6e6; padding: 10px; border: 1px solid #ff0000;'>";
        echo "❌ Insert Hatası: " . $e->getMessage();
        echo "</div>";
        
        // PDO hata detayları
        $errorInfo = $db->getConnection()->errorInfo();
        echo "<h4>PDO Error Info:</h4>";
        echo "<pre>" . print_r($errorInfo, true) . "</pre>";
        
        // SQL State açıklaması
        if (isset($errorInfo[0])) {
            echo "<h4>SQL State: {$errorInfo[0]}</h4>";
            switch ($errorInfo[0]) {
                case '23000':
                    echo "<p>Integrity constraint violation (foreign key, unique, etc.)</p>";
                    break;
                case '42S22':
                    echo "<p>Column not found</p>";
                    break;
                case '42000':
                    echo "<p>Syntax error or access violation</p>";
                    break;
                default:
                    echo "<p>Diğer SQL hatası</p>";
            }
        }
    }
    
} catch (Exception $e) {
    echo "<div style='color: red;'>Genel Hata: " . $e->getMessage() . "</div>";
}
?>

{
ALTER TABLE LAB_ETMENLER ADD KABUL_ALT_TURU VARCHAR(100);
}

{$define UNIGUI_VCL} // Comment out this line to turn this project into an ISAPI module

{$ifndef UNIGUI_VCL}
library
{$else}
program
{$endif}
  Laboratuvar;

uses
  uniGUIISAPI,
  Forms,
  ServerModule in 'ServerModule.pas' {UniServerModule: TUniGUIServerModule},
  MainModule in 'MainModule.pas' {UniMainModule: TUniGUIMainModule},
  Main in 'Main.pas' {MainForm: TUniForm},
  Numune<PERSON><PERSON><PERSON> in 'NumuneListesi.pas' {frmNumuneListesi: TUniForm},
  NumuneKabul in 'NumuneKabul.pas' {frmNumuneKabul: TUniForm},
  uGenelTanim in 'uGenelTanim.pas' {frmGenelTanim: TUniForm},
  urunler in 'urunler.pas' {frmUrunler: TUniForm},
  uTanimBirimler in 'uTanimBirimler.pas' {frmTanimBirimler: TUniForm},
  uTanimBirimEkle in 'uTanimBirimEkle.pas' {frmTanimBirimEkle: TUniForm},
  uTanimPersonel in 'uTanimPersonel.pas' {frmTanimPersonel: TUniForm},
  uTanimPersonelEkle in 'uTanimPersonelEkle.pas' {frmTanimPersonelEkle: TUniForm},
  BirimlereGonder in 'BirimlereGonder.pas' {frmBirimlereGonder: TUniForm},
  Onhazirlik in 'Onhazirlik.pas' {frmOnhazirlik: TUniForm},
  NumuneSonuc in 'NumuneSonuc.pas' {frmNumuneSonuc: TUniForm},
  NumuneSonucGir in 'NumuneSonucGir.pas' {frmNumuneSonucGir: TUniForm},
  uLaboratuvarBirimler in 'uLaboratuvarBirimler.pas' {frmLaboratuvarBirimler: TUniForm},
  uKullanicilar in 'uKullanicilar.pas' {frmKullanicilar: TUniForm},
  uKullaniciEkle in 'uKullaniciEkle.pas' {frmKullaniciEkle: TUniForm},
  login in 'login.pas' {UniLoginForm1: TUniLoginForm},
  urapor in 'urapor.pas' {frmRapor: TUniForm},
  log in 'log.pas' {frmLog: TUniForm},
  dashboard in 'dashboard.pas' {frmDashboard: TUniForm},
  uUlkeler in 'uUlkeler.pas' {FrmUlkeler: TUniForm},
  etmen_tanim in 'etmen_tanim.pas' {frmEtmenTanim: TUniForm},
  donersermayebaglanti in 'donersermayebaglanti.pas' {frmDonersermayebaglanti: TUniForm},
  uKurumEvrak in 'uKurumEvrak.pas' {frmKurumEvrak: TUniForm},
  uKurumEvrakEkle in 'uKurumEvrakEkle.pas' {frmKurumEvrakEkle: TUniForm},
  numunekalangir in 'numunekalangir.pas' {frmnumunekalangir: TUniForm};

{$R *.res}

{$ifndef UNIGUI_VCL}
exports
  GetExtensionVersion,
  HttpExtensionProc,
  TerminateExtension;
{$endif}


begin
{$ifdef UNIGUI_VCL}
  ReportMemoryLeaksOnShutdown := True;
  Application.Initialize;
  TUniServerModule.Create(Application);
  Application.Run;
{$endif}
end.

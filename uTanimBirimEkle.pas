unit uTanimBirimEkle;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics,
  Controls, Forms, Dialogs, uniGUITypes, uniGUIAbstractClasses,
  uniGUIClasses, uniGUIForm, uniCheckBox, uniDBCheckBox, uniEdit, uniDBEdit,
  uniLabel, uniButton, uniBitBtn, uniSpeedButton, uniGUIBaseClasses, uniPanel,
  uniMultiItem, uniComboBox, uniDBComboBox, uniDBLookupComboBox, Data.DB;

type
  TfrmTanimBirimEkle = class(TUniForm)
    UniPanel1: TUniPanel;
    UniSpeedButton2: TUniSpeedButton;
    UniSpeedButton3: TUniSpeedButton;
    UniSpeedButton5: TUniSpeedButton;
    lbldurum: TUniLabel;
    UniDBEdit1: TUniDBEdit;
    UniDBCheckBox1: TUniDBCheckBox;
    dsbirim: TDataSource;
    procedure UniSpeedButton3Click(Sender: TObject);
    procedure UniSpeedButton2Click(Sender: TObject);
    procedure UniFormShow(Sender: TObject);
  private
    { Private declarations }
  public
    { Public declarations }
  end;

function frmTanimBirimEkle: TfrmTanimBirimEkle;

implementation

{$R *.dfm}

uses
  MainModule, uniGUIApplication;

function frmTanimBirimEkle: TfrmTanimBirimEkle;
begin
  Result := TfrmTanimBirimEkle(UniMainModule.GetFormInstance(TfrmTanimBirimEkle));
end;

procedure TfrmTanimBirimEkle.UniFormShow(Sender: TObject);
begin
  unimainmodule.tblBirim.Open;unimainmodule.tblBirim.Last;
  dsbirim.DataSet:=unimainmodule.tblBirim;
end;

procedure TfrmTanimBirimEkle.UniSpeedButton2Click(Sender: TObject);
begin
  With unimainmodule.tblBirim do begin

   edit;
   post;
   transaction.CommitRetaining;
  end;
  close;
end;

procedure TfrmTanimBirimEkle.UniSpeedButton3Click(Sender: TObject);
begin
 close;
end;

end.

object frmBirimlereGonder: TfrmBirimlereGonder
  Left = 0
  Top = 0
  ClientHeight = 447
  ClientWidth = 709
  Caption = 'Birimlere G'#246'nder'
  OnShow = UniFormShow
  OldCreateOrder = False
  MonitoredKeys.Keys = <>
  OnCreate = UniFormCreate
  PixelsPerInch = 96
  TextHeight = 13
  object UniPanel1: TUniPanel
    Left = 0
    Top = 362
    Width = 709
    Height = 40
    Visible = False
    Align = alBottom
    TabOrder = 0
    BorderStyle = ubsNone
    Color = 16744448
    object UniButton2: TUniButton
      AlignWithMargins = True
      Left = 3
      Top = 3
      Width = 61
      Height = 34
      Hint = 'Se'#231'ili Numuneyi <PERSON>ler'
      Visible = False
      ShowHint = True
      ParentShowHint = False
      Caption = 'Sil'
      Align = alLeft
      TabOrder = 1
      OnClick = UniButton2Click
    end
    object UniButton7: TUniButton
      AlignWithMargins = True
      Left = 70
      Top = 3
      Width = 62
      Height = 34
      Visible = False
      Caption = #199'o'#287'alt'
      Align = alLeft
      TabOrder = 2
    end
  end
  object UniPanel2: TUniPanel
    Left = 0
    Top = 402
    Width = 709
    Height = 45
    Margins.Left = 4
    Margins.Top = 4
    Margins.Right = 4
    Margins.Bottom = 4
    Align = alBottom
    TabOrder = 1
    TabStop = False
    BorderStyle = ubsNone
    Color = 16447736
    object UniSpeedButton3: TUniSpeedButton
      AlignWithMargins = True
      Left = 615
      Top = 6
      Width = 90
      Height = 33
      Margins.Left = 1
      Margins.Top = 6
      Margins.Right = 4
      Margins.Bottom = 6
      Caption = 'Kapat'
      Align = alRight
      ParentColor = False
      Color = clBtnFace
      TabOrder = 1
      OnClick = UniSpeedButton3Click
    end
    object btnKaydet: TUniButton
      AlignWithMargins = True
      Left = 526
      Top = 6
      Width = 87
      Height = 33
      Margins.Left = 4
      Margins.Top = 6
      Margins.Right = 1
      Margins.Bottom = 6
      Visible = False
      Caption = 'Kaydet'
      Align = alRight
      TabOrder = 2
      ScreenMask.Enabled = True
      ScreenMask.Message = 'L'#252'tfen Bekleyiniz...'
      OnClick = btnKaydetClick
    end
    object UniSpeedButton1: TUniSpeedButton
      AlignWithMargins = True
      Left = 3
      Top = 6
      Width = 60
      Height = 33
      Margins.Top = 6
      Margins.Right = 4
      Margins.Bottom = 6
      Caption = 'Sil'
      Align = alLeft
      ParentColor = False
      Color = clBtnFace
      TabOrder = 3
      OnClick = UniSpeedButton1Click
    end
    object btnBarkod: TUniButton
      AlignWithMargins = True
      Left = 68
      Top = 6
      Width = 96
      Height = 33
      Margins.Left = 1
      Margins.Top = 6
      Margins.Right = 4
      Margins.Bottom = 6
      Visible = False
      Caption = 'Barkod Yazd'#305'r'
      Align = alLeft
      TabOrder = 4
      ScreenMask.Enabled = True
      ScreenMask.Message = 'L'#252'tfen Bekleyiniz...'
    end
    object UniButton3: TUniButton
      AlignWithMargins = True
      Left = 169
      Top = 6
      Width = 106
      Height = 33
      Margins.Left = 1
      Margins.Top = 6
      Margins.Right = 4
      Margins.Bottom = 6
      Caption = 'Etmenleri Y'#252'kle'
      Align = alLeft
      TabOrder = 5
      ScreenMask.Enabled = True
      ScreenMask.Message = 'L'#252'tfen Bekleyiniz...'
      OnClick = UniButton3Click
    end
  end
  object UniPanel3: TUniPanel
    Left = 0
    Top = 0
    Width = 709
    Height = 89
    Align = alTop
    TabOrder = 2
    BorderStyle = ubsNone
    ExplicitTop = -6
    ExplicitWidth = 765
    object UniLabel10: TUniLabel
      Left = 19
      Top = 18
      Width = 256
      Height = 13
      Caption = 'Analiz Ger'#231'ekle'#351'tirecek Laboratuvar Birimleri'
      ParentFont = False
      Font.Style = [fsBold]
      TabOrder = 1
    end
    object lkbBirimler: TUniDBLookupComboBox
      Left = 19
      Top = 37
      Width = 318
      Height = 21
      ListField = 'BIRIMADI'
      ListSource = dsNumuneBirimler
      KeyField = 'ID'
      ListFieldIndex = 0
      TabOrder = 2
      Color = clWindow
      ClientEvents.ExtEvents.Strings = (
        
          'OnAfterrender=function OnAfterrender(sender)'#13#10'{'#13#10'      sender.al' +
          'lowBlank=true; '#13#10'  sender.editable = true;//This allows you to e' +
          'dit the DBLookupCombobox '#13#10'  sender.forceSelection=true;//This l' +
          'ine allows only item in list to be selected'#13#10'//  sender.multiSel' +
          'ect= true;'#13#10'//  sender.matchFieldWidth= false;'#13#10' // sender.listW' +
          'idth= 1550;'#13#10' sender.selectOnFocus=true;'#13#10'}'
        
          'OnFocus=function OnFocus(sender)'#13#10'{'#13#10'  sender.selectOnFocus=true' +
          ';'#13#10'}')
      ClientEvents.UniEvents.Strings = (
        
          'beforeInit=function OnBeforeInit(sender)'#13#10'{'#13#10'//    sender.matchF' +
          'ieldWidth= false;'#13#10'}')
      OnCloseUp = lkbBirimlerCloseUp
    end
    object UniComboBox1: TUniComboBox
      Left = 343
      Top = 36
      Width = 256
      TabOrder = 3
      ClientEvents.ExtEvents.Strings = (
        
          'beforerender=function beforerender(sender, eOpts)'#13#10'{'#13#10'         s' +
          'ender.allowBlank=true; '#13#10'  sender.editable = true;'#13#10'}')
      RemoteFilter = False
      CaseSensitive = True
      IconItems = <>
    end
    object UniLabel8: TUniLabel
      Left = 343
      Top = 18
      Width = 48
      Height = 13
      Caption = 'Etmen Ad'#305
      TabOrder = 4
    end
    object UniButton1: TUniButton
      Left = 615
      Top = 18
      Width = 64
      Height = 40
      Caption = 'Ekle'
      TabOrder = 5
      OnClick = UniButton1Click
    end
    object UniCheckBox1: TUniCheckBox
      Left = 343
      Top = 61
      Width = 256
      Height = 17
      Caption = 'Akreditasyon kapsam'#305'ndad'#305'r.'
      TabOrder = 6
    end
  end
  object UniPanel4: TUniPanel
    Left = 240
    Top = 95
    Width = 525
    Height = 267
    Visible = False
    TabOrder = 3
    BorderStyle = ubsNone
    object UniDBGrid1: TUniDBGrid
      AlignWithMargins = True
      Left = 424
      Top = 3
      Width = 98
      Height = 261
      HeaderTitle = 'Laboratuvar Birimleri'
      DataSource = dsNumuneBirimler
      Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgRowSelect, dgCheckSelect, dgConfirmDelete, dgMultiSelect, dgAutoRefreshRow, dgDontShowSelected]
      WebOptions.Paged = False
      WebOptions.FetchAll = True
      LoadMask.Message = 'Y'#252'kleniyor...'
      ForceFit = True
      Align = alClient
      TabOrder = 1
      OnDblClick = UniButton4Click
      Columns = <
        item
          FieldName = 'BIRIMADI'
          Title.Caption = 'Birimler'
          Width = 64
        end>
    end
    object UniPanel5: TUniPanel
      Left = 360
      Top = 0
      Width = 61
      Height = 267
      Visible = False
      Align = alLeft
      TabOrder = 2
      BorderStyle = ubsNone
      object UniButton4: TUniButton
        Left = 0
        Top = 141
        Width = 58
        Height = 43
        Caption = 'Ekle >'
        TabOrder = 1
        OnClick = UniButton4Click
      end
      object UniButton6: TUniButton
        Left = 0
        Top = 190
        Width = 58
        Height = 43
        Hint = 'Laboratuvar Birimini Siler'
        ShowHint = True
        ParentShowHint = False
        Caption = '< '#199#305'kar'
        TabOrder = 2
        OnClick = UniButton6Click
      end
    end
    object UniDBGrid2: TUniDBGrid
      AlignWithMargins = True
      Left = 3
      Top = 3
      Width = 354
      Height = 261
      Visible = False
      HeaderTitle = 'Atamas'#305' Yap'#305'lan Birimler'
      DataSource = dsAtamalar
      Options = [dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgConfirmDelete, dgAutoRefreshRow, dgRowNumbers]
      WebOptions.Paged = False
      WebOptions.FetchAll = True
      LoadMask.Message = 'Y'#252'kleniyor...'
      ForceFit = True
      Align = alLeft
      TabOrder = 3
      OnDblClick = UniButton6Click
      Columns = <
        item
          FieldName = 'BIRIMADI'
          Title.Caption = 'BIRIMADI'
          Width = 64
        end>
    end
  end
  object UniDBGrid3: TUniDBGrid
    AlignWithMargins = True
    Left = 8
    Top = 84
    Width = 689
    Height = 311
    HeaderTitle = 'Laboratuvar Birimleri'
    DataSource = dsListe
    Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgRowSelect, dgConfirmDelete, dgAutoRefreshRow, dgDontShowSelected]
    WebOptions.Paged = False
    WebOptions.FetchAll = True
    LoadMask.Message = 'Y'#252'kleniyor...'
    ForceFit = True
    TabOrder = 4
    OnDblClick = UniButton4Click
    Columns = <
      item
        FieldName = 'BIRIMADI'
        Title.Caption = 'Birimler'
        Width = 205
      end
      item
        FieldName = 'ETMEN_ADI'
        Title.Caption = 'Etmen Ad'#305
        Width = 306
      end>
  end
  object dsNumuneBirimler: TDataSource
    DataSet = tblNumuneBirimler
    Left = 184
    Top = 225
  end
  object tblAtamalar: TpFIBDataSet
    UpdateSQL.Strings = (
      'UPDATE LAB_ATAMALAR'
      'SET '
      '    NUMUNE_ID = :NUMUNE_ID,'
      '    BIRIM_ID = :BIRIM_ID,'
      '    ATAMA_TARIHI = :ATAMA_TARIHI,'
      '    ATAMA_SAATI = :ATAMA_SAATI,'
      '    ATAYAN_KULLANICI = :ATAYAN_KULLANICI'
      'WHERE'
      '    ID = :OLD_ID'
      '    ')
    DeleteSQL.Strings = (
      'DELETE FROM'
      '    LAB_ATAMALAR'
      'WHERE'
      '        ID = :OLD_ID'
      '    ')
    InsertSQL.Strings = (
      'INSERT INTO LAB_ATAMALAR('
      '    ID,'
      '    NUMUNE_ID,'
      '    BIRIM_ID,'
      '    ATAMA_TARIHI,'
      '    ATAMA_SAATI,'
      '    ATAYAN_KULLANICI'
      ')'
      'VALUES('
      '    :ID,'
      '    :NUMUNE_ID,'
      '    :BIRIM_ID,'
      '    :ATAMA_TARIHI,'
      '    :ATAMA_SAATI,'
      '    :ATAYAN_KULLANICI'
      ')')
    RefreshSQL.Strings = (
      'select LAB_ATAMALAR.*,lb.BIRIMADI from LAB_ATAMALAR'
      
        'left join LABORATUVAR_BIRIMLER lb on (lb.id= LAB_ATAMALAR.birim_' +
        'id)'
      ''
      'where'
      '     LAB_ATAMALAR.ID = :OLD_ID'
      '     '
      '    ')
    SelectSQL.Strings = (
      'select LAB_ATAMALAR.*,lb.BIRIMADI from LAB_ATAMALAR'
      
        'left join LABORATUVAR_BIRIMLER lb on (lb.id= LAB_ATAMALAR.birim_' +
        'id)'
      ''
      'where'
      ' numune_id in (:id)')
    AutoUpdateOptions.UpdateTableName = 'LAB_ATAMALAR'
    AutoUpdateOptions.KeyFields = 'ID'
    AutoUpdateOptions.GeneratorName = 'LAB_ATAMALAR_ID_GEN'
    AutoUpdateOptions.WhenGetGenID = wgOnNewRecord
    Transaction = UniMainModule.tr
    Database = UniMainModule.db
    Left = 448
    Top = 184
  end
  object dsAtamalar: TDataSource
    DataSet = tblAtamalar
    Left = 539
    Top = 184
  end
  object tblNumuneBirimler: TpFIBDataSet
    UpdateSQL.Strings = (
      'UPDATE LABORATUVAR_BIRIMLER'
      'SET '
      '    BIRIMADI = :BIRIMADI,'
      '    PERSONEL = :PERSONEL'
      'WHERE'
      '    ID = :OLD_ID'
      '    ')
    DeleteSQL.Strings = (
      'DELETE FROM'
      '    LABORATUVAR_BIRIMLER'
      'WHERE'
      '        ID = :OLD_ID'
      '    ')
    InsertSQL.Strings = (
      'INSERT INTO LABORATUVAR_BIRIMLER('
      '    ID,'
      '    BIRIMADI,'
      '    PERSONEL'
      ')'
      'VALUES('
      '    :ID,'
      '    :BIRIMADI,'
      '    :PERSONEL'
      ')')
    RefreshSQL.Strings = (
      'SELECT'
      '*'
      'FROM'
      '    LABORATUVAR_BIRIMLER '
      ''
      ' WHERE '
      '        LABORATUVAR_BIRIMLER.ID = :OLD_ID'
      '    ')
    SelectSQL.Strings = (
      'SELECT'
      '*'
      'FROM'
      '    LABORATUVAR_BIRIMLER ')
    AutoUpdateOptions.UpdateTableName = 'LABORATUVAR_BIRIMLER'
    AutoUpdateOptions.KeyFields = 'ID'
    AutoUpdateOptions.GeneratorName = 'GEN_LABORATUVAR_BIRIMLER_ID'
    AutoUpdateOptions.WhenGetGenID = wgOnNewRecord
    Transaction = UniMainModule.trtanim
    Database = UniMainModule.db
    Left = 80
    Top = 229
  end
  object tblTmp: TpFIBDataSet
    UpdateSQL.Strings = (
      'UPDATE LAB_SONUC_ETMENLER'
      'SET '
      '    SONUC_ID = :SONUC_ID,'
      '    ETMEN_ADI = :ETMEN_ADI,'
      '    NUMUNE_ID = :NUMUNE_ID,'
      '    BULASIKMI = :BULASIKMI'
      'WHERE'
      '    ID = :OLD_ID'
      '    ')
    DeleteSQL.Strings = (
      'DELETE FROM'
      '    LAB_SONUC_ETMENLER'
      'WHERE'
      '        ID = :OLD_ID'
      '    ')
    InsertSQL.Strings = (
      'INSERT INTO LAB_SONUC_ETMENLER('
      '    ID,'
      '    SONUC_ID,'
      '    ETMEN_ADI,'
      '    NUMUNE_ID,'
      '    BULASIKMI'
      ')'
      'VALUES('
      '    :ID,'
      '    :SONUC_ID,'
      '    :ETMEN_ADI,'
      '    :NUMUNE_ID,'
      '    :BULASIKMI'
      ')')
    RefreshSQL.Strings = (
      'SELECT'
      '    LAB_SONUC_ETMENLER.*'
      'FROM'
      'LAB_SONUC_ETMENLER'
      'where      LAB_SONUC_ETMENLER.ID = :OLD_ID'
      '      '
      '')
    SelectSQL.Strings = (
      'SELECT'
      '    LAB_SONUC_ETMENLER.*'
      'FROM'
      'LAB_SONUC_ETMENLER'
      'where sonuc_id=:id')
    AutoUpdateOptions.UpdateTableName = 'SERTIFIKA'
    AutoUpdateOptions.KeyFields = 'ID'
    AutoUpdateOptions.GeneratorName = 'GEN_SERTIFIKA_ID'
    Transaction = UniMainModule.tr
    Database = UniMainModule.db
    Left = 528
    Top = 256
  end
  object tblEtmenler: TpFIBDataSet
    UpdateSQL.Strings = (
      'UPDATE LAB_SONUC_ETMENLER'
      'SET '
      '    SONUC_ID = :SONUC_ID,'
      '    ETMEN_ADI = :ETMEN_ADI,'
      '    NUMUNE_ID = :NUMUNE_ID,'
      '    BULASIKMI = :BULASIKMI,'
      '    AKREDITE = :AKREDITE'
      'WHERE'
      '    ID = :OLD_ID'
      '    ')
    DeleteSQL.Strings = (
      'DELETE FROM'
      '    LAB_SONUC_ETMENLER'
      'WHERE'
      '        ID = :OLD_ID'
      '    ')
    InsertSQL.Strings = (
      'INSERT INTO LAB_SONUC_ETMENLER('
      '    ID,'
      '    SONUC_ID,'
      '    ETMEN_ADI,'
      '    NUMUNE_ID,'
      '    BULASIKMI,'
      '    AKREDITE'
      ')'
      'VALUES('
      '    :ID,'
      '    :SONUC_ID,'
      '    :ETMEN_ADI,'
      '    :NUMUNE_ID,'
      '    :BULASIKMI,'
      '    :AKREDITE'
      ')')
    RefreshSQL.Strings = (
      'SELECT'
      '    LAB_SONUC_ETMENLER.*'
      'FROM'
      'LAB_SONUC_ETMENLER'
      'where    LAB_SONUC_ETMENLER.ID = :OLD_ID'
      '   '
      '    ')
    SelectSQL.Strings = (
      'SELECT'
      '    LAB_SONUC_ETMENLER.*'
      'FROM'
      'LAB_SONUC_ETMENLER'
      'where sonuc_id=:id')
    AutoUpdateOptions.UpdateTableName = 'SERTIFIKA'
    AutoUpdateOptions.KeyFields = 'ID'
    AutoUpdateOptions.GeneratorName = 'GEN_SERTIFIKA_ID'
    AutoUpdateOptions.WhenGetGenID = wgOnNewRecord
    Transaction = UniMainModule.tr
    Database = UniMainModule.db
    Left = 528
    Top = 312
  end
  object dsEtmenler: TDataSource
    DataSet = tblEtmenler
    Left = 600
    Top = 312
  end
  object tblListe: TpFIBDataSet
    UpdateSQL.Strings = (
      'UPDATE LAB_SONUC_ETMENLER'
      'SET '
      '    SONUC_ID = :SONUC_ID,'
      '    ETMEN_ADI = :ETMEN_ADI,'
      '    NUMUNE_ID = :NUMUNE_ID,'
      '    BULASIKMI = :BULASIKMI,'
      '    AKREDITE = :AKREDITE'
      'WHERE'
      '    ID = :OLD_ID'
      '    ')
    DeleteSQL.Strings = (
      'DELETE FROM'
      '    LAB_SONUC_ETMENLER'
      'WHERE'
      '        ID = :OLD_ID'
      '    ')
    InsertSQL.Strings = (
      'INSERT INTO LAB_SONUC_ETMENLER('
      '    ID,'
      '    SONUC_ID,'
      '    ETMEN_ADI,'
      '    NUMUNE_ID,'
      '    BULASIKMI,'
      '    AKREDITE'
      ')'
      'VALUES('
      '    :ID,'
      '    :SONUC_ID,'
      '    :ETMEN_ADI,'
      '    :NUMUNE_ID,'
      '    :BULASIKMI,'
      '    :AKREDITE'
      ')')
    RefreshSQL.Strings = (
      'SELECT'
      '    LAB_SONUC_ETMENLER.*'
      'FROM'
      'LAB_SONUC_ETMENLER'
      'where    LAB_SONUC_ETMENLER.ID = :OLD_ID'
      '   '
      '    ')
    SelectSQL.Strings = (
      'select lb.BIRIMADI,ls.ETMEN_ADI from lab_atamalar la'
      'left join LAB_SONUC_ETMENLER ls on ls.NUMUNE_ID = la.NUMUNE_ID'
      'left join LABORATUVAR_BIRIMLER lb on (lb.id= la.birim_id)'
      'group by lb.BIRIMADI,ls.ETMEN_ADI'
      'order by lb.birimadi')
    AutoUpdateOptions.UpdateTableName = 'SERTIFIKA'
    AutoUpdateOptions.KeyFields = 'ID'
    AutoUpdateOptions.GeneratorName = 'GEN_SERTIFIKA_ID'
    AutoUpdateOptions.WhenGetGenID = wgOnNewRecord
    Transaction = UniMainModule.tr
    Database = UniMainModule.db
    Left = 256
    Top = 288
  end
  object dsListe: TDataSource
    DataSet = tblListe
    Left = 312
    Top = 288
  end
  object tblqry: TpFIBQuery
    Transaction = UniMainModule.tr
    Database = UniMainModule.db
    Left = 392
    Top = 336
  end
end

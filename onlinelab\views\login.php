<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Giriş - <?= APP_NAME ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .login-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 900px;
            width: 100%;
            margin: 20px;
        }
        
        .login-left {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 60px 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
        }
        
        .login-right {
            padding: 60px 40px;
        }
        
        .login-logo {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.9;
        }
        
        .login-title {
            font-size: 2.5rem;
            font-weight: 300;
            margin-bottom: 10px;
        }
        
        .login-subtitle {
            font-size: 1.1rem;
            opacity: 0.8;
            margin-bottom: 30px;
        }
        
        .form-floating {
            margin-bottom: 20px;
        }
        
        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 15px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn-login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 15px 30px;
            font-size: 1.1rem;
            font-weight: 600;
            color: white;
            width: 100%;
            transition: all 0.3s ease;
        }
        
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            color: white;
        }
        
        .alert {
            border: none;
            border-radius: 10px;
            padding: 15px 20px;
            margin-bottom: 20px;
        }
        
        .features {
            list-style: none;
            padding: 0;
            margin-top: 30px;
        }
        
        .features li {
            padding: 10px 0;
            border-bottom: 1px solid rgba(255,255,255,0.2);
        }
        
        .features li:last-child {
            border-bottom: none;
        }
        
        .features i {
            margin-right: 10px;
            width: 20px;
        }
        
        @media (max-width: 768px) {
            .login-left {
                padding: 40px 20px;
            }
            
            .login-right {
                padding: 40px 20px;
            }
            
            .login-title {
                font-size: 2rem;
            }
        }
        
        .version-info {
            position: absolute;
            bottom: 20px;
            right: 20px;
            color: rgba(255,255,255,0.7);
            font-size: 0.9rem;
        }
        
        .loading {
            display: none;
        }
        
        .spinner-border {
            width: 1.5rem;
            height: 1.5rem;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="row g-0">
            <!-- Sol taraf - Bilgi -->
            <div class="col-lg-6 login-left">
                <div class="login-logo">
                    <i class="fas fa-microscope"></i>
                </div>
                <h1 class="login-title">Laboratuvar</h1>
                <p class="login-subtitle">Yönetim Sistemi</p>
                
                <ul class="features">
                    <li><i class="fas fa-check"></i> Numune Takip Sistemi</li>
                    <li><i class="fas fa-check"></i> Analiz Sonuç Yönetimi</li>
                    <li><i class="fas fa-check"></i> Raporlama ve İstatistikler</li>
                    <li><i class="fas fa-check"></i> Kullanıcı Yetki Yönetimi</li>
                    <li><i class="fas fa-check"></i> Güvenli Veri Saklama</li>
                </ul>
            </div>
            
            <!-- Sağ taraf - Giriş formu -->
            <div class="col-lg-6 login-right">
                <div class="text-center mb-4">
                    <h2>Hoş Geldiniz</h2>
                    <p class="text-muted">Lütfen giriş bilgilerinizi giriniz</p>
                </div>
                
                <!-- Alert messages -->
                <?php if (isset($_SESSION['error'])): ?>
                    <div class="alert alert-danger" role="alert">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        <?= $_SESSION['error'] ?>
                    </div>
                    <?php unset($_SESSION['error']); ?>
                <?php endif; ?>
                
                <?php if (isset($_SESSION['success'])): ?>
                    <div class="alert alert-success" role="alert">
                        <i class="fas fa-check-circle me-2"></i>
                        <?= $_SESSION['success'] ?>
                    </div>
                    <?php unset($_SESSION['success']); ?>
                <?php endif; ?>
                
                <form method="POST" action="index.php?page=login&action=authenticate" id="loginForm">
                    <div class="form-floating">
                        <input type="text" class="form-control" id="username" name="username" 
                               placeholder="Kullanıcı Adı" required autofocus>
                        <label for="username">
                            <i class="fas fa-user me-2"></i>Kullanıcı Adı
                        </label>
                    </div>
                    
                    <div class="form-floating">
                        <input type="password" class="form-control" id="password" name="password" 
                               placeholder="Şifre" required>
                        <label for="password">
                            <i class="fas fa-lock me-2"></i>Şifre
                        </label>
                    </div>
                    
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="remember" name="remember">
                        <label class="form-check-label" for="remember">
                            Beni hatırla
                        </label>
                    </div>
                    
                    <button type="submit" class="btn btn-login" id="loginBtn">
                        <span class="login-text">
                            <i class="fas fa-sign-in-alt me-2"></i>Giriş Yap
                        </span>
                        <span class="loading">
                            <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                            Giriş yapılıyor...
                        </span>
                    </button>
                </form>
                
                <div class="text-center mt-4">
                    <small class="text-muted">
                        Şifrenizi mi unuttunuz? 
                        <a href="#" class="text-decoration-none">Yardım alın</a>
                    </small>
                </div>
                
                <div class="text-center mt-4">
                    <small class="text-muted">
                        <i class="fas fa-shield-alt me-1"></i>
                        Güvenli bağlantı ile korunmaktadır
                    </small>
                </div>
            </div>
        </div>
    </div>
    
    <div class="version-info">
        v<?= APP_VERSION ?>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Form submit animasyonu
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            const btn = document.getElementById('loginBtn');
            const loginText = btn.querySelector('.login-text');
            const loading = btn.querySelector('.loading');
            
            loginText.style.display = 'none';
            loading.style.display = 'inline';
            btn.disabled = true;
        });
        
        // Enter tuşu ile form submit
        document.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                document.getElementById('loginForm').submit();
            }
        });
        
        // Caps Lock uyarısı
        document.getElementById('password').addEventListener('keypress', function(e) {
            if (e.getModifierState && e.getModifierState('CapsLock')) {
                // Caps Lock açık uyarısı göster
                console.log('Caps Lock açık');
            }
        });
        
        // Otomatik odaklanma
        window.addEventListener('load', function() {
            document.getElementById('username').focus();
        });
    </script>
</body>
</html>

<?php
/**
 * Ürün Model Sınıfı
 */

class Urun extends BaseModel {
    protected $table = 'urunler';
    
    // Aktif ürünleri getir
    public function getActiveUrunler() {
        return $this->getWhere('aktif = 1', [], 'urunadi_t');
    }
    
    // Ürün listesi (say<PERSON>lama ile)
    public function getUrunList($page = 1, $search = '') {
        $where = '1=1';
        $params = [];
        
        if (!empty($search)) {
            $where .= ' AND urunadi_t LIKE ?';
            $params[] = "%$search%";
        }
        
        $urunler = $this->getPaginated($page, RECORDS_PER_PAGE, $where, $params, 'urunadi_t');
        $total = $this->getCount($where, $params);
        
        return [
            'urunler' => $urunler,
            'total' => $total,
            'pages' => ceil($total / RECORDS_PER_PAGE),
            'current_page' => $page
        ];
    }
    
    // Ürün kaydet
    public function saveUrun($data) {
        // Validation
        $rules = [
            'urunadi_t' => ['required' => true, 'max_length' => 255]
        ];
        
        $errors = $this->validate($data, $rules);
        
        // Ürün adı benzersizlik kontrolü
        if (!$this->isUrunAdiUnique($data['urunadi_t'], $data['id'] ?? null)) {
            $errors['urunadi_t'] = 'Bu ürün adı zaten kullanılmaktadır.';
        }
        
        if (!empty($errors)) {
            return ['success' => false, 'errors' => $errors];
        }
        
        try {
            if (isset($data['id']) && $data['id'] > 0) {
                // Güncelleme
                $id = $data['id'];
                unset($data['id']);
                $this->update($id, $data);
                return ['success' => true, 'id' => $id];
            } else {
                // Yeni kayıt
                unset($data['id']);
                $data['aktif'] = $data['aktif'] ?? 1;
                $id = $this->insert($data);
                return ['success' => true, 'id' => $id];
            }
        } catch (Exception $e) {
            return ['success' => false, 'error' => 'Kayıt sırasında hata oluştu.'];
        }
    }
    
    // Ürün adı benzersizlik kontrolü
    public function isUrunAdiUnique($urunAdi, $excludeId = null) {
        $where = 'urunadi_t = ?';
        $params = [$urunAdi];
        
        if ($excludeId) {
            $where .= ' AND id != ?';
            $params[] = $excludeId;
        }
        
        return !$this->exists($where, $params);
    }
    
    // Ürün durumunu değiştir (aktif/pasif)
    public function toggleStatus($id) {
        $urun = $this->getById($id);
        if ($urun) {
            $newStatus = $urun['aktif'] == 1 ? 0 : 1;
            return $this->update($id, ['aktif' => $newStatus]);
        }
        return false;
    }
    
    // Dropdown için ürün seçenekleri
    public function getOptionsForSelect() {
        $urunler = $this->getActiveUrunler();
        $options = [];
        
        foreach ($urunler as $urun) {
            $options[$urun['id']] = $urun['urunadi_t'];
        }
        
        return $options;
    }
    
    // Ürün kullanım istatistikleri
    public function getUsageStats($id) {
        $sql = "SELECT COUNT(*) as numune_sayisi 
                FROM lab_numuneler 
                WHERE urun_id = ? AND silindi = 0";
        
        $result = $this->db->fetch($sql, [$id]);
        return $result['numune_sayisi'] ?? 0;
    }
    
    // En çok kullanılan ürünler
    public function getMostUsedUrunler($limit = 10) {
        $sql = "SELECT u.*, COUNT(n.id) as kullanim_sayisi
                FROM {$this->table} u
                LEFT JOIN lab_numuneler n ON u.id = n.urun_id AND n.silindi = 0
                WHERE u.aktif = 1
                GROUP BY u.id
                ORDER BY kullanim_sayisi DESC, u.urunadi_t
                LIMIT ?";
        
        return $this->db->fetchAll($sql, [$limit]);
    }
}
?>

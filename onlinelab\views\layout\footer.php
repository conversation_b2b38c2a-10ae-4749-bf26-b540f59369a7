            </main>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Choices.js -->
    <script src="https://cdn.jsdelivr.net/npm/choices.js@10.2.0/public/assets/scripts/choices.min.js"></script>

    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    <script src="https://cdn.datatables.net/fixedcolumns/4.1.0/js/dataTables.fixedColumns.min.js"></script>

    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <!-- Custom JavaScript -->
    <script>
        // DataTables Türkçe dil ayarları
        $.extend(true, $.fn.dataTable.defaults, {
            language: {
                url: 'https://cdn.datatables.net/plug-ins/1.11.5/i18n/tr.json'
            },
            pageLength: <?= RECORDS_PER_PAGE ?>,
            responsive: true,
            order: [[0, 'desc']],
            columnDefs: [
                { orderable: false, targets: 'no-sort' }
            ]
        });
        
        // Otomatik DataTable başlatma
        $(document).ready(function() {
            $('.data-table').DataTable();
        });
        
        // Silme onayı
        function confirmDelete(url, message = 'Bu kaydı silmek istediğinizden emin misiniz?') {
            Swal.fire({
                title: 'Emin misiniz?',
                text: message,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#3085d6',
                confirmButtonText: 'Evet, sil!',
                cancelButtonText: 'İptal'
            }).then((result) => {
                if (result.isConfirmed) {
                    window.location.href = url;
                }
            });
        }
        
        // Form validasyonu
        function validateForm(formId) {
            const form = document.getElementById(formId);
            if (!form.checkValidity()) {
                form.classList.add('was-validated');
                return false;
            }
            return true;
        }
        
        // Loading göster/gizle
        function showLoading() {
            $('.loading').show();
        }
        
        function hideLoading() {
            $('.loading').hide();
        }
        
        // AJAX hata yönetimi
        $(document).ajaxError(function(event, xhr, settings, thrownError) {
            hideLoading();
            Swal.fire({
                title: 'Hata!',
                text: 'Bir hata oluştu. Lütfen tekrar deneyin.',
                icon: 'error'
            });
        });
        
        // Sayfa yüklenme animasyonu
        $(window).on('load', function() {
            $('.card').each(function(index) {
                $(this).delay(index * 100).animate({
                    opacity: 1
                }, 500);
            });
        });
        
        // Tooltip'leri başlat
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
        
        // Popover'ları başlat
        var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
        var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
            return new bootstrap.Popover(popoverTriggerEl);
        });
        
        // Tarih formatı yardımcısı
        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('tr-TR');
        }
        
        function formatDateTime(dateString) {
            const date = new Date(dateString);
            return date.toLocaleString('tr-TR');
        }
        
        // Numara formatı yardımcısı
        function formatNumber(number, decimals = 2) {
            return parseFloat(number).toLocaleString('tr-TR', {
                minimumFractionDigits: decimals,
                maximumFractionDigits: decimals
            });
        }
        
        // Durum badge renkleri
        function getStatusBadgeClass(status) {
            const statusColors = {
                'Numune Kabul': 'bg-primary',
                'Ön Hazırlık': 'bg-warning',
                'Analiz Aşamasında': 'bg-info',
                'Analiz Tamamlandı': 'bg-success',
                'Rapor Hazırlandı': 'bg-secondary',
                'Teslim Edildi': 'bg-dark'
            };
            return statusColors[status] || 'bg-secondary';
        }
        
        // Otomatik form kaydetme (draft)
        function autoSaveForm(formId, interval = 30000) {
            setInterval(function() {
                const formData = new FormData(document.getElementById(formId));
                formData.append('auto_save', '1');
                
                fetch('index.php?page=auto-save', {
                    method: 'POST',
                    body: formData
                }).then(response => {
                    if (response.ok) {
                        console.log('Form otomatik kaydedildi');
                    }
                }).catch(error => {
                    console.error('Otomatik kaydetme hatası:', error);
                });
            }, interval);
        }
        
        // Klavye kısayolları
        $(document).keydown(function(e) {
            // Ctrl+S ile kaydet
            if (e.ctrlKey && e.which === 83) {
                e.preventDefault();
                const saveButton = $('button[type="submit"], .btn-save').first();
                if (saveButton.length) {
                    saveButton.click();
                }
            }
            
            // Escape ile modal kapat
            if (e.which === 27) {
                $('.modal.show').modal('hide');
            }
        });
        
        // Responsive tablo yardımcısı
        function makeTableResponsive() {
            $('.table-responsive').each(function() {
                const table = $(this).find('table');
                if (table.width() > $(this).width()) {
                    $(this).addClass('table-scroll');
                }
            });
        }
        
        $(window).resize(makeTableResponsive);
        $(document).ready(makeTableResponsive);
        
        // Print fonksiyonu
        function printPage() {
            window.print();
        }
        
        // Export fonksiyonları
        function exportToExcel(tableId) {
            // Excel export implementasyonu
            console.log('Excel export:', tableId);
        }

        function exportToPDF(tableId) {
            // PDF export implementasyonu
            console.log('PDF export:', tableId);
        }

        // Sidebar Toggle Functionality
        $(document).ready(function() {
            const hamburgerMenu = $('#hamburgerMenu');
            const sidebar = $('#sidebar');
            const sidebarOverlay = $('#sidebarOverlay');

            // Hamburger menu click
            hamburgerMenu.on('click', function() {
                sidebar.toggleClass('show');
                sidebarOverlay.toggleClass('show');

                // Icon değiştir
                const icon = $(this).find('i');
                if (sidebar.hasClass('show')) {
                    icon.removeClass('fa-bars').addClass('fa-times');
                } else {
                    icon.removeClass('fa-times').addClass('fa-bars');
                }
            });

            // Overlay click - sidebar'ı kapat
            sidebarOverlay.on('click', function() {
                sidebar.removeClass('show');
                sidebarOverlay.removeClass('show');
                hamburgerMenu.find('i').removeClass('fa-times').addClass('fa-bars');
            });

            // Sidebar link click - mobile'da sidebar'ı kapat
            sidebar.find('.nav-link').on('click', function() {
                if (window.innerWidth < 992) {
                    sidebar.removeClass('show');
                    sidebarOverlay.removeClass('show');
                    hamburgerMenu.find('i').removeClass('fa-times').addClass('fa-bars');
                }
            });

            // Window resize - desktop'ta sidebar'ı göster
            $(window).on('resize', function() {
                if (window.innerWidth >= 992) {
                    sidebar.removeClass('show');
                    sidebarOverlay.removeClass('show');
                    hamburgerMenu.find('i').removeClass('fa-times').addClass('fa-bars');
                }
            });

            // ESC tuşu ile sidebar'ı kapat
            $(document).on('keydown', function(e) {
                if (e.key === 'Escape' && sidebar.hasClass('show')) {
                    sidebar.removeClass('show');
                    sidebarOverlay.removeClass('show');
                    hamburgerMenu.find('i').removeClass('fa-times').addClass('fa-bars');
                }
            });
        });
    </script>
    
    <!-- Sayfa özel JavaScript'leri -->
    <?php if (isset($customJS)): ?>
        <?= $customJS ?>
    <?php endif; ?>
    
    <footer class="text-center py-3 mt-5">
        <small class="text-muted">
            <?= APP_NAME ?> v<?= APP_VERSION ?> &copy; <?= date('Y') ?>
        </small>
    </footer>
</body>
</html>

<?php
/**
 * Veritabanı Test Sayfası
 * Veritabanı bağlantısını ve tabloları kontrol eder
 */

echo "<h2>🔧 Veritabanı Test ve Kurulum</h2>";

// Konfigürasyon dosyalarını yükle
if (!file_exists('config/config.php')) {
    echo "<p style='color: red;'>❌ config/config.php dosyası bulunamadı!</p>";
    exit;
}

require_once 'config/config.php';

echo "<h3>📋 Konfigürasyon Bilgileri</h3>";
echo "<ul>";
echo "<li><strong>Host:</strong> " . DB_HOST . "</li>";
echo "<li><strong>Database:</strong> " . DB_NAME . "</li>";
echo "<li><strong>User:</strong> " . DB_USER . "</li>";
echo "<li><strong>Charset:</strong> " . DB_CHARSET . "</li>";
echo "</ul>";

// Veritabanı bağlantısını test et
try {
    $dsn = "mysql:host=" . DB_HOST . ";charset=" . DB_CHARSET;
    $pdo = new PDO($dsn, DB_USER, DB_PASS, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
    ]);
    
    echo "<p style='color: green;'>✅ MySQL bağlantısı başarılı!</p>";
    
    // Veritabanının varlığını kontrol et
    $stmt = $pdo->query("SHOW DATABASES LIKE '" . DB_NAME . "'");
    $dbExists = $stmt->fetch();
    
    if (!$dbExists) {
        echo "<p style='color: orange;'>⚠️ Veritabanı '" . DB_NAME . "' bulunamadı. Oluşturuluyor...</p>";
        
        // Veritabanını oluştur
        $pdo->exec("CREATE DATABASE " . DB_NAME . " CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        echo "<p style='color: green;'>✅ Veritabanı oluşturuldu!</p>";
    } else {
        echo "<p style='color: green;'>✅ Veritabanı '" . DB_NAME . "' mevcut!</p>";
    }
    
    // Veritabanına bağlan
    $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
    $pdo = new PDO($dsn, DB_USER, DB_PASS, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
    ]);
    
    echo "<p style='color: green;'>✅ Veritabanına bağlantı başarılı!</p>";
    
    // Tabloları kontrol et
    $requiredTables = [
        'kullanicilar',
        'geneltanim', 
        'urunler',
        'laboratuvar_birimler',
        'lab_numuneler',
        'lab_atamalar',
        'lab_sonuc_etmenler',
        'lab_log',
        'parametreler'
    ];
    
    echo "<h3>📊 Tablo Kontrolü</h3>";
    echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse;'>";
    echo "<tr><th>Tablo Adı</th><th>Durum</th><th>Kayıt Sayısı</th></tr>";
    
    $missingTables = [];
    
    foreach ($requiredTables as $table) {
        try {
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM $table");
            $result = $stmt->fetch();
            $count = $result['count'];
            
            echo "<tr>";
            echo "<td>$table</td>";
            echo "<td style='color: green;'>✅ Mevcut</td>";
            echo "<td>$count</td>";
            echo "</tr>";
            
        } catch (PDOException $e) {
            echo "<tr>";
            echo "<td>$table</td>";
            echo "<td style='color: red;'>❌ Eksik</td>";
            echo "<td>-</td>";
            echo "</tr>";
            
            $missingTables[] = $table;
        }
    }
    
    echo "</table>";
    
    // Eksik tablolar varsa kurulum öner
    if (!empty($missingTables)) {
        echo "<h3>🔧 Eksik Tablolar</h3>";
        echo "<p style='color: orange;'>Aşağıdaki tablolar eksik:</p>";
        echo "<ul>";
        foreach ($missingTables as $table) {
            echo "<li>$table</li>";
        }
        echo "</ul>";
        
        echo "<p><strong>Çözüm:</strong></p>";
        echo "<ol>";
        echo "<li>phpMyAdmin'e gidin: <a href='http://localhost/phpmyadmin' target='_blank'>http://localhost/phpmyadmin</a></li>";
        echo "<li>'" . DB_NAME . "' veritabanını seçin</li>";
        echo "<li>SQL sekmesine gidin</li>";
        echo "<li>Aşağıdaki dosyayı içe aktarın veya içeriğini kopyalayıp yapıştırın:</li>";
        echo "<li><code>install/quick_setup.sql</code></li>";
        echo "</ol>";
        
        // Hızlı kurulum butonu
        echo "<hr>";
        echo "<h3>⚡ Hızlı Kurulum</h3>";
        echo "<form method='post'>";
        echo "<button type='submit' name='quick_setup' style='background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>";
        echo "🚀 Tabloları Otomatik Oluştur";
        echo "</button>";
        echo "</form>";
        
        // Hızlı kurulum işlemi
        if (isset($_POST['quick_setup'])) {
            echo "<h4>🔄 Kurulum İşlemi</h4>";
            
            try {
                $sqlFile = file_get_contents('install/quick_setup.sql');
                $statements = explode(';', $sqlFile);
                
                foreach ($statements as $statement) {
                    $statement = trim($statement);
                    if (!empty($statement)) {
                        $pdo->exec($statement);
                    }
                }
                
                echo "<p style='color: green; font-weight: bold;'>✅ Kurulum başarıyla tamamlandı!</p>";
                echo "<p><a href='test_db.php'>🔄 Sayfayı Yenile</a></p>";
                
            } catch (PDOException $e) {
                echo "<p style='color: red;'>❌ Kurulum hatası: " . $e->getMessage() . "</p>";
            }
        }
        
    } else {
        echo "<p style='color: green; font-weight: bold;'>✅ Tüm tablolar mevcut!</p>";
        
        // Admin kullanıcısını kontrol et
        echo "<h3>👤 Admin Kullanıcı Kontrolü</h3>";
        
        try {
            $stmt = $pdo->prepare("SELECT * FROM kullanicilar WHERE kullaniciadi = 'admin'");
            $stmt->execute();
            $admin = $stmt->fetch();
            
            if ($admin) {
                echo "<p style='color: green;'>✅ Admin kullanıcısı mevcut!</p>";
                echo "<ul>";
                echo "<li><strong>ID:</strong> " . $admin['id'] . "</li>";
                echo "<li><strong>Ad Soyad:</strong> " . $admin['adisoyadi'] . "</li>";
                echo "<li><strong>Aktif:</strong> " . ($admin['aktif'] ? 'Evet' : 'Hayır') . "</li>";
                echo "</ul>";
                
                // Şifre testi
                if (password_verify('admin123', $admin['sifresi'])) {
                    echo "<p style='color: green;'>✅ Admin şifresi doğru!</p>";
                } else {
                    echo "<p style='color: orange;'>⚠️ Admin şifresi yanlış, düzeltiliyor...</p>";
                    
                    $newPassword = password_hash('admin123', PASSWORD_DEFAULT);
                    $stmt = $pdo->prepare("UPDATE kullanicilar SET sifresi = ? WHERE id = ?");
                    $stmt->execute([$newPassword, $admin['id']]);
                    
                    echo "<p style='color: green;'>✅ Admin şifresi düzeltildi!</p>";
                }
                
                echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; border-left: 4px solid #28a745; margin: 20px 0;'>";
                echo "<h4>🎉 Sistem Hazır!</h4>";
                echo "<p>Artık sisteme giriş yapabilirsiniz:</p>";
                echo "<ul>";
                echo "<li><strong>URL:</strong> <a href='index.php'>index.php</a></li>";
                echo "<li><strong>Kullanıcı Adı:</strong> admin</li>";
                echo "<li><strong>Şifre:</strong> admin123</li>";
                echo "</ul>";
                echo "</div>";
                
            } else {
                echo "<p style='color: red;'>❌ Admin kullanıcısı bulunamadı!</p>";
                
                // Admin kullanıcısı oluştur
                echo "<form method='post'>";
                echo "<button type='submit' name='create_admin' style='background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>";
                echo "👤 Admin Kullanıcısı Oluştur";
                echo "</button>";
                echo "</form>";
                
                if (isset($_POST['create_admin'])) {
                    $hashedPassword = password_hash('admin123', PASSWORD_DEFAULT);
                    
                    $stmt = $pdo->prepare("INSERT INTO kullanicilar (
                        kullaniciadi, sifresi, adisoyadi,
                        NUMUNE_KABUL_YAPABILIR, NUMUNE_LISTESI_GOREBILIR,
                        NUMUNE_ONHAZIRLIK_YAPABILIR, NUMUNE_SONUC_GIREBILIR,
                        TANIMLAMA_YAPABILIR, NUMUNE_GENEL_GORUNUM, NUMUNE_ATAMA_YAPABILIR
                    ) VALUES (
                        'admin', ?, 'Sistem Yöneticisi',
                        1, 1, 1, 1, 1, 1, 1
                    )");
                    
                    $stmt->execute([$hashedPassword]);
                    
                    echo "<p style='color: green;'>✅ Admin kullanıcısı oluşturuldu!</p>";
                    echo "<p><a href='test_db.php'>🔄 Sayfayı Yenile</a></p>";
                }
            }
            
        } catch (PDOException $e) {
            echo "<p style='color: red;'>❌ Admin kontrolü hatası: " . $e->getMessage() . "</p>";
        }
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Veritabanı bağlantı hatası: " . $e->getMessage() . "</p>";
    
    echo "<h3>🔧 Kontrol Edilecekler:</h3>";
    echo "<ol>";
    echo "<li>XAMPP Control Panel'de MySQL servisi çalışıyor mu?</li>";
    echo "<li>MySQL portu (3306) açık mı?</li>";
    echo "<li>config/config.php dosyasındaki ayarlar doğru mu?</li>";
    echo "</ol>";
}

echo "<hr>";
echo "<p><a href='index.php'>← Ana Sayfaya Dön</a></p>";
?>

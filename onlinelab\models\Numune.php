<?php
/**
 * Numune Model Sınıfı
 */

class Numune extends BaseModel {
    protected $table = 'lab_numuneler';
    
    // Numune listesi (detaylı bilgilerle)
    public function getNumuneList($page = 1, $filters = []) {
        $where = 'n.SILINDI = 0';
        $params = [];

        // Filtreleme
        if (!empty($filters['numune_turu'])) {
            $where .= ' AND n.NUMUNE_TURU = ?';
            $params[] = $filters['numune_turu'];
        }

        if (!empty($filters['durumu'])) {
            $where .= ' AND n.DURUMU = ?';
            $params[] = $filters['durumu'];
        }

        if (!empty($filters['lab_kayitno'])) {
            $where .= ' AND n.LAB_KAYITNO LIKE ?';
            $params[] = '%' . $filters['lab_kayitno'] . '%';
        }

        if (!empty($filters['gonderen'])) {
            $where .= ' AND n.GONDEREN LIKE ?';
            $params[] = '%' . $filters['gonderen'] . '%';
        }

        if (!empty($filters['baslangic_tarihi'])) {
            $where .= ' AND DATE(n.GELIS_TARIHI) >= ?';
            $params[] = $filters['baslangic_tarihi'];
        }

        if (!empty($filters['bitis_tarihi'])) {
            $where .= ' AND DATE(n.GELIS_TARIHI) <= ?';
            $params[] = $filters['bitis_tarihi'];
        }

        // Önce sayıyı al
        $countSql = "SELECT COUNT(*) as count FROM {$this->table} n WHERE $where";
        $countResult = $this->db->fetch($countSql, $params);
        $total = $countResult['count'] ?? 0;

        // Sonra verileri al - tüm alanları açık şekilde seç
        $sql = "SELECT n.id,
                       n.LAB_KAYITNO,
                       n.NUMUNE_TURU,
                       n.NUMUNE_ALT_TURU,
                       n.GONDEREN,
                       n.GONDEREN_PERSONEL,
                       n.FIRMA_ID,
                       n.URUN_ID,
                       n.MENSEI,
                       n.ULKE,
                       n.NUMUNE_ALINDIGIYER,
                       n.NUMUNE_SAHIBI,
                       n.BASVURU_NO,
                       n.MUHUR_NO,
                       n.ETIKET_NO,
                       n.BARKOD,
                       n.LOTNO,
                       n.URUN_MIKTARI,
                       n.MIKTAR_BIRIM,
                       n.GELIS_TARIHI,
                       n.GELIS_SAATI,
                       n.TESLIM_EDEN,
                       n.TESLIM_ALAN,
                       n.UCRET_DURUMU,
                       n.DURUMU,
                       n.ONHAZIRLIK,
                       n.SILINDI,
                       n.ACIKLAMALAR,
                       n.ANALIZ_SONUCU,
                       n.KULLANILAN_NUMUNE,
                       n.MENSEI_2,
                       u.urunadi_t as urun_adi,
                       g.izahat as firma_adi,
                       n.NUMUNE_TURU as numune_turu,
                       n.GONDEREN as gonderen,
                       n.URUN_MIKTARI as miktar,
                       n.MIKTAR_BIRIM as birim,
                       n.GELIS_TARIHI as gelis_tarihi,
                       n.DURUMU as durumu
                FROM {$this->table} n
                LEFT JOIN urunler u ON n.URUN_ID = u.id
                LEFT JOIN geneltanim g ON n.FIRMA_ID = g.id
                WHERE $where
                ORDER BY n.GELIS_TARIHI DESC";

        $numuneler = $this->db->getPaginated($sql, $params, $page, RECORDS_PER_PAGE);

        // Her numune için atanan birimleri ayrı olarak çek
        foreach ($numuneler as &$numune) {
            $numune['atanan_birimler'] = $this->getAtananBirimler($numune['id']);
        }

        return [
            'numuneler' => $numuneler,
            'total' => $total,
            'pages' => ceil($total / RECORDS_PER_PAGE),
            'current_page' => $page
        ];
    }

    // Numuneye atanan birimleri getir
    private function getAtananBirimler($numuneId) {
        $sql = "SELECT GROUP_CONCAT(DISTINCT b.birimadi SEPARATOR ', ') as birimler
                FROM lab_atamalar a
                LEFT JOIN laboratuvar_birimler b ON a.birim_id = b.id
                WHERE a.numune_id = ?";

        $result = $this->db->fetch($sql, [$numuneId]);
        return $result['birimler'] ?? '';
    }

    // Numune detayları
    public function getNumuneDetails($id) {
        $sql = "SELECT n.*,
                       u.urunadi_t as urun_adi,
                       g.izahat as firma_adi,
                       n.NUMUNE_TURU as numune_turu,
                       n.NUMUNE_ALT_TURU as numune_alt_turu,
                       n.GONDEREN as gonderen,
                       n.GONDEREN_PERSONEL as gonderen_personel,
                       n.FIRMA_ID as firma_id,
                       n.URUN_ID as urun_id,
                       n.MENSEI as mensei,
                       n.ULKE as ulke,
                       n.NUMUNE_ALINDIGIYER as numune_alindigiyer,
                       n.NUMUNE_SAHIBI as numune_sahibi,
                       n.BASVURU_NO as basvuru_no,
                       n.MUHUR_NO as muhur_no,
                       n.ETIKET_NO as etiket_no,
                       n.BARKOD as barkod,
                       n.LOTNO as lotno,
                       n.URUN_MIKTARI as miktar,
                       n.MIKTAR_BIRIM as birim,
                       n.ACIKLAMALAR as aciklama,
                       n.DURUMU as durumu
                FROM {$this->table} n
                LEFT JOIN urunler u ON n.URUN_ID = u.id
                LEFT JOIN geneltanim g ON n.FIRMA_ID = g.id
                WHERE n.id = ?";

        return $this->db->fetch($sql, [$id]);
    }
    
    // Yeni laboratuvar kayıt numarası oluştur
    public function generateLabKayitNo($numuneTuru) {
        $paramTable = 'parametreler';
        $field = $numuneTuru === 'İç Karantina' ? 'LAB_Ic_NO' : 'LAB_dis_NO';
        $prefix = $numuneTuru === 'İç Karantina' ? 'İÇ' : 'DIŞ';
        
        // Mevcut numarayı al
        $sql = "SELECT $field FROM $paramTable LIMIT 1";
        $result = $this->db->fetch($sql);
        $currentNo = $result[$field] ?? 1;
        
        // Yeni numara oluştur
        $year = date('Y');
        $labKayitNo = "$prefix-$year-" . str_pad($currentNo, 4, '0', STR_PAD_LEFT);
        
        // Parametreyi güncelle
        $sql = "UPDATE $paramTable SET $field = $field + 1";
        $this->db->execute($sql);
        
        return $labKayitNo;
    }
    
    // Numune kaydet
    public function saveNumune($data) {
        // Validation
        $rules = [
            'NUMUNE_TURU' => ['required' => true],
            'GONDEREN' => ['required' => true, 'max_length' => 150],
            'URUN_ID' => ['required' => true],
            'URUN_MIKTARI' => ['required' => true],
            'MIKTAR_BIRIM' => ['required' => true, 'max_length' => 30]
        ];
        
        $errors = $this->validate($data, $rules);
        
        if (!empty($errors)) {
            return ['success' => false, 'errors' => $errors];
        }
        
        try {
            $this->db->beginTransaction();
            
            if (isset($data['id']) && $data['id'] > 0) {
                // Güncelleme
                $id = $data['id'];
                unset($data['id']);
                $this->update($id, $data);
                
                // Log kaydı
                $this->addLog($id, 'Numune güncellendi', $_SESSION['user_name'] ?? 'Sistem');
            } else {
                // Yeni kayıt
                unset($data['id']);

                // Lab kayıt numarası oluştur
                if (empty($data['LAB_KAYITNO'])) {
                    $data['LAB_KAYITNO'] = $this->generateLabKayitNo($data['NUMUNE_TURU']);
                }

                $data['GELIS_TARIHI'] = date('Y-m-d H:i:s');
                $data['DURUMU'] = 'Numune Kabul';

                // Önce tablodaki alanları kontrol et
                $sql = "DESCRIBE {$this->table}";
                $tableColumns = $this->db->fetchAll($sql);
                $validFields = array_column($tableColumns, 'Field');

                // Alan adı eşleştirme tablosu (form_field => db_field)
                $fieldMapping = [
                    'NUMUNE_TURU' => 'numune_turu',
                    'NUMUNE_ALT_TURU' => 'numune_alt_turu',
                    'GONDEREN' => 'gonderen',
                    'GONDEREN_PERSONEL' => 'gonderen_personel',
                    'FIRMA_ID' => 'firma_id',
                    'URUN_ID' => 'urun_id',
                    'MENSEI' => 'mensei',
                    'ULKE' => 'ULKE',
                    'BASVURU_NO' => 'basvuru_no',
                    'MUHUR_NO' => 'muhur_no',
                    'ETIKET_NO' => 'etiket_no',
                    'BARKOD' => 'barkod',
                    'URUN_MIKTARI' => 'URUN_MIKTARI',
                    'MIKTAR_BIRIM' => 'MIKTAR_BIRIM',
                    'UCRET_DURUMU' => 'ucret_durumu',
                    'ACIKLAMALAR' => 'ACIKLAMALAR',
                    'LAB_KAYITNO' => 'LAB_KAYITNO',
                    'NUMUNE_ALINDIGIYER' => 'NUMUNE_ALINDIGIYER',
                    'NUMUNE_SAHIBI' => 'NUMUNE_SAHIBI',
                    'LOTNO' => 'LOTNO',
                    'TESLIM_EDEN' => 'TESLIM_EDEN',
                    'TESLIM_ALAN' => 'TESLIM_ALAN',
                    'GELIS_SAATI' => 'GELIS_SAATI',
                    'GELIS_TARIHI' => 'gelis_tarihi',
                    'DURUMU' => 'durumu'
                ];

                // Sadece tabloda olan alanları kullan
                $cleanData = [];
                foreach ($data as $key => $value) {
                    // Alan adını eşleştir
                    $dbField = isset($fieldMapping[$key]) ? $fieldMapping[$key] : $key;

                    if (in_array($dbField, $validFields)) {
                        // Boş string'leri null'a çevir (foreign key alanları için)
                        if ($value === '' && in_array($key, ['FIRMA_ID', 'URUN_ID'])) {
                            $cleanData[$dbField] = null;
                        } elseif ($value === '' && !in_array($key, ['NUMUNE_TURU', 'GONDEREN', 'URUN_ID', 'URUN_MIKTARI', 'MIKTAR_BIRIM', 'LAB_KAYITNO'])) {
                            // Zorunlu olmayan alanlar için boş string'leri null yap
                            $cleanData[$dbField] = null;
                        } else {
                            $cleanData[$dbField] = $value;
                        }
                    }
                }

                // Debug: Temizlenmiş veriyi logla
                error_log("Temizlenmiş veri: " . json_encode($cleanData));
                error_log("Tablo alanları: " . json_encode($validFields));

                $id = $this->insert($cleanData);

                // Log kaydı
                $this->addLog($id, 'Numune kabul edildi', $_SESSION['user_name'] ?? 'Sistem');
            }
            
            $this->db->commit();
            return ['success' => true, 'id' => $id];
            
        } catch (Exception $e) {
            $this->db->rollback();
            return [
                'success' => false,
                'error' => 'Kayıt sırasında hata oluştu: ' . $e->getMessage(),
                'sql_error' => $e->getMessage(),
                'debug_data' => $data
            ];
        }
    }
    
    // Numune durumunu güncelle
    public function updateDurum($id, $durum, $aciklama = '') {
        try {
            $this->db->beginTransaction();
            
            $oldData = $this->getById($id);
            $this->update($id, ['durumu' => $durum]);
            
            // Log kaydı
            $logMessage = "Durum değiştirildi: {$oldData['durumu']} → $durum";
            if ($aciklama) {
                $logMessage .= " ($aciklama)";
            }
            $this->addLog($id, $logMessage, $_SESSION['user_name'] ?? 'Sistem');
            
            $this->db->commit();
            return true;
            
        } catch (Exception $e) {
            $this->db->rollback();
            return false;
        }
    }
    
    // Numune atamalarını getir
    public function getAtamalar($numuneId) {
        $sql = "SELECT a.*, 
                       b.birimadi,
                       k.adisoyadi as atayan_adi
                FROM lab_atamalar a
                LEFT JOIN laboratuvar_birimler b ON a.birim_id = b.id
                LEFT JOIN kullanicilar k ON a.atayan_kullanici = k.id
                WHERE a.numune_id = ?
                ORDER BY a.atama_tarihi";
        
        return $this->db->fetchAll($sql, [$numuneId]);
    }
    
    // Birim atama
    public function assignToBirim($numuneId, $birimId, $atayanKullanici) {
        try {
            $this->db->beginTransaction();
            
            // Atama kaydı
            $atama = [
                'numune_id' => $numuneId,
                'birim_id' => $birimId,
                'atayan_kullanici' => $atayanKullanici,
                'atama_tarihi' => date('Y-m-d H:i:s')
            ];
            
            $sql = "INSERT INTO lab_atamalar (numune_id, birim_id, atayan_kullanici, atama_tarihi) 
                    VALUES (?, ?, ?, ?)";
            $this->db->execute($sql, array_values($atama));
            
            // Numune durumunu güncelle
            $this->update($numuneId, ['durumu' => 'Ön Hazırlık']);
            
            // Log kaydı
            $birimSql = "SELECT birimadi FROM laboratuvar_birimler WHERE id = ?";
            $birim = $this->db->fetch($birimSql, [$birimId]);
            $this->addLog($numuneId, "Birime atandı: " . $birim['birimadi'], $_SESSION['user_name'] ?? 'Sistem');
            
            $this->db->commit();
            return true;
            
        } catch (Exception $e) {
            $this->db->rollback();
            return false;
        }
    }
    
    // Log kaydı ekle
    public function addLog($numuneId, $islem, $islemYapan, $oncekiDeger = '') {
        $sql = "INSERT INTO lab_log (numune_id, islem, islemi_yapan, onceki_deger, tarih) 
                VALUES (?, ?, ?, ?, ?)";
        $this->db->execute($sql, [$numuneId, $islem, $islemYapan, $oncekiDeger, date('Y-m-d H:i:s')]);
    }
    
    // Dashboard için istatistikler
    public function getDashboardStats() {
        $stats = [];

        // Toplam numune sayısı
        $stats['toplam_numune'] = $this->getCount('SILINDI = 0');

        // Bu ay gelen numuneler
        $stats['bu_ay'] = $this->getCount('SILINDI = 0 AND MONTH(GELIS_TARIHI) = MONTH(CURRENT_DATE()) AND YEAR(GELIS_TARIHI) = YEAR(CURRENT_DATE())');

        // Duruma göre dağılım
        foreach (NUMUNE_DURUMLARI as $durum) {
            $count = $this->getCount('SILINDI = 0 AND DURUMU = ?', [$durum]);
            $stats['durum'][$durum] = $count;
        }

        // Türe göre dağılım
        foreach (NUMUNE_TURLERI as $tur) {
            $count = $this->getCount('SILINDI = 0 AND NUMUNE_TURU = ?', [$tur]);
            $stats['tur'][$tur] = $count;
        }

        return $stats;
    }
}
?>

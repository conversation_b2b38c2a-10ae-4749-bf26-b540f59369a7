<?php
/**
 * <PERSON><PERSON><PERSON> Listesi Sayfası
 */

require_once 'views/layout/header.php';

$numuneler = $data['numuneler'] ?? [];
$pagination = $data['pagination'] ?? [];
$birimler = $data['birimler'] ?? [];
$filters = $data['filters'] ?? [];
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="row align-items-center">
                        <div class="col">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-flask me-2"></i>Sonuç Yönetimi
                            </h5>
                            <small class="text-muted">Ana<PERSON>z aşamasındaki ve tamamlanan numunelerin sonuç yönetimi</small>
                        </div>
                        <div class="col-auto">
                            <div class="btn-group">
                                <button type="button" class="btn btn-outline-primary" onclick="refreshPage()">
                                    <i class="fas fa-sync-alt me-2"></i>Yenile
                                </button>

                                <button type="button" class="btn btn-outline-primary" data-bs-toggle="collapse" data-bs-target="#filterCollapse">
                                    <i class="fas fa-filter me-2"></i>Filtrele
                                </button>

                                <button type="button" class="btn btn-success" id="topluKabulBtn" onclick="topluAnalizKabul()" style="display: none;">
                                    <i class="fas fa-check me-2"></i>Kabul Et
                                </button>

                                <button type="button" class="btn btn-primary" id="topluTamamlaBtn" onclick="topluAnalizTamamla()" style="display: none;">
                                    <i class="fas fa-check-double me-2"></i>Tamamla
                                </button>

                                <a href="index.php?page=sonuc-listesi&action=export<?= !empty($_SERVER['QUERY_STRING']) ? '&' . $_SERVER['QUERY_STRING'] : '' ?>"
                                   class="btn btn-outline-success">
                                    <i class="fas fa-file-excel me-2"></i>Excel'e Aktar
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Filtre Alanı -->
                <div class="collapse" id="filterCollapse">
                    <div class="card-body border-bottom">
                        <form method="GET" action="index.php">
                            <input type="hidden" name="page" value="sonuc-listesi">
                            
                            <div class="row g-3">
                                <div class="col-md-3">
                                    <label for="lab_kayit_no" class="form-label">Lab Kayıt No</label>
                                    <input type="text" class="form-control" id="lab_kayit_no" name="lab_kayit_no" 
                                           value="<?= safe_html($filters['lab_kayit_no'] ?? '') ?>" 
                                           placeholder="Lab kayıt no ara...">
                                </div>
                                
                                <div class="col-md-3">
                                    <label for="numune_turu" class="form-label">Numune Türü</label>
                                    <select class="form-select" id="numune_turu" name="numune_turu">
                                        <option value="">Tümü</option>
                                        <option value="İç Karantina" <?= safe_selected($filters['numune_turu'] ?? '', 'İç Karantina') ?>>İç Karantina</option>
                                        <option value="Dış Karantina" <?= safe_selected($filters['numune_turu'] ?? '', 'Dış Karantina') ?>>Dış Karantina</option>
                                    </select>
                                </div>
                                
                                <div class="col-md-3">
                                    <label for="birim_id" class="form-label">Laboratuvar Birimi</label>
                                    <select class="form-select" id="birim_id" name="birim_id">
                                        <option value="">Tümü</option>
                                        <?php foreach ($birimler as $birim): ?>
                                            <option value="<?= $birim['id'] ?>" <?= safe_selected($filters['birim_id'] ?? '', $birim['id']) ?>>
                                                <?= safe_html($birim['birimadi']) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <div class="col-md-3">
                                    <label for="baslangic_tarihi" class="form-label">Başlangıç Tarihi</label>
                                    <input type="date" class="form-control" id="baslangic_tarihi" name="baslangic_tarihi" 
                                           value="<?= safe_html($filters['baslangic_tarihi'] ?? '') ?>">
                                </div>
                                
                                <div class="col-md-3">
                                    <label for="bitis_tarihi" class="form-label">Bitiş Tarihi</label>
                                    <input type="date" class="form-control" id="bitis_tarihi" name="bitis_tarihi" 
                                           value="<?= safe_html($filters['bitis_tarihi'] ?? '') ?>">
                                </div>
                                
                                <div class="col-md-9">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="d-flex gap-2">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-search me-2"></i>Filtrele
                                        </button>
                                        <a href="index.php?page=sonuc-listesi" class="btn btn-outline-secondary">
                                            <i class="fas fa-times me-2"></i>Temizle
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                
                <div class="card-body">
                    <?php if (empty($numuneler)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-search fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">Sonuç Bulunamadı</h5>
                            <p class="text-muted">Arama kriterlerinize uygun sonuç bulunmamaktadır.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover" id="sonucTable">
                                <thead>
                                    <tr style="background: #f8f9fa !important; background-image: none !important; color: #000 !important;">
                                        <th width="40" style="background: #f8f9fa !important; background-image: none !important; color: #000 !important; font-weight: 600;">
                                            <input type="checkbox" id="selectAll" class="form-check-input">
                                        </th>
                                        <th style="background: #f8f9fa !important; background-image: none !important; color: #000 !important; font-weight: 600;">Lab Kayıt No</th>
                                        <th style="background: #f8f9fa !important; background-image: none !important; color: #000 !important; font-weight: 600;">Numune Türü</th>
                                        <th style="background: #f8f9fa !important; background-image: none !important; color: #000 !important; font-weight: 600;">Gönderen</th>
                                        <th style="background: #f8f9fa !important; background-image: none !important; color: #000 !important; font-weight: 600;">Ürün</th>
                                        <th style="background: #f8f9fa !important; background-image: none !important; color: #000 !important; font-weight: 600;">Birim</th>
                                        <th style="background: #f8f9fa !important; background-image: none !important; color: #000 !important; font-weight: 600;">Analiz Durumu</th>
                                        <th style="background: #f8f9fa !important; background-image: none !important; color: #000 !important; font-weight: 600;">Tarihler</th>
                                        <th style="background: #f8f9fa !important; background-image: none !important; color: #000 !important; font-weight: 600;">Etmen Sayısı</th>
                                        <th width="200" style="background: #f8f9fa !important; background-image: none !important; color: #000 !important; font-weight: 600;">İşlemler</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($numuneler as $numune): ?>
                                        <tr>
                                            <td>
                                                <input type="checkbox" name="atama_ids[]" value="<?= $numune['atama_id'] ?>"
                                                       class="form-check-input numune-checkbox"
                                                       data-durum="<?= $numune['atama_durum'] ?>">
                                            </td>
                                            <td>
                                                <strong><?= safe_html($numune['LAB_KAYITNO']) ?></strong>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?= $numune['numune_turu'] === 'İç Karantina' ? 'primary' : 'info' ?>">
                                                    <?= safe_html($numune['numune_turu']) ?>
                                                </span>
                                            </td>
                                            <td><?= safe_html($numune['gonderen']) ?></td>
                                            <td><?= safe_html($numune['urun_adi']) ?></td>
                                            <td>
                                                <span class="badge bg-secondary"><?= safe_html($numune['birimadi']) ?></span>
                                            </td>
                                            <td>
                                                <?php
                                                $durumClass = match($numune['atama_durum']) {
                                                    'Hazırlık Yapıldı' => 'bg-warning',
                                                    'Analiz Kabul Edildi' => 'bg-info',
                                                    'Analiz Tamamlandı' => 'bg-success',
                                                    default => 'bg-secondary'
                                                };
                                                ?>
                                                <span class="badge <?= $durumClass ?>"><?= safe_html($numune['atama_durum']) ?></span>
                                            </td>
                                            <td>
                                                <div class="d-flex flex-column gap-1">
                                                    <?php if ($numune['hazirlik_tarihi']): ?>
                                                        <small class="text-muted">
                                                            <i class="fas fa-cogs me-1"></i>
                                                            Hazırlık: <?= date('d.m.Y H:i', strtotime($numune['hazirlik_tarihi'])) ?>
                                                        </small>
                                                    <?php endif; ?>
                                                    <?php if ($numune['analiz_kabul_tarihi']): ?>
                                                        <small class="text-info">
                                                            <i class="fas fa-check me-1"></i>
                                                            Kabul: <?= date('d.m.Y H:i', strtotime($numune['analiz_kabul_tarihi'])) ?>
                                                        </small>
                                                    <?php endif; ?>
                                                    <?php if ($numune['analiz_tarihi']): ?>
                                                        <small class="text-success">
                                                            <i class="fas fa-flask me-1"></i>
                                                            Analiz: <?= date('d.m.Y H:i', strtotime($numune['analiz_tarihi'])) ?>
                                                        </small>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-info"><?= $numune['etmen_sayisi'] ?> etmen</span>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <?php if ($numune['atama_durum'] === 'Hazırlık Yapıldı'): ?>
                                                        <button type="button" class="btn btn-success btn-sm"
                                                                onclick="analizKabul([<?= $numune['atama_id'] ?>])">
                                                            <i class="fas fa-check"></i> Kabul Et
                                                        </button>
                                                    <?php elseif ($numune['atama_durum'] === 'Analiz Kabul Edildi'): ?>
                                                        <a href="index.php?page=sonuc-listesi&action=gir&id=<?= $numune['id'] ?>"
                                                           class="btn btn-primary btn-sm">
                                                            <i class="fas fa-flask"></i> Sonuç Gir
                                                        </a>
                                                        <button type="button" class="btn btn-success btn-sm"
                                                                onclick="analizTamamla([<?= $numune['atama_id'] ?>])">
                                                            <i class="fas fa-check-double"></i> Tamamla
                                                        </button>
                                                    <?php elseif ($numune['atama_durum'] === 'Analiz Tamamlandı'): ?>
                                                        <a href="index.php?page=sonuc-listesi&action=gir&id=<?= $numune['id'] ?>"
                                                           class="btn btn-outline-primary btn-sm">
                                                            <i class="fas fa-eye"></i> Sonuçları Gör
                                                        </a>
                                                        <a href="index.php?page=sonuc-listesi&action=detay&id=<?= $numune['id'] ?>"
                                                           class="btn btn-outline-info btn-sm">
                                                            <i class="fas fa-info"></i> Detay
                                                        </a>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- Pagination -->
                        <?php if (($pagination['pages'] ?? 0) > 1): ?>
                            <nav aria-label="Sayfa navigasyonu">
                                <ul class="pagination justify-content-center">
                                    <?php
                                    $currentPage = $pagination['current_page'] ?? 1;
                                    $totalPages = $pagination['pages'] ?? 1;
                                    $queryString = http_build_query(array_merge($_GET, ['page' => 'sonuc-listesi']));
                                    ?>
                                    
                                    <li class="page-item <?= $currentPage <= 1 ? 'disabled' : '' ?>">
                                        <a class="page-link" href="index.php?<?= $queryString ?>&page_num=1">
                                            <i class="fas fa-angle-double-left"></i>
                                        </a>
                                    </li>
                                    
                                    <li class="page-item <?= $currentPage <= 1 ? 'disabled' : '' ?>">
                                        <a class="page-link" href="index.php?<?= $queryString ?>&page_num=<?= $currentPage - 1 ?>">
                                            <i class="fas fa-angle-left"></i>
                                        </a>
                                    </li>
                                    
                                    <?php
                                    $start = max(1, $currentPage - 2);
                                    $end = min($totalPages, $currentPage + 2);
                                    
                                    for ($i = $start; $i <= $end; $i++):
                                    ?>
                                        <li class="page-item <?= $i == $currentPage ? 'active' : '' ?>">
                                            <a class="page-link" href="index.php?<?= $queryString ?>&page_num=<?= $i ?>">
                                                <?= $i ?>
                                            </a>
                                        </li>
                                    <?php endfor; ?>
                                    
                                    <li class="page-item <?= $currentPage >= $totalPages ? 'disabled' : '' ?>">
                                        <a class="page-link" href="index.php?<?= $queryString ?>&page_num=<?= $currentPage + 1 ?>">
                                            <i class="fas fa-angle-right"></i>
                                        </a>
                                    </li>
                                    
                                    <li class="page-item <?= $currentPage >= $totalPages ? 'disabled' : '' ?>">
                                        <a class="page-link" href="index.php?<?= $queryString ?>&page_num=<?= $totalPages ?>">
                                            <i class="fas fa-angle-double-right"></i>
                                        </a>
                                    </li>
                                </ul>
                            </nav>
                        <?php endif; ?>
                        
                        <div class="mt-3">
                            <small class="text-muted">
                                Toplam <?= number_format($pagination['total'] ?? 0) ?> sonuç bulundu.
                                Sayfa <?= $pagination['current_page'] ?? 1 ?> / <?= $pagination['pages'] ?? 1 ?>
                            </small>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Tümünü seç/seçme
    $('#selectAll').change(function() {
        $('.numune-checkbox').prop('checked', this.checked);
        updateSelectedCount();
    });

    // Tekil checkbox değişikliği
    $('.numune-checkbox').change(function() {
        updateSelectedCount();

        // Tümü seçili mi kontrol et
        const total = $('.numune-checkbox').length;
        const checked = $('.numune-checkbox:checked').length;
        $('#selectAll').prop('checked', total === checked);
    });

    updateSelectedCount();

    // DataTable başlat
    $('#sonucTable').DataTable({
        "paging": false,
        "searching": true,
        "ordering": true,
        "info": false,
        "language": {
            "search": "Ara:",
            "emptyTable": "Tabloda veri bulunmuyor",
            "zeroRecords": "Eşleşen kayıt bulunamadı",
            "buttons": {
                "excel": "Excel",
                "pdf": "PDF",
                "print": "Yazdır"
            }
        },
        "columnDefs": [
            { "orderable": false, "targets": [0, 9] } // Checkbox ve işlemler sütunları sıralanamaz
        ],
        "order": [[ 7, "desc" ]], // Tarihler sütununa göre azalan sıralama
        "dom": "Brt",
        "buttons": [
            {
                "extend": "excel",
                "text": '<i class=\"fas fa-file-excel me-1\"></i> Excel',
                "className": "btn btn-success btn-sm",
                "title": "Sonuç Listesi",
                "exportOptions": {
                    "columns": ":not(.no-sort):not(:first-child)" // Checkbox ve işlemler sütununu hariç tut
                }
            },
            {
                "extend": "pdf",
                "text": '<i class=\"fas fa-file-pdf me-1\"></i> PDF',
                "className": "btn btn-danger btn-sm",
                "title": "Sonuç Listesi",
                "orientation": "landscape",
                "pageSize": "A4",
                "exportOptions": {
                    "columns": ":not(.no-sort):not(:first-child)" // Checkbox ve işlemler sütununu hariç tut
                }
            },
            {
                "extend": "print",
                "text": '<i class=\"fas fa-print me-1\"></i> Yazdır',
                "className": "btn btn-info btn-sm",
                "title": "Sonuç Listesi",
                "exportOptions": {
                    "columns": ":not(.no-sort):not(:first-child)" // Checkbox ve işlemler sütununu hariç tut
                }
            }
        ]
    });
});

// Seçili sayısını güncelle
function updateSelectedCount() {
    const checked = $('.numune-checkbox:checked');
    const checkedCount = checked.length;

    // Durum bazında sayıları hesapla
    let hazirlikYapildiCount = 0;
    let analizKabulEdildiCount = 0;

    checked.each(function() {
        const durum = $(this).data('durum');
        if (durum === 'Hazırlık Yapıldı') hazirlikYapildiCount++;
        if (durum === 'Analiz Kabul Edildi') analizKabulEdildiCount++;
    });

    // Butonları duruma göre göster/gizle
    if (hazirlikYapildiCount > 0) {
        $('#topluKabulBtn').show();
    } else {
        $('#topluKabulBtn').hide();
    }

    if (analizKabulEdildiCount > 0) {
        $('#topluTamamlaBtn').show();
    } else {
        $('#topluTamamlaBtn').hide();
    }
}

// Tekli analiz kabul
function analizKabul(atamaIds) {
    if (confirm('Seçilen analizleri kabul etmek istediğinizden emin misiniz?')) {
        const form = $('<form method="POST" action="index.php?page=sonuc-listesi&action=analiz-kabul"></form>');
        atamaIds.forEach(function(id) {
            form.append('<input type="hidden" name="atama_ids[]" value="' + id + '">');
        });
        $('body').append(form);
        form.submit();
    }
}

// Tekli analiz tamamla
function analizTamamla(atamaIds) {
    if (confirm('Seçilen analizleri tamamlamak istediğinizden emin misiniz?')) {
        const form = $('<form method="POST" action="index.php?page=sonuc-listesi&action=analiz-tamamla"></form>');
        atamaIds.forEach(function(id) {
            form.append('<input type="hidden" name="atama_ids[]" value="' + id + '">');
        });
        $('body').append(form);
        form.submit();
    }
}

// Toplu analiz kabul
function topluAnalizKabul() {
    const checked = $('.numune-checkbox:checked').filter(function() {
        return $(this).data('durum') === 'Hazırlık Yapıldı';
    });

    if (checked.length === 0) {
        alert('Kabul edilecek analiz bulunamadı.');
        return;
    }

    const atamaIds = [];
    checked.each(function() {
        atamaIds.push($(this).val());
    });

    analizKabul(atamaIds);
}

// Toplu analiz tamamla
function topluAnalizTamamla() {
    const checked = $('.numune-checkbox:checked').filter(function() {
        return $(this).data('durum') === 'Analiz Kabul Edildi';
    });

    if (checked.length === 0) {
        alert('Tamamlanacak analiz bulunamadı.');
        return;
    }

    const atamaIds = [];
    checked.each(function() {
        atamaIds.push($(this).val());
    });

    analizTamamla(atamaIds);
}

// Sayfa yenile
function refreshPage() {
    window.location.reload();
}
</script>

<?php require_once 'views/layout/footer.php'; ?>

<?php
// Test numune kaydetme işlemi
require_once 'config/database.php';
require_once 'models/BaseModel.php';
require_once 'models/Numune.php';
require_once 'models/GenelTanim.php';
require_once 'models/Urun.php';

// Test verisi
$testData = [
    'numune_turu' => 'İç Karantina',
    'numune_alt_turu' => 'Proje',
    'LAB_KAYITNO' => 'TEST-2024-001',
    'gonderen' => 'Test Gönderen',
    'gonderen_personel' => 'Test Personel',
    'firma_id' => 1,
    'urun_id' => 1,
    'mensei' => 'Türkiye',
    'ulke' => 'Türkiye',
    'numune_alindigiyer' => 'Fabrika',
    'numune_sahibi' => 'Test Sahibi',
    'basvuru_no' => 'TEST001',
    'muhur_no' => 'MH001',
    'etiket_no' => 'ET001',
    'barkod' => 'BC001',
    'lotno' => 'LOT001',
    'miktar' => 10.5,
    'birim' => 'kg',
    'teslim_eden' => 'Test Teslim Eden',
    'teslim_alan' => 'Test Teslim Alan',
    'ucret_durumu' => 'Ücretli',
    'gelis_saati' => '14:30:00',
    'aciklama' => 'Test açıklama'
];

try {
    $db = new Database();
    $numuneModel = new Numune($db);
    
    echo "<h2>Test Numune Kaydetme</h2>";
    echo "<h3>Test Verisi:</h3>";
    echo "<pre>" . print_r($testData, true) . "</pre>";
    
    // Veritabanı tablosunu kontrol et
    echo "<h3>Tablo Yapısı Kontrolü:</h3>";
    $sql = "DESCRIBE lab_numuneler";
    $columns = $db->fetchAll($sql);
    echo "<table border='1'>";
    echo "<tr><th>Alan</th><th>Tip</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    foreach ($columns as $col) {
        echo "<tr>";
        echo "<td>{$col['Field']}</td>";
        echo "<td>{$col['Type']}</td>";
        echo "<td>{$col['Null']}</td>";
        echo "<td>{$col['Key']}</td>";
        echo "<td>{$col['Default']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Kaydetme işlemini test et
    echo "<h3>Kaydetme İşlemi:</h3>";
    $result = $numuneModel->saveNumune($testData);
    
    if ($result['success']) {
        echo "<div style='color: green;'>✅ Başarılı! ID: " . $result['id'] . "</div>";
        
        // Kaydedilen veriyi kontrol et
        $saved = $numuneModel->getById($result['id']);
        echo "<h4>Kaydedilen Veri:</h4>";
        echo "<pre>" . print_r($saved, true) . "</pre>";
        
    } else {
        echo "<div style='color: red;'>❌ Hata!</div>";
        if (isset($result['errors'])) {
            echo "<h4>Validasyon Hataları:</h4>";
            echo "<pre>" . print_r($result['errors'], true) . "</pre>";
        }
        if (isset($result['error'])) {
            echo "<h4>Sistem Hatası:</h4>";
            echo "<pre>" . $result['error'] . "</pre>";
        }
    }
    
} catch (Exception $e) {
    echo "<div style='color: red;'>❌ Exception: " . $e->getMessage() . "</div>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>

-- Laboratuvar <PERSON>önetim Sistemi Veritabanı Şeması
-- Firebird için tasarlanmıştır
-- Mevcut IHRACAT_YENI.FDB veritabanına eklenecek tablolar

-- Bu script'i Firebird veritabanında çalıştırın

-- Kullanı<PERSON><PERSON>lar tablosu
CREATE TABLE kullanicilar (
    id INT AUTO_INCREMENT PRIMARY KEY,
    kullaniciadi VARCHAR(50) UNIQUE NOT NULL,
    sifresi VARCHAR(255) NOT NULL,
    adisoyadi VARCHAR(100) NOT NULL,
    email VARCHAR(100),
    telefon VARCHAR(20),
    aktif TINYINT(1) DEFAULT 1,
    kayit_ta<PERSON>hi TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- <PERSON><PERSON> (Delphi sisteminden)
    NUMUNE_KABUL_YAPABILIR TINYINT(1) DEFAULT 0,
    NUMUNE_LISTESI_GOREBILIR TINYINT(1) DEFAULT 0,
    NUMUNE_ONHAZIRLIK_YAPABILIR TINYINT(1) DEFAULT 0,
    NUMUNE_SONUC_GIREBILIR TINYINT(1) DEFAULT 0,
    TANIMLAMA_YAPABILIR TINYINT(1) DEFAULT 0,
    NUMUNE_GENEL_GORUNUM TINYINT(1) DEFAULT 0,
    NUMUNE_ATAMA_YAPABILIR TINYINT(1) DEFAULT 0
);

-- Genel tanımlar tablosu (Delphi sistemindeki geneltanim)
CREATE TABLE geneltanim (
    id INT AUTO_INCREMENT PRIMARY KEY,
    sahibi INT NOT NULL,
    izahat VARCHAR(255) NOT NULL,
    aciklama TEXT,
    INDEX idx_sahibi (sahibi)
);

-- Ürünler tablosu
CREATE TABLE urunler (
    id INT AUTO_INCREMENT PRIMARY KEY,
    urunadi_t VARCHAR(255) NOT NULL,
    aciklama TEXT,
    aktif TINYINT(1) DEFAULT 1
);

-- Laboratuvar birimleri tablosu
CREATE TABLE laboratuvar_birimler (
    id INT AUTO_INCREMENT PRIMARY KEY,
    birimadi VARCHAR(100) NOT NULL,
    aciklama TEXT,
    aktif TINYINT(1) DEFAULT 1
);

-- Numuneler ana tablosu
CREATE TABLE lab_numuneler (
    id INT AUTO_INCREMENT PRIMARY KEY,
    LAB_KAYITNO VARCHAR(50),
    numune_turu ENUM('İç Karantina', 'Dış Karantina') NOT NULL,
    numune_alt_turu VARCHAR(100),
    urun_id INT,
    firma_id INT,
    gonderen VARCHAR(255),
    gonderen_personel VARCHAR(100),
    mensei VARCHAR(100),
    basvuru_no VARCHAR(50),
    muhur_no VARCHAR(50),
    etiket_no VARCHAR(50),
    miktar DECIMAL(10,2),
    birim VARCHAR(20),
    aciklama TEXT,
    barkod VARCHAR(20),
    durumu VARCHAR(100) DEFAULT 'Numune Kabul',
    ucret_durumu VARCHAR(50),
    gelis_tarihi TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    silindi TINYINT(1) DEFAULT 0,

    FOREIGN KEY (urun_id) REFERENCES urunler(id),
    FOREIGN KEY (firma_id) REFERENCES geneltanim(id),
    INDEX idx_numune_turu (numune_turu),
    INDEX idx_durumu (durumu),
    INDEX idx_gelis_tarihi (gelis_tarihi),
    INDEX idx_lab_kayitno (LAB_KAYITNO),
    INDEX idx_barkod (barkod)
);

-- Laboratuvar atamalar tablosu
CREATE TABLE lab_atamalar (
    id INT AUTO_INCREMENT PRIMARY KEY,
    numune_id INT NOT NULL,
    birim_id INT NOT NULL,
    atama_tarihi TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    analiz_tarihi TIMESTAMP NULL,
    analiz_sonucu VARCHAR(100),
    analiz_methodu VARCHAR(255),
    atayan_kullanici INT,

    FOREIGN KEY (numune_id) REFERENCES lab_numuneler(id) ON DELETE CASCADE,
    FOREIGN KEY (birim_id) REFERENCES laboratuvar_birimler(id),
    FOREIGN KEY (atayan_kullanici) REFERENCES kullanicilar(id),
    UNIQUE KEY unique_assignment (numune_id, birim_id)
);

-- Sonuç etmenleri tablosu
CREATE TABLE lab_sonuc_etmenler (
    id INT AUTO_INCREMENT PRIMARY KEY,
    numune_id INT NOT NULL,
    ETMEN_ADI VARCHAR(255) NOT NULL,
    BULASIKMI ENUM('Evet', 'Hayır') DEFAULT 'Hayır',
    deger VARCHAR(100),
    birim VARCHAR(50),

    FOREIGN KEY (numune_id) REFERENCES lab_numuneler(id) ON DELETE CASCADE
);

-- Log tablosu
CREATE TABLE lab_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    numune_id INT,
    islem VARCHAR(255) NOT NULL,
    islemi_yapan VARCHAR(100),
    onceki_deger TEXT,
    tarih TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (numune_id) REFERENCES lab_numuneler(id) ON DELETE SET NULL,
    INDEX idx_numune_id (numune_id),
    INDEX idx_tarih (tarih)
);

-- Parametreler tablosu (sayaçlar için)
CREATE TABLE parametreler (
    id INT AUTO_INCREMENT PRIMARY KEY,
    LAB_Ic_NO INT DEFAULT 1,
    LAB_dis_NO INT DEFAULT 1
);

-- Varsayılan veriler
INSERT INTO parametreler (LAB_Ic_NO, LAB_dis_NO) VALUES (1, 1);

-- Varsayılan admin kullanıcısı (şifre: admin123)
INSERT INTO kullanicilar (
    kullaniciadi, sifresi, adisoyadi,
    NUMUNE_KABUL_YAPABILIR, NUMUNE_LISTESI_GOREBILIR,
    NUMUNE_ONHAZIRLIK_YAPABILIR, NUMUNE_SONUC_GIREBILIR,
    TANIMLAMA_YAPABILIR, NUMUNE_GENEL_GORUNUM, NUMUNE_ATAMA_YAPABILIR
) VALUES (
    'admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Sistem Yöneticisi',
    1, 1, 1, 1, 1, 1, 1
);

-- Genel tanımlar için örnek veriler
-- Ülkeler (sahibi = 85)
INSERT INTO geneltanim (sahibi, izahat) VALUES
(85, 'Türkiye'),
(85, 'Almanya'),
(85, 'Fransa'),
(85, 'İtalya'),
(85, 'İspanya'),
(85, 'Hollanda'),
(85, 'Belçika'),
(85, 'İngiltere'),
(85, 'ABD'),
(85, 'Çin');

-- Müdürlükler (sahibi = 80)
INSERT INTO geneltanim (sahibi, izahat) VALUES
(80, 'Ankara Müdürlüğü'),
(80, 'İstanbul Müdürlüğü'),
(80, 'İzmir Müdürlüğü'),
(80, 'Antalya Müdürlüğü'),
(80, 'Bursa Müdürlüğü');

-- Firmalar (sahibi = 89)
INSERT INTO geneltanim (sahibi, izahat) VALUES
(89, 'ABC Gıda San. Tic. Ltd. Şti.'),
(89, 'XYZ İhracat A.Ş.'),
(89, 'DEF Tarım Ürünleri Ltd.');

-- Alındığı yerler (sahibi = 88)
INSERT INTO geneltanim (sahibi, izahat) VALUES
(88, 'Fabrika'),
(88, 'Depo'),
(88, 'Mağaza'),
(88, 'Gümrük');

-- İç karantina türleri (sahibi = 180)
INSERT INTO geneltanim (sahibi, izahat) VALUES
(180, 'İthalat'),
(180, 'İhracat');

-- Dış karantina türleri (sahibi = 181)
INSERT INTO geneltanim (sahibi, izahat) VALUES
(181, 'Transit'),
(181, 'Gümrük');

-- Etmenler (sahibi = 184)
INSERT INTO geneltanim (sahibi, izahat) VALUES
(184, 'Salmonella'),
(184, 'E.Coli'),
(184, 'Listeria'),
(184, 'Aflatoksin'),
(184, 'Pestisit');

-- Örnek ürünler
INSERT INTO urunler (urunadi_t) VALUES
('Buğday'),
('Mısır'),
('Arpa'),
('Pirinç'),
('Fasulye'),
('Nohut'),
('Mercimek'),
('Fındık'),
('Antep Fıstığı'),
('Kuru İncir');

-- Örnek laboratuvar birimleri
INSERT INTO laboratuvar_birimler (birimadi, aciklama) VALUES
('Mikrobiyoloji', 'Mikrobiyal analizler'),
('Kimya', 'Kimyasal analizler'),
('Pestisit', 'Pestisit kalıntı analizleri'),
('Aflatoksin', 'Aflatoksin analizleri'),
('Fiziksel', 'Fiziksel özellik analizleri');

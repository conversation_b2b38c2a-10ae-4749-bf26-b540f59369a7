<?php
/**
 * Rapor Controller Sınıfı
 */

class RaporController {
    private $numuneModel;
    
    public function __construct() {
        AuthController::checkSession();
        
        $this->numuneModel = new Numune();
    }
    
    // Rapor ana sayfası
    public function index() {
        $data = [
            'user' => AuthController::getCurrentUser()
        ];
        
        require_once 'views/raporlar/index.php';
    }
}
?>

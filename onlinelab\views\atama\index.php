<?php
/**
 * Birim <PERSON>ama Sayfası
 */

require_once 'views/layout/header.php';

// Mesajları göster
if (isset($_SESSION['success'])) {
    echo '<div class="alert alert-success alert-dismissible fade show" role="alert">';
    echo $_SESSION['success'];
    echo '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>';
    echo '</div>';
    unset($_SESSION['success']);
}

if (isset($_SESSION['error'])) {
    echo '<div class="alert alert-danger alert-dismissible fade show" role="alert">';
    echo $_SESSION['error'];
    echo '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>';
    echo '</div>';
    unset($_SESSION['error']);
}

if (isset($_SESSION['warning'])) {
    echo '<div class="alert alert-warning alert-dismissible fade show" role="alert">';
    echo $_SESSION['warning'];
    echo '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>';
    echo '</div>';
    unset($_SESSION['warning']);
}
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="row align-items-center">
                        <div class="col">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-share-alt me-2"></i>Birim Atama
                            </h5>
                            <small class="text-muted">Ödenen numuneleri laboratuvar birimlerine atayın</small>
                        </div>
                        <div class="col-auto">
                            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#topluAtamaModal">
                                <i class="fas fa-layer-group me-2"></i>Toplu Atama
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="card-body">
                    <?php if (empty($data['numuneler'])): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">Atanacak Numune Bulunamadı</h5>
                            <p class="text-muted">Ödenen ve henüz atanmamış numune bulunmamaktadır.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover" id="atamaTable">
                                <thead>
                                    <tr style="background: #f8f9fa !important; background-image: none !important; color: #000 !important;">
                                        <th width="40" style="background: #f8f9fa !important; background-image: none !important; color: #000 !important; font-weight: 600;">
                                            <input type="checkbox" id="selectAll" class="form-check-input">
                                        </th>
                                        <th style="background: #f8f9fa !important; background-image: none !important; color: #000 !important; font-weight: 600;">Lab Kayıt No</th>
                                        <th style="background: #f8f9fa !important; background-image: none !important; color: #000 !important; font-weight: 600;">Numune Türü</th>
                                        <th style="background: #f8f9fa !important; background-image: none !important; color: #000 !important; font-weight: 600;">Gönderen</th>
                                        <th style="background: #f8f9fa !important; background-image: none !important; color: #000 !important; font-weight: 600;">Ürün</th>
                                        <th style="background: #f8f9fa !important; background-image: none !important; color: #000 !important; font-weight: 600;">Firma</th>
                                        <th style="background: #f8f9fa !important; background-image: none !important; color: #000 !important; font-weight: 600;">Geliş Tarihi</th>
                                        <th style="background: #f8f9fa !important; background-image: none !important; color: #000 !important; font-weight: 600;">Ödeme Durumu</th>
                                        <th width="200" style="background: #f8f9fa !important; background-image: none !important; color: #000 !important; font-weight: 600;">İşlemler</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($data['numuneler'] as $numune): ?>
                                        <tr>
                                            <td>
                                                <input type="checkbox" name="numune_ids[]" value="<?= $numune['id'] ?>" class="form-check-input numune-checkbox">
                                            </td>
                                            <td>
                                                <strong><?= safe_html($numune['LAB_KAYITNO']) ?></strong>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?= $numune['numune_turu'] === 'İç Karantina' ? 'primary' : 'info' ?>">
                                                    <?= safe_html($numune['numune_turu']) ?>
                                                </span>
                                            </td>
                                            <td><?= safe_html($numune['gonderen']) ?></td>
                                            <td><?= safe_html($numune['urun_adi']) ?></td>
                                            <td><?= safe_html($numune['firma_adi']) ?></td>
                                            <td><?= date('d.m.Y H:i', strtotime($numune['gelis_tarihi'])) ?></td>
                                            <td>
                                                <span class="badge bg-success">Ödendi</span>
                                            </td>
                                            <td>
                                                <form method="POST" action="index.php?page=atama&action=ata" class="d-inline">
                                                    <input type="hidden" name="numune_id" value="<?= $numune['id'] ?>">
                                                    <div class="input-group input-group-sm">
                                                        <select name="birim_id" class="form-select" required>
                                                            <option value="">Birim Seçin</option>
                                                            <?php foreach ($data['birimler'] as $birim): ?>
                                                                <option value="<?= $birim['id'] ?>"><?= safe_html($birim['birimadi']) ?></option>
                                                            <?php endforeach; ?>
                                                        </select>
                                                        <button type="submit" class="btn btn-primary">
                                                            <i class="fas fa-share-alt"></i>
                                                        </button>
                                                    </div>
                                                </form>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- Pagination -->
                        <?php if ($data['pagination']['pages'] > 1): ?>
                            <nav aria-label="Sayfa navigasyonu">
                                <ul class="pagination justify-content-center">
                                    <?php for ($i = 1; $i <= $data['pagination']['pages']; $i++): ?>
                                        <li class="page-item <?= $i == $data['pagination']['current_page'] ? 'active' : '' ?>">
                                            <a class="page-link" href="?page=atama&page_num=<?= $i ?>"><?= $i ?></a>
                                        </li>
                                    <?php endfor; ?>
                                </ul>
                            </nav>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Toplu Atama Modal -->
<div class="modal fade" id="topluAtamaModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Toplu Birim Atama</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="index.php?page=atama&action=toplu-atama" id="topluAtamaForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="topluBirimId" class="form-label">Atanacak Birim</label>
                        <select name="birim_id" id="topluBirimId" class="form-select" required>
                            <option value="">Birim Seçin</option>
                            <?php foreach ($data['birimler'] as $birim): ?>
                                <option value="<?= $birim['id'] ?>"><?= safe_html($birim['birimadi']) ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        Seçilen numuneler yukarıda belirlenen birime atanacaktır.
                    </div>
                    <div id="seciliNumuneler"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">İptal</button>
                    <button type="submit" class="btn btn-primary">Atama Yap</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Tümünü seç/seçme
    $('#selectAll').change(function() {
        $('.numune-checkbox').prop('checked', this.checked);
        updateTopluAtamaButton();
    });
    
    // Tekil checkbox değişikliği
    $('.numune-checkbox').change(function() {
        updateSelectAllState();
        updateTopluAtamaButton();
    });
    
    // Toplu atama modal açıldığında
    $('#topluAtamaModal').on('show.bs.modal', function() {
        var seciliNumuneler = $('.numune-checkbox:checked');
        var html = '<p><strong>Seçilen Numuneler (' + seciliNumuneler.length + '):</strong></p><ul>';
        
        seciliNumuneler.each(function() {
            var row = $(this).closest('tr');
            var labKayitNo = row.find('td:nth-child(2)').text().trim();
            html += '<li>' + labKayitNo + '</li>';
            $('#topluAtamaForm').append('<input type="hidden" name="numune_ids[]" value="' + $(this).val() + '">');
        });
        
        html += '</ul>';
        $('#seciliNumuneler').html(html);
    });
    
    // Modal kapandığında hidden inputları temizle
    $('#topluAtamaModal').on('hidden.bs.modal', function() {
        $('#topluAtamaForm input[name="numune_ids[]"]').remove();
    });
    
    function updateSelectAllState() {
        var totalCheckboxes = $('.numune-checkbox').length;
        var checkedCheckboxes = $('.numune-checkbox:checked').length;
        
        $('#selectAll').prop('indeterminate', checkedCheckboxes > 0 && checkedCheckboxes < totalCheckboxes);
        $('#selectAll').prop('checked', checkedCheckboxes === totalCheckboxes);
    }
    
    function updateTopluAtamaButton() {
        var checkedCount = $('.numune-checkbox:checked').length;
        $('[data-bs-target="#topluAtamaModal"]').prop('disabled', checkedCount === 0);
    }
    
    // Sayfa yüklendiğinde buton durumunu güncelle
    updateTopluAtamaButton();

    // DataTable başlat
    $('#atamaTable').DataTable({
        "paging": false,
        "searching": true,
        "ordering": true,
        "info": false,
        "language": {
            "search": "Ara:",
            "emptyTable": "Tabloda veri bulunmuyor",
            "zeroRecords": "Eşleşen kayıt bulunamadı"
        },
        "columnDefs": [
            { "orderable": false, "targets": [0, 7] } // Checkbox ve işlemler sütunları sıralanamaz
        ],
        "order": [[ 6, "desc" ]] // Geliş tarihine göre azalan sıralama
    });
});
</script>

<?php require_once 'views/layout/footer.php'; ?>

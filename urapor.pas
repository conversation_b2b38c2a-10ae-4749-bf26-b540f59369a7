unit urapor;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics,
  Controls, Forms, Dialogs, uniGUITypes, uniGUIAbstractClasses,
  uniGUIClasses, uniGUIForm, frxClass, frxDBSet, uniButton, uniBitBtn,
  uniSpeedButton, uniPanel, uniGUIBaseClasses, uniURLFrame, frxExportPDF,
  uniSpinEdit, uniLabel, uniRadioButton, Data.DB, FIBDataSet, pFIBDataSet;

type
  TfrmRapor = class(TUniForm)
    UniURLFrame1: TUniURLFrame;
    UniPanel1: TUniPanel;
    UniSpeedButton1: TUniSpeedButton;
    pnlSertifika: TUniPanel;
    procedure UniSpeedButton1Click(Sender: TObject);
    procedure UniFormKeyDown(Sender: TObject; var Key: Word;
      Shift: TShiftState);
  private
    { Private declarations }
  public
    { Public declarations }
  end;

function frmRapor: TfrmRapor;

implementation

{$R *.dfm}

uses
  MainModule, uniGUIApplication;

function frmRapor: TfrmRapor;
begin
  Result := TfrmRapor(UniMainModule.GetFormInstance(TfrmRapor));
end;

procedure TfrmRapor.UniFormKeyDown(Sender: TObject; var Key: Word;
  Shift: TShiftState);
begin
 if key=vk_escape then close;

end;

procedure TfrmRapor.UniSpeedButton1Click(Sender: TObject);
begin
 close;
end;

end.

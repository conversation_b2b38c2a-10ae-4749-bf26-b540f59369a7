object frmNumuneSonucGir: TfrmNumuneSonucGir
  Left = 0
  Top = 0
  ClientHeight = 542
  ClientWidth = 924
  Caption = 'Numune Sonu'#231
  OnShow = UniFormShow
  OldCreateOrder = False
  OnClose = UniFormClose
  MonitoredKeys.Keys = <>
  PixelsPerInch = 96
  TextHeight = 13
  object UniLabel1: TUniLabel
    Left = 24
    Top = 28
    Width = 109
    Height = 13
    Caption = 'Karantina Etmen Say'#305's'#305
    TabOrder = 0
  end
  object UniLabel2: TUniLabel
    Left = 24
    Top = 55
    Width = 66
    Height = 13
    Caption = 'Analiz Sonucu'
    TabOrder = 1
  end
  object UniLabel5: TUniLabel
    Left = 24
    Top = 82
    Width = 81
    Height = 13
    Caption = 'Analizi Yapan Ki'#351'i'
    TabOrder = 2
  end
  object UniLabel6: TUniLabel
    Left = 24
    Top = 136
    Width = 57
    Height = 13
    Caption = 'Analiz Ta<PERSON>hi'
    TabOrder = 3
  end
  object UniLabel7: TUniLabel
    Left = 24
    Top = 106
    Width = 73
    Height = 13
    Caption = 'Analiz Methodu'
    TabOrder = 4
  end
  object lblPersonel: TUniLabel
    Left = 200
    Top = 82
    Width = 51
    Height = 13
    Caption = 'lblPersonel'
    TabOrder = 5
  end
  object UniDBVerticalGrid1: TUniDBVerticalGrid
    Left = 24
    Top = 172
    Width = 409
    Height = 322
    DataSource = dsNumuneKabul
    FieldsColumn.Width = 150
    VerticalColumns = <
      item
        Width = 300
      end>
    TabOrder = 6
    LoadMask.Message = 'Y'#252'kleniyor...'
    Columns = <
      item
        FieldName = 'GELIS_TARIHI'
        Title.Caption = 'Geli'#351' Tarihi'
        Width = 300
        ReadOnly = True
      end
      item
        FieldName = 'GELIS_SAATI'
        Title.Caption = 'Geli'#351' Saati'
        Width = 300
        ReadOnly = True
      end
      item
        FieldName = 'NUMUNE_TURU'
        Title.Caption = 'Numune T'#252'r'#252
        Width = 300
        ReadOnly = True
      end
      item
        FieldName = 'NUMUNE_ALT_TURU'
        Title.Caption = 'Numune Alt T'#252'r'#252
        Width = 300
        ReadOnly = True
      end
      item
        FieldName = 'LAB_KAYITNO'
        Title.Caption = 'Lab.Kay'#305't No'
        Width = 300
        ReadOnly = True
      end
      item
        FieldName = 'ETIKET_NO'
        Title.Caption = 'Etiket No'
        Width = 300
        ReadOnly = True
      end
      item
        FieldName = 'BASVURU_NO'
        Title.Caption = 'Ba'#351'vuru No'
        Width = 300
        ReadOnly = True
      end
      item
        FieldName = 'MUHUR_NO'
        Title.Caption = 'M'#252'h'#252'r No'
        Width = 300
        ReadOnly = True
      end
      item
        FieldName = 'URUNADI_T'
        Title.Caption = #220'r'#252'n Ad'#305
        Width = 300
        ReadOnly = True
      end
      item
        FieldName = 'URUN_MIKTARI'
        Title.Caption = #220'r'#252'n Miktar'#305
        Width = 300
        ReadOnly = True
      end
      item
        FieldName = 'MIKTAR_BIRIM'
        Title.Caption = 'Birimi'
        Width = 300
        ReadOnly = True
      end>
  end
  object UniPanel1: TUniPanel
    Left = 0
    Top = 498
    Width = 924
    Height = 44
    Align = alBottom
    TabOrder = 7
    BorderStyle = ubsNone
    object UniButton3: TUniButton
      AlignWithMargins = True
      Left = 735
      Top = 3
      Width = 90
      Height = 38
      Caption = 'Kaydet'
      Align = alRight
      TabOrder = 1
      OnClick = UniButton3Click
    end
    object UniButton6: TUniButton
      AlignWithMargins = True
      Left = 831
      Top = 3
      Width = 90
      Height = 38
      Caption = 'Kapat'
      Align = alRight
      TabOrder = 2
      OnClick = UniButton6Click
    end
  end
  object UniDBText1: TUniDBText
    Left = 200
    Top = 134
    Width = 56
    Height = 13
    DataField = 'ANALIZ_TARIHI'
    DataSource = dsSonuc
  end
  object UniDBComboBox3: TUniDBComboBox
    Left = 200
    Top = 106
    Width = 201
    DataField = 'ANALIZ_METHODU'
    DataSource = dsSonuc
    Items.Strings = (
      ''
      'Kar.Etmeni'
      'Di'#287'er')
    TabOrder = 9
    IconItems = <>
  end
  object UniButton9: TUniButton
    Left = 406
    Top = 105
    Width = 27
    Height = 23
    Caption = '---'
    TabOrder = 10
    OnClick = UniButton9Click
  end
  object UniPanel2: TUniPanel
    Left = 80
    Top = 342
    Width = 449
    Height = 139
    Visible = False
    TabOrder = 11
    TitleVisible = True
    Title = 'Etmenler'
    object UniDBGrid1: TUniDBGrid
      Left = 3
      Top = 78
      Width = 443
      Height = 43
      Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgRowSelect, dgConfirmDelete, dgAutoRefreshRow]
      WebOptions.Paged = False
      WebOptions.FetchAll = True
      LoadMask.Message = 'Y'#252'kleniyor...'
      ForceFit = True
      TabOrder = 1
      OnColumnActionClick = UniDBGrid1ColumnActionClick
      Columns = <
        item
          FieldName = 'ETMEN_ADI'
          Title.Caption = 'Etmen Ad'#305
          Width = 268
        end
        item
          FieldName = 'BULASIKMI'
          Title.Caption = 'Bula'#351#305'k m'#305'?'
          Width = 64
        end
        item
          ActionColumn.Enabled = True
          ActionColumn.Buttons = <
            item
              ButtonId = 0
              Hint = 'Se'#231'ili Etmeni Sil'
              IconCls = 'delete'
            end>
          Title.Caption = ' '
          Width = 38
        end>
    end
    object UniLabel8: TUniLabel
      Left = 9
      Top = 8
      Width = 48
      Height = 13
      Caption = 'Etmen Ad'#305
      TabOrder = 2
    end
    object UniButton1: TUniButton
      Left = 375
      Top = 16
      Width = 50
      Height = 51
      Caption = 'Ekle'
      TabOrder = 3
      OnClick = UniButton1Click
    end
    object UniComboBox1: TUniComboBox
      Left = 9
      Top = 27
      Width = 256
      Style = csDropDownList
      TabOrder = 4
      IconItems = <>
    end
    object UniLabel9: TUniLabel
      Left = 95
      Top = 8
      Width = 92
      Height = 13
      Cursor = crHandPoint
      Visible = False
      Caption = 'Yeni Etmen Tan'#305'mla'
      ParentFont = False
      Font.Color = clBlue
      Font.Style = [fsUnderline]
      TabOrder = 5
      OnClick = UniLabel9Click
    end
    object UniLabel11: TUniLabel
      Left = 272
      Top = 8
      Width = 50
      Height = 13
      Caption = 'Bula'#351#305'k m'#305'?'
      TabOrder = 6
    end
    object UniComboBox2: TUniComboBox
      Left = 271
      Top = 26
      Width = 68
      Style = csDropDownList
      Items.Strings = (
        'Hay'#305'r'
        'Evet')
      TabOrder = 7
      IconItems = <>
    end
    object UniCheckBox1: TUniCheckBox
      Left = 9
      Top = 54
      Width = 256
      Height = 17
      Caption = 'Akreditasyon kapsam'#305'ndad'#305'r.'
      TabOrder = 8
    end
  end
  object UniDBText2: TUniDBText
    Left = 200
    Top = 51
    Width = 113
    Height = 23
    DataField = 'ANALIZ_SONUCU'
    DataSource = dsSonuc
    ParentFont = False
    Font.Color = clBlue
    Font.Height = -19
    Font.Style = [fsBold]
    ParentColor = False
    Color = 16447736
  end
  object UniDBText3: TUniDBText
    Left = 200
    Top = 28
    Width = 56
    Height = 13
    DataField = 'KARANTINA_ETMEN_SAYISI'
    DataSource = dsSonuc
  end
  object UniPanel3: TUniPanel
    AlignWithMargins = True
    Left = 457
    Top = 3
    Width = 464
    Height = 492
    Align = alRight
    TabOrder = 14
    TitleVisible = True
    Title = 'Etmenler'
    object UniDBGrid2: TUniDBGrid
      AlignWithMargins = True
      Left = 4
      Top = 4
      Width = 456
      Height = 484
      DataSource = dsEtmenler
      Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgRowSelect, dgConfirmDelete, dgAutoRefreshRow]
      WebOptions.Paged = False
      WebOptions.FetchAll = True
      LoadMask.Message = 'Y'#252'kleniyor...'
      ForceFit = True
      Align = alClient
      TabOrder = 1
      OnDrawColumnCell = UniDBGrid2DrawColumnCell
      OnColumnActionClick = UniDBGrid2ColumnActionClick
      Columns = <
        item
          FieldName = 'ETMEN_ADI'
          Title.Caption = 'Etmen Ad'#305
          Width = 268
        end
        item
          FieldName = 'BULASIKMI'
          Title.Caption = 'Bula'#351#305'k m'#305'?'
          Width = 64
        end
        item
          ActionColumn.Enabled = True
          ActionColumn.Buttons = <
            item
              ButtonId = 0
              Hint = 'Bula'#351#305'kl'#305'k Durumunu De'#287'i'#351'tir'
              IconCls = 'reply'
            end>
          Title.Caption = ' '
          Width = 38
        end>
    end
  end
  object tblNumuneKabul: TpFIBDataSet
    UpdateSQL.Strings = (
      'UPDATE LAB_NUMUNELER'
      'SET '
      '    NUMUNE_TURU = :NUMUNE_TURU,'
      '    NUMUNE_ALT_TURU = :NUMUNE_ALT_TURU,'
      '    LAB_KAYITNO = :LAB_KAYITNO,'
      '    GONDEREN = :GONDEREN,'
      '    NUMUNE_ALINDIGIYER = :NUMUNE_ALINDIGIYER,'
      '    NUMUNE_SAHIBI = :NUMUNE_SAHIBI,'
      '    ETIKET_NO = :ETIKET_NO,'
      '    BASVURU_NO = :BASVURU_NO,'
      '    MUHUR_NO = :MUHUR_NO,'
      '    GONDEREN_PERSONEL = :GONDEREN_PERSONEL,'
      '    MENSEI = :MENSEI,'
      '    ULKE = :ULKE,'
      '    URUN_ID = :URUN_ID,'
      '    URUN_MIKTARI = :URUN_MIKTARI,'
      '    MIKTAR_BIRIM = :MIKTAR_BIRIM,'
      '    GELIS_TARIHI = :GELIS_TARIHI,'
      '    GELIS_SAATI = :GELIS_SAATI,'
      '    TESLIM_EDEN = :TESLIM_EDEN,'
      '    TESLIM_ALAN = :TESLIM_ALAN,'
      '    UCRET_DURUMU = :UCRET_DURUMU,'
      '    DURUMU = :DURUMU,'
      '    ONHAZIRLIK = :ONHAZIRLIK'
      'WHERE'
      '    ID = :OLD_ID'
      '    ')
    DeleteSQL.Strings = (
      'DELETE FROM'
      '    LAB_NUMUNELER'
      'WHERE'
      '        ID = :OLD_ID'
      '    ')
    InsertSQL.Strings = (
      'INSERT INTO LAB_NUMUNELER('
      '    ID,'
      '    NUMUNE_TURU,'
      '    NUMUNE_ALT_TURU,'
      '    LAB_KAYITNO,'
      '    GONDEREN,'
      '    NUMUNE_ALINDIGIYER,'
      '    NUMUNE_SAHIBI,'
      '    ETIKET_NO,'
      '    BASVURU_NO,'
      '    MUHUR_NO,'
      '    GONDEREN_PERSONEL,'
      '    MENSEI,'
      '    ULKE,'
      '    URUN_ID,'
      '    URUN_MIKTARI,'
      '    MIKTAR_BIRIM,'
      '    GELIS_TARIHI,'
      '    GELIS_SAATI,'
      '    TESLIM_EDEN,'
      '    TESLIM_ALAN,'
      '    UCRET_DURUMU,'
      '    DURUMU,'
      '    ONHAZIRLIK'
      ')'
      'VALUES('
      '    :ID,'
      '    :NUMUNE_TURU,'
      '    :NUMUNE_ALT_TURU,'
      '    :LAB_KAYITNO,'
      '    :GONDEREN,'
      '    :NUMUNE_ALINDIGIYER,'
      '    :NUMUNE_SAHIBI,'
      '    :ETIKET_NO,'
      '    :BASVURU_NO,'
      '    :MUHUR_NO,'
      '    :GONDEREN_PERSONEL,'
      '    :MENSEI,'
      '    :ULKE,'
      '    :URUN_ID,'
      '    :URUN_MIKTARI,'
      '    :MIKTAR_BIRIM,'
      '    :GELIS_TARIHI,'
      '    :GELIS_SAATI,'
      '    :TESLIM_EDEN,'
      '    :TESLIM_ALAN,'
      '    :UCRET_DURUMU,'
      '    :DURUMU,'
      '    :ONHAZIRLIK'
      ')')
    RefreshSQL.Strings = (
      'SELECT'
      '    lab_numuneler.*,urunler.urunadi_t'
      'FROM'
      'lab_numuneler'
      'left join urunler on (urunler.id=lab_numuneler.urun_id)'
      ''
      ' WHERE '
      '        LAB_NUMUNELER.ID = :OLD_ID'
      '    ')
    SelectSQL.Strings = (
      'SELECT'
      '   first 1  lab_numuneler.*,urunler.urunadi_t'
      'FROM'
      'lab_numuneler'
      'left join urunler on (urunler.id=lab_numuneler.urun_id)'
      'where'
      'lab_numuneler.id=:idno')
    AutoUpdateOptions.UpdateTableName = 'SERTIFIKA'
    AutoUpdateOptions.KeyFields = 'ID'
    AutoUpdateOptions.GeneratorName = 'GEN_SERTIFIKA_ID'
    AutoUpdateOptions.WhenGetGenID = wgOnNewRecord
    Transaction = UniMainModule.tr
    Database = UniMainModule.db
    Left = 544
    Top = 224
  end
  object dsNumuneKabul: TDataSource
    DataSet = tblNumuneKabul
    Left = 656
    Top = 224
  end
  object dsSonuc: TDataSource
    Left = 656
    Top = 160
  end
  object tblEtmenler: TpFIBDataSet
    UpdateSQL.Strings = (
      'UPDATE LAB_SONUC_ETMENLER'
      'SET '
      '    SONUC_ID = :SONUC_ID,'
      '    ETMEN_ADI = :ETMEN_ADI,'
      '    NUMUNE_ID = :NUMUNE_ID,'
      '    BULASIKMI = :BULASIKMI,'
      '    AKREDITE = :AKREDITE'
      'WHERE'
      '    ID = :OLD_ID'
      '    ')
    DeleteSQL.Strings = (
      'DELETE FROM'
      '    LAB_SONUC_ETMENLER'
      'WHERE'
      '        ID = :OLD_ID'
      '    ')
    InsertSQL.Strings = (
      'INSERT INTO LAB_SONUC_ETMENLER('
      '    ID,'
      '    SONUC_ID,'
      '    ETMEN_ADI,'
      '    NUMUNE_ID,'
      '    BULASIKMI,'
      '    AKREDITE'
      ')'
      'VALUES('
      '    :ID,'
      '    :SONUC_ID,'
      '    :ETMEN_ADI,'
      '    :NUMUNE_ID,'
      '    :BULASIKMI,'
      '    :AKREDITE'
      ')')
    RefreshSQL.Strings = (
      'SELECT'
      '    LAB_SONUC_ETMENLER.*'
      'FROM'
      'LAB_SONUC_ETMENLER'
      'where    LAB_SONUC_ETMENLER.ID = :OLD_ID'
      '   '
      '    ')
    SelectSQL.Strings = (
      'SELECT'
      '    LAB_SONUC_ETMENLER.*'
      'FROM'
      'LAB_SONUC_ETMENLER'
      'where sonuc_id=:id')
    AutoUpdateOptions.UpdateTableName = 'SERTIFIKA'
    AutoUpdateOptions.KeyFields = 'ID'
    AutoUpdateOptions.GeneratorName = 'GEN_SERTIFIKA_ID'
    AutoUpdateOptions.WhenGetGenID = wgOnNewRecord
    Transaction = UniMainModule.tr
    Database = UniMainModule.db
    Left = 544
    Top = 296
  end
  object dsEtmenler: TDataSource
    DataSet = tblEtmenler
    Left = 656
    Top = 296
  end
  object tblTmp: TpFIBDataSet
    UpdateSQL.Strings = (
      'UPDATE LAB_SONUC_ETMENLER'
      'SET '
      '    SONUC_ID = :SONUC_ID,'
      '    ETMEN_ADI = :ETMEN_ADI,'
      '    NUMUNE_ID = :NUMUNE_ID,'
      '    BULASIKMI = :BULASIKMI'
      'WHERE'
      '    ID = :OLD_ID'
      '    ')
    DeleteSQL.Strings = (
      'DELETE FROM'
      '    LAB_SONUC_ETMENLER'
      'WHERE'
      '        ID = :OLD_ID'
      '    ')
    InsertSQL.Strings = (
      'INSERT INTO LAB_SONUC_ETMENLER('
      '    ID,'
      '    SONUC_ID,'
      '    ETMEN_ADI,'
      '    NUMUNE_ID,'
      '    BULASIKMI'
      ')'
      'VALUES('
      '    :ID,'
      '    :SONUC_ID,'
      '    :ETMEN_ADI,'
      '    :NUMUNE_ID,'
      '    :BULASIKMI'
      ')')
    RefreshSQL.Strings = (
      'SELECT'
      '    LAB_SONUC_ETMENLER.*'
      'FROM'
      'LAB_SONUC_ETMENLER'
      'where      LAB_SONUC_ETMENLER.ID = :OLD_ID'
      '      '
      '')
    SelectSQL.Strings = (
      'SELECT'
      '    LAB_SONUC_ETMENLER.*'
      'FROM'
      'LAB_SONUC_ETMENLER'
      'where sonuc_id=:id')
    AutoUpdateOptions.UpdateTableName = 'SERTIFIKA'
    AutoUpdateOptions.KeyFields = 'ID'
    AutoUpdateOptions.GeneratorName = 'GEN_SERTIFIKA_ID'
    AutoUpdateOptions.WhenGetGenID = wgOnNewRecord
    Transaction = UniMainModule.tr
    Database = UniMainModule.db
    Left = 552
    Top = 384
  end
end

object frmNumuneListesi: TfrmNumuneListesi
  Left = 0
  Top = 0
  ClientHeight = 678
  ClientWidth = 1278
  Caption = 'Numune Listesi'
  OnShow = UniFormShow
  WindowState = wsMaximized
  OldCreateOrder = False
  MonitoredKeys.Keys = <>
  PixelsPerInch = 96
  TextHeight = 13
  object UniDBGrid1: TUniDBGrid
    AlignWithMargins = True
    Left = 3
    Top = 151
    Width = 1272
    Height = 480
    ClientEvents.ExtEvents.Strings = (
      
        'beforerender=function beforerender(sender, eOpts)'#13#10'{'#13#10'    if (se' +
        'nder.pagingBar) {    '#13#10' sender.removeDocked(sender.pagingBar, tr' +
        'ue);'#13#10'    sender.addDocked('#13#10'         {dock: '#39'bottom'#39',          ' +
        ' '#13#10'          items: [Ext.create('#39'Ext.PagingToolbar'#39', {          ' +
        '  '#13#10'            pageSize: sender.store.pageSize,'#13#10'            st' +
        'ore: sender.store,'#13#10'            displayInfo: true,            '#13#10 +
        '            plugins: Ext.create('#39'Ext.ux.PagingToolbarResizer'#39',{d' +
        'isplayText: '#39'Records per Page'#39', options : [ 5, 10, 15, 20, 25 ]}' +
        ')'#13#10'          })]'#13#10'         });       '#13#10'  } '#13#10'}')
    ClientEvents.UniEvents.Strings = (
      
        'beforeInit=function beforeInit(sender, config)'#13#10'{'#13#10'        confi' +
        'g.viewConfig.enableTextSelection = true;'#13#10'}')
    DataSource = dsNumuneKabul
    Options = [dgTitles, dgIndicator, dgColumnResize, dgColumnMove, dgColLines, dgRowSelect, dgCheckSelect, dgConfirmDelete, dgMultiSelect, dgAutoRefreshRow, dgDontShowSelected, dgRowNumbers]
    ReadOnly = True
    LoadMask.Message = 'Y'#252'kleniyor...'
    Align = alClient
    TabOrder = 0
    Exporter.Enabled = True
    Exporter.FileName = 'Numune_liste'
    Exporter.Exporter = UniGridExcelExporter1
    Exporter.Title = 'Numune Listesi'
    OnColumnSort = UniDBGrid1ColumnSort
    OnFieldImageURL = UniDBGrid1FieldImageURL
    OnDrawColumnCell = UniDBGrid1DrawColumnCell
    OnColumnActionClick = UniDBGrid1ColumnActionClick
    Columns = <
      item
        FieldName = 'DURUMU'
        Title.Caption = 'Durumu'
        Width = 45
        Sortable = True
        ImageOptions.Visible = True
      end
      item
        FieldName = 'DURUMU'
        Title.Caption = 'Durumu'
        Width = 184
        Sortable = True
      end
      item
        FieldName = 'GELIS_TARIHI'
        Title.Caption = 'Geli'#351' Tarihi'
        Width = 77
        Sortable = True
      end
      item
        FieldName = 'GELIS_SAATI'
        Title.Caption = 'Geli'#351' Saati'
        Width = 74
        Sortable = True
      end
      item
        FieldName = 'ATAMABIRIM'
        Title.Caption = 'Atama Yap'#305'lan Birimler'
        Width = 120
        Sortable = True
        DisplayMemo = True
      end
      item
        FieldName = 'NUMUNE_TURU'
        Title.Caption = 'Numune T'#252'r'#252
        Width = 123
        Sortable = True
      end
      item
        FieldName = 'NUMUNE_ALT_TURU'
        Title.Caption = 'Alt T'#252'r'#252
        Width = 124
        Sortable = True
      end
      item
        FieldName = 'LAB_KAYITNO'
        Title.Caption = 'Lab.Kay'#305't No'
        Width = 136
        Sortable = True
      end
      item
        FieldName = 'GONDEREN'
        Title.Caption = 'G'#246'nderen Kurum'
        Width = 150
        Sortable = True
      end
      item
        FieldName = 'GONDEREN_PERSONEL'
        Title.Caption = 'G'#246'nderen Personel'
        Width = 124
        Sortable = True
      end
      item
        FieldName = 'UCRET_DURUMU'
        Title.Caption = #220'cret Durumu'
        Width = 45
        Sortable = True
        ImageOptions.Visible = True
      end
      item
        FieldName = 'UCRET_DURUMU'
        Title.Caption = #220'cret Durumu'
        Width = 100
        Sortable = True
      end
      item
        FieldName = 'NUMUNE_ALINDIGIYER'
        Title.Caption = 'Numunenin Al'#305'nd'#305#287#305' Yer'
        Width = 120
        Sortable = True
      end
      item
        FieldName = 'ETIKET_NO'
        Title.Caption = 'Etiket No'
        Width = 80
        Sortable = True
      end
      item
        FieldName = 'BASVURU_NO'
        Title.Caption = 'Ba'#351'vuru No'
        Width = 80
        Sortable = True
      end
      item
        FieldName = 'MUHUR_NO'
        Title.Caption = 'M'#252'h'#252'r No'
        Width = 80
        Sortable = True
      end
      item
        FieldName = 'MENSEI'
        Title.Caption = 'Men'#351'ei'
        Width = 100
        Sortable = True
      end
      item
        FieldName = 'URUNADI_T'
        Title.Caption = #220'r'#252'n Ad'#305
        Width = 204
        Sortable = True
      end
      item
        FieldName = 'URUN_MIKTARI'
        Title.Caption = #220'r'#252'n Miktar'#305
        Width = 100
        Sortable = True
      end
      item
        FieldName = 'MIKTAR_BIRIM'
        Title.Caption = 'Miktar Birim'
        Width = 80
        Sortable = True
      end
      item
        FieldName = 'TESLIM_EDEN'
        Title.Caption = 'Teslim Eden'
        Width = 100
        Sortable = True
      end
      item
        FieldName = 'TESLIM_ALAN'
        Title.Caption = 'Teslim Alan'
        Width = 100
        Sortable = True
      end
      item
        FieldName = 'BARKOD'
        Title.Caption = 'Barkod'
        Width = 100
        Sortable = True
      end
      item
        FieldName = 'KULLANILAN_NUMUNE'
        Title.Caption = 'Kalan Numune Miktar'#305
        Width = 90
      end
      item
        FieldName = 'ETmenadi'
        Title.Caption = 'Etmen Ad'#305
        Width = 200
        DisplayMemo = True
      end
      item
        FieldName = 'bulasikETMENADI'
        Title.Caption = 'Bula'#351#305'k Olan Etmenler'
        Width = 100
        Font.Color = clRed
      end
      item
        FieldName = 'firmaadi'
        Title.Caption = 'Firma Ad'#305
        Width = 150
      end
      item
        FieldName = 'ACIKLAMALAR'
        Title.Caption = 'Notlar'
        Width = 150
        Sortable = True
      end
      item
        ActionColumn.Enabled = True
        WidgetColumn.Widget = UniButtonWidget2
        Title.Caption = 'Kalan'#305' Gir'
        Width = 100
        Visible = False
      end
      item
        ActionColumn.Enabled = True
        WidgetColumn.Enabled = True
        WidgetColumn.Widget = UniButtonWidget1
        Title.Caption = 'Dosya Y'#252'kle'
        Width = 100
      end
      item
        ActionColumn.Enabled = True
        ActionColumn.Buttons = <
          item
            ButtonId = 0
            Hint = 'Hareket Ge'#231'mi'#351'i'
            IconCls = 'info'
          end>
        Title.Caption = ' '
        Width = 35
      end>
  end
  object UniPanel1: TUniPanel
    Left = 0
    Top = 634
    Width = 1278
    Height = 44
    Align = alBottom
    TabOrder = 1
    object brnDuzenle: TUniButton
      AlignWithMargins = True
      Left = 1088
      Top = 4
      Width = 90
      Height = 36
      Caption = 'D'#252'zenle'
      Align = alRight
      TabOrder = 1
      OnClick = brnDuzenleClick
    end
    object UniButton4: TUniButton
      AlignWithMargins = True
      Left = 3
      Top = 3
      Width = 69
      Height = 38
      Margins.Left = 2
      Margins.Top = 2
      Margins.Right = 2
      Margins.Bottom = 2
      Caption = 'Sil'
      Align = alLeft
      TabOrder = 2
      OnClick = UniButton4Click
    end
    object btnOnayaGonder: TUniButton
      AlignWithMargins = True
      Left = 952
      Top = 4
      Width = 130
      Height = 36
      Visible = False
      Caption = 'Onaya G'#246'nder'
      Align = alRight
      TabOrder = 3
      OnClick = btnOnayaGonderClick
    end
    object UniButton6: TUniButton
      AlignWithMargins = True
      Left = 1184
      Top = 4
      Width = 90
      Height = 36
      Caption = 'Kapat'
      Align = alRight
      TabOrder = 4
      OnClick = UniButton6Click
    end
    object btnOnayla: TUniButton
      AlignWithMargins = True
      Left = 856
      Top = 4
      Width = 90
      Height = 36
      Visible = False
      Caption = 'Onayla'
      Align = alRight
      TabOrder = 5
      OnClick = btnOnaylaClick
    end
    object btnOnayiptal: TUniButton
      AlignWithMargins = True
      Left = 760
      Top = 4
      Width = 90
      Height = 36
      Visible = False
      Caption = 'Onaylama'
      Align = alRight
      TabOrder = 6
      OnClick = btnOnayiptalClick
    end
    object btnOdendi: TUniButton
      AlignWithMargins = True
      Left = 170
      Top = 3
      Width = 90
      Height = 38
      Margins.Left = 2
      Margins.Top = 2
      Margins.Right = 2
      Margins.Bottom = 2
      Caption = #214'dendi'
      Align = alLeft
      TabOrder = 7
      OnClick = btnOdendiClick
    end
    object btnOdenmedi: TUniButton
      AlignWithMargins = True
      Left = 76
      Top = 3
      Width = 90
      Height = 38
      Margins.Left = 2
      Margins.Top = 2
      Margins.Right = 2
      Margins.Bottom = 2
      Caption = #214'denmedi'
      Align = alLeft
      TabOrder = 8
      OnClick = btnOdenmediClick
    end
    object btnBirimlereGonder: TUniButton
      AlignWithMargins = True
      Left = 264
      Top = 3
      Width = 112
      Height = 38
      Margins.Left = 2
      Margins.Top = 2
      Margins.Right = 2
      Margins.Bottom = 2
      Caption = 'Birimleri Ata'
      Align = alLeft
      TabOrder = 9
      OnClick = btnBirimlereGonderClick
    end
    object UniButton5: TUniButton
      AlignWithMargins = True
      Left = 657
      Top = 3
      Width = 98
      Height = 38
      Margins.Left = 2
      Margins.Top = 2
      Margins.Right = 2
      Margins.Bottom = 2
      Caption = 'Sonucu G'#246'ster'
      Align = alRight
      TabOrder = 10
      OnClick = UniButton5Click
    end
    object UniButton9: TUniButton
      AlignWithMargins = True
      Left = 380
      Top = 3
      Width = 104
      Height = 38
      Margins.Left = 2
      Margins.Top = 2
      Margins.Right = 2
      Margins.Bottom = 2
      Caption = 'Yazd'#305'r'
      Align = alLeft
      TabOrder = 11
      OnClick = UniButton9Click
    end
    object UniButton10: TUniButton
      AlignWithMargins = True
      Left = 488
      Top = 3
      Width = 121
      Height = 38
      Margins.Left = 2
      Margins.Top = 2
      Margins.Right = 2
      Margins.Bottom = 2
      Visible = False
      Caption = 'Birim Barkod Yazd'#305'r'
      Align = alLeft
      TabOrder = 12
      OnClick = UniButton10Click
    end
  end
  object UniPanel2: TUniPanel
    AlignWithMargins = True
    Left = 3
    Top = 3
    Width = 1272
    Height = 49
    Align = alTop
    TabOrder = 2
    BorderStyle = ubsNone
    object UniButton1: TUniButton
      AlignWithMargins = True
      Left = 3
      Top = 3
      Width = 210
      Height = 43
      Caption = 'Numune Ekle'
      Align = alLeft
      TabOrder = 1
      OnClick = UniButton1Click
    end
    object UniButton7: TUniButton
      AlignWithMargins = True
      Left = 1112
      Top = 3
      Width = 157
      Height = 43
      Caption = 'Ekran Ayarlar'#305'n'#305' Kaydet'
      Align = alRight
      TabOrder = 2
      OnClick = UniButton7Click
    end
    object UniButton8: TUniButton
      AlignWithMargins = True
      Left = 1018
      Top = 3
      Width = 88
      Height = 43
      Caption = 'Excel'#39'e Aktar'
      Align = alRight
      TabOrder = 3
      OnClick = UniButton8Click
    end
    object UniButton3: TUniButton
      AlignWithMargins = True
      Left = 219
      Top = 3
      Width = 112
      Height = 43
      Caption = 'Kopyala'
      Align = alLeft
      TabOrder = 4
      OnClick = UniButton3Click
    end
  end
  object UniTabControl1: TUniTabControl
    AlignWithMargins = True
    Left = 3
    Top = 58
    Width = 1272
    Height = 87
    TabIndex = 1
    Tabs.Strings = (
      #304#231' Karantina'
      'D'#305#351' Karantina')
    OnChange = UniTabControl1Change
    Align = alTop
    TabOrder = 3
    ExplicitLeft = -2
    object UniLabel1: TUniLabel
      Left = 17
      Top = 42
      Width = 79
      Height = 13
      Caption = 'Giri'#351' Tarih Aral'#305#287#305
      TabOrder = 1
    end
    object UniDateTimePicker1: TUniDateTimePicker
      Left = 17
      Top = 61
      Width = 120
      DateTime = 45615.000000000000000000
      DateFormat = 'dd/MM/yyyy'
      TimeFormat = 'HH:mm:ss'
      TabOrder = 2
    end
    object UniDateTimePicker2: TUniDateTimePicker
      Left = 139
      Top = 61
      Width = 120
      DateTime = 45615.000000000000000000
      DateFormat = 'dd/MM/yyyy'
      TimeFormat = 'HH:mm:ss'
      TabOrder = 3
    end
    object btnGuncelle: TUniButton
      AlignWithMargins = True
      Left = 1015
      Top = 48
      Width = 108
      Height = 36
      Caption = 'Listele'
      TabOrder = 4
      ScreenMask.Enabled = True
      ScreenMask.Message = 'Sorgulan'#305'yor...'
      OnClick = btnGuncelleClick
    end
    object edtKayitNo: TUniEdit
      Left = 265
      Top = 61
      Width = 121
      TabOrder = 5
    end
    object UniLabel3: TUniLabel
      Left = 266
      Top = 44
      Width = 40
      Height = 13
      Caption = 'Kay'#305't No'
      TabOrder = 6
    end
    object UniLabel4: TUniLabel
      Left = 390
      Top = 44
      Width = 46
      Height = 13
      Caption = 'M'#252'h'#252'r No'
      TabOrder = 7
    end
    object edtMuhurNo: TUniEdit
      Left = 389
      Top = 61
      Width = 121
      TabOrder = 8
    end
    object edtBasvuruNo: TUniEdit
      Left = 512
      Top = 61
      Width = 121
      TabOrder = 9
    end
    object UniLabel5: TUniLabel
      Left = 513
      Top = 44
      Width = 55
      Height = 13
      Caption = 'Ba'#351'vuru No'
      TabOrder = 10
    end
    object UniLabel2: TUniLabel
      Left = 636
      Top = 44
      Width = 44
      Height = 13
      Caption = 'Firma Ad'#305
      TabOrder = 11
    end
    object edtFirma: TUniEdit
      Left = 635
      Top = 61
      Width = 212
      TabOrder = 12
    end
    object UniLabel6: TUniLabel
      Left = 852
      Top = 44
      Width = 37
      Height = 13
      Caption = 'Durumu'
      TabOrder = 13
    end
    object edtDurumu: TUniEdit
      Left = 850
      Top = 61
      Width = 159
      TabOrder = 14
    end
  end
  object UniHiddenPanel1: TUniHiddenPanel
    Left = 568
    Top = 232
    Width = 160
    Height = 256
    Visible = True
    object UniButtonWidget1: TUniButtonWidget
      Left = 3
      Top = 3
      Width = 135
      Height = 33
      ShowCaption = True
      ShowValue = False
      OnClick = UniButtonWidget1Click
      Caption = 'Dosya Y'#252'kle'
    end
    object UniButtonWidget2: TUniButtonWidget
      Left = 3
      Top = 40
      Width = 135
      Height = 32
      ShowCaption = True
      ShowValue = False
      OnClick = UniButtonWidget2Click
      Caption = 'Kalan'#305' Gir'
    end
  end
  object tblNumuneKabul: TpFIBDataSet
    UpdateSQL.Strings = (
      'UPDATE LAB_NUMUNELER'
      'SET '
      '    NUMUNE_TURU = :NUMUNE_TURU,'
      '    NUMUNE_ALT_TURU = :NUMUNE_ALT_TURU,'
      '    GONDEREN = :GONDEREN,'
      '    NUMUNE_ALINDIGIYER = :NUMUNE_ALINDIGIYER,'
      '    NUMUNE_SAHIBI = :NUMUNE_SAHIBI,'
      '    ETIKET_NO = :ETIKET_NO,'
      '    BASVURU_NO = :BASVURU_NO,'
      '    MUHUR_NO = :MUHUR_NO,'
      '    GONDEREN_PERSONEL = :GONDEREN_PERSONEL,'
      '    MENSEI = :MENSEI,'
      '    ULKE = :ULKE,'
      '    URUN_ID = :URUN_ID,'
      '    URUN_MIKTARI = :URUN_MIKTARI,'
      '    MIKTAR_BIRIM = :MIKTAR_BIRIM,'
      '    GELIS_TARIHI = :GELIS_TARIHI,'
      '    GELIS_SAATI = :GELIS_SAATI,'
      '    TESLIM_EDEN = :TESLIM_EDEN,'
      '    TESLIM_ALAN = :TESLIM_ALAN,'
      '    UCRET_DURUMU = :UCRET_DURUMU,'
      '    DURUMU = :DURUMU,'
      '    ONHAZIRLIK = :ONHAZIRLIK,'
      '    BARKOD = :BARKOD,'
      '    SILINDI = :SILINDI,'
      '    ACIKLAMALAR = :ACIKLAMALAR,'
      '    FIRMA_ID = :FIRMA_ID,'
      '    ANALIZ_SONUCU = :ANALIZ_SONUCU,'
      '    LOTNO = :LOTNO,'
      '    KULLANILAN_NUMUNE = :KULLANILAN_NUMUNE,'
      '    LAB_KAYITNO = :LAB_KAYITNO,'
      '    MENSEI_2 = :MENSEI_2'
      'WHERE'
      '    ID = :OLD_ID'
      '    ')
    DeleteSQL.Strings = (
      'DELETE FROM'
      '    LAB_NUMUNELER'
      'WHERE'
      '        ID = :OLD_ID'
      '    ')
    InsertSQL.Strings = (
      'INSERT INTO LAB_NUMUNELER('
      '    ID,'
      '    NUMUNE_TURU,'
      '    NUMUNE_ALT_TURU,'
      '    GONDEREN,'
      '    NUMUNE_ALINDIGIYER,'
      '    NUMUNE_SAHIBI,'
      '    ETIKET_NO,'
      '    BASVURU_NO,'
      '    MUHUR_NO,'
      '    GONDEREN_PERSONEL,'
      '    MENSEI,'
      '    ULKE,'
      '    URUN_ID,'
      '    URUN_MIKTARI,'
      '    MIKTAR_BIRIM,'
      '    GELIS_TARIHI,'
      '    GELIS_SAATI,'
      '    TESLIM_EDEN,'
      '    TESLIM_ALAN,'
      '    UCRET_DURUMU,'
      '    DURUMU,'
      '    ONHAZIRLIK,'
      '    BARKOD,'
      '    SILINDI,'
      '    ACIKLAMALAR,'
      '    FIRMA_ID,'
      '    ANALIZ_SONUCU,'
      '    LOTNO,'
      '    KULLANILAN_NUMUNE,'
      '    LAB_KAYITNO,'
      '    MENSEI_2'
      ')'
      'VALUES('
      '    :ID,'
      '    :NUMUNE_TURU,'
      '    :NUMUNE_ALT_TURU,'
      '    :GONDEREN,'
      '    :NUMUNE_ALINDIGIYER,'
      '    :NUMUNE_SAHIBI,'
      '    :ETIKET_NO,'
      '    :BASVURU_NO,'
      '    :MUHUR_NO,'
      '    :GONDEREN_PERSONEL,'
      '    :MENSEI,'
      '    :ULKE,'
      '    :URUN_ID,'
      '    :URUN_MIKTARI,'
      '    :MIKTAR_BIRIM,'
      '    :GELIS_TARIHI,'
      '    :GELIS_SAATI,'
      '    :TESLIM_EDEN,'
      '    :TESLIM_ALAN,'
      '    :UCRET_DURUMU,'
      '    :DURUMU,'
      '    :ONHAZIRLIK,'
      '    :BARKOD,'
      '    :SILINDI,'
      '    :ACIKLAMALAR,'
      '    :FIRMA_ID,'
      '    :ANALIZ_SONUCU,'
      '    :LOTNO,'
      '    :KULLANILAN_NUMUNE,'
      '    :LAB_KAYITNO,'
      '    :MENSEI_2'
      ')')
    RefreshSQL.Strings = (
      'SELECT'
      'lab_numuneler.*,urunler.urunadi_t'
      ',(select list(lb.birimadi,'#39','#39') from lab_atamalar'
      
        'inner join LABORATUVAR_BIRIMLER lb on (lb.id=lab_atamalar.birim_' +
        'id) where numune_id=lab_numuneler.id) as atamabirim_duz'
      
        ',(select * from SP_LABATAMA_DURUM(lab_numuneler.id)) as atamabir' +
        'im'
      ',lab_numuneler.ANALIZ_SONUCU'
      
        ',(select izahat from geneltanim where sahibi=89 and id=lab_numun' +
        'eler.firma_id) as firmaadi'
      
        ',(select list(ls.ETMEN_ADI,'#39','#39') from LAB_SONUC_ETMENLER ls where' +
        ' ls.numune_id= lab_numuneler.id) as ETMENADI'
      'FROM'
      'lab_numuneler'
      'left join urunler on (urunler.id=lab_numuneler.urun_id)'
      'Where(  silindi = 0'
      '     ) and (     LAB_NUMUNELER.ID = :OLD_ID'
      '     )'
      '    '
      ' '
      ' ')
    SelectSQL.Strings = (
      'SELECT'
      'lab_numuneler.*,urunler.urunadi_t'
      ',(select list(lb.birimadi,'#39','#39') from lab_atamalar'
      
        'inner join LABORATUVAR_BIRIMLER lb on (lb.id=lab_atamalar.birim_' +
        'id) where numune_id=lab_numuneler.id) as atamabirim_duz'
      
        ',(select * from SP_LABATAMA_DURUM(lab_numuneler.id)) as atamabir' +
        'im'
      ',lab_numuneler.ANALIZ_SONUCU'
      
        ',(select izahat from geneltanim where sahibi=89 and id=lab_numun' +
        'eler.firma_id) as firmaadi'
      
        ',(select list(ls.ETMEN_ADI,'#39','#39') from LAB_SONUC_ETMENLER ls where' +
        ' ls.numune_id= lab_numuneler.id) as ETMENADI'
      'FROM'
      'lab_numuneler'
      'left join urunler on (urunler.id=lab_numuneler.urun_id)'
      'Where silindi = 0'
      ' '
      'order by lab_numuneler.id desc')
    AutoUpdateOptions.UpdateTableName = 'SERTIFIKA'
    AutoUpdateOptions.KeyFields = 'ID'
    AutoUpdateOptions.GeneratorName = 'GEN_SERTIFIKA_ID'
    AutoUpdateOptions.WhenGetGenID = wgOnNewRecord
    OnNewRecord = tblNumuneKabulNewRecord
    Transaction = UniMainModule.tr
    Database = UniMainModule.db
    Left = 173
    Top = 192
  end
  object dsNumuneKabul: TDataSource
    DataSet = tblNumuneKabul
    Left = 173
    Top = 256
  end
  object UniGridExcelExporter1: TUniGridExcelExporter
    FileExtention = 'xlsx'
    MimeType = 
      'application/vnd.openxmlformats-officedocument.spreadsheetml.shee' +
      't'
    CharSet = 'UTF-8'
    Left = 336
    Top = 256
  end
  object rpnumune_genel: TfrxDBDataset
    UserName = 'rpnumune_genel'
    CloseDataSource = False
    DataSet = tblTutanakYazdir
    BCDToCurrency = False
    Left = 632
    Top = 360
  end
  object frxReport1: TfrxReport
    Version = '5.2.12'
    DotMatrixReport = False
    IniFile = '\Software\Fast Reports'
    PreviewOptions.Buttons = [pbPrint, pbLoad, pbSave, pbExport, pbZoom, pbFind, pbOutline, pbPageSetup, pbTools, pbEdit, pbNavigator, pbExportQuick]
    PreviewOptions.Zoom = 1.000000000000000000
    PrintOptions.Printer = 'Varsay'#305'lan'
    PrintOptions.PrintOnSheet = 0
    ReportOptions.CreateDate = 45692.997786782410000000
    ReportOptions.LastChange = 45692.997786782410000000
    ScriptLanguage = 'PascalScript'
    ScriptText.Strings = (
      ''
      'begin'
      ''
      'end.')
    OnGetValue = frxReport1GetValue
    Left = 536
    Top = 360
    Datasets = <>
    Variables = <>
    Style = <>
    object Data: TfrxDataPage
      Height = 1000.000000000000000000
      Width = 1000.000000000000000000
    end
    object Page1: TfrxReportPage
      PaperWidth = 215.900000000000000000
      PaperHeight = 279.400000000000000000
      PaperSize = 1
      LeftMargin = 10.000000000000000000
      RightMargin = 10.000000000000000000
      TopMargin = 10.000000000000000000
      BottomMargin = 10.000000000000000000
      object ReportTitle1: TfrxReportTitle
        FillType = ftBrush
        Height = 22.677180000000000000
        Top = 18.897650000000000000
        Width = 740.787880000000000000
      end
      object MasterData1: TfrxMasterData
        FillType = ftBrush
        Height = 22.677180000000000000
        Top = 102.047310000000000000
        Width = 740.787880000000000000
        RowCount = 0
      end
      object PageFooter1: TfrxPageFooter
        FillType = ftBrush
        Height = 22.677180000000000000
        Top = 185.196970000000000000
        Width = 740.787880000000000000
        object Memo1: TfrxMemoView
          Left = 665.197280000000000000
          Width = 75.590600000000000000
          Height = 18.897650000000000000
          HAlign = haRight
          Memo.UTF8W = (
            '[Page#]')
        end
      end
    end
  end
  object frxPDFExport1: TfrxPDFExport
    UseFileCache = True
    ShowProgress = True
    OverwritePrompt = False
    DataOnly = False
    PrintOptimized = False
    Outline = False
    Background = False
    HTMLTags = True
    Quality = 95
    Transparency = False
    Author = 'FastReport'
    Subject = 'FastReport PDF export'
    ProtectionFlags = [ePrint, eModify, eCopy, eAnnot]
    HideToolbar = False
    HideMenubar = False
    HideWindowUI = False
    FitWindow = False
    CenterWindow = False
    PrintScaling = False
    PdfA = False
    Left = 536
    Top = 424
  end
  object tblTutanakYazdir: TpFIBDataSet
    UpdateSQL.Strings = (
      'UPDATE LAB_NUMUNELER'
      'SET '
      '    NUMUNE_TURU = :NUMUNE_TURU,'
      '    NUMUNE_ALT_TURU = :NUMUNE_ALT_TURU,'
      '    LAB_KAYITNO = :LAB_KAYITNO,'
      '    GONDEREN = :GONDEREN,'
      '    NUMUNE_ALINDIGIYER = :NUMUNE_ALINDIGIYER,'
      '    NUMUNE_SAHIBI = :NUMUNE_SAHIBI,'
      '    ETIKET_NO = :ETIKET_NO,'
      '    BASVURU_NO = :BASVURU_NO,'
      '    MUHUR_NO = :MUHUR_NO,'
      '    GONDEREN_PERSONEL = :GONDEREN_PERSONEL,'
      '    MENSEI = :MENSEI,'
      '    ULKE = :ULKE,'
      '    URUN_ID = :URUN_ID,'
      '    URUN_MIKTARI = :URUN_MIKTARI,'
      '    MIKTAR_BIRIM = :MIKTAR_BIRIM,'
      '    GELIS_TARIHI = :GELIS_TARIHI,'
      '    GELIS_SAATI = :GELIS_SAATI,'
      '    TESLIM_EDEN = :TESLIM_EDEN,'
      '    TESLIM_ALAN = :TESLIM_ALAN,'
      '    UCRET_DURUMU = :UCRET_DURUMU,'
      '    DURUMU = :DURUMU,'
      '    ONHAZIRLIK = :ONHAZIRLIK,'
      '    BARKOD = :BARKOD,'
      '    SILINDI = :SILINDI,'
      '    ACIKLAMALAR = :ACIKLAMALAR,'
      '    FIRMA_ID = :FIRMA_ID,'
      '    ANALIZ_SONUCU = :ANALIZ_SONUCU,'
      '    LOTNO = :LOTNO'
      'WHERE'
      '    ID = :OLD_ID'
      '    ')
    DeleteSQL.Strings = (
      'DELETE FROM'
      '    LAB_NUMUNELER'
      'WHERE'
      '        ID = :OLD_ID'
      '    ')
    InsertSQL.Strings = (
      'INSERT INTO LAB_NUMUNELER('
      '    ID,'
      '    NUMUNE_TURU,'
      '    NUMUNE_ALT_TURU,'
      '    LAB_KAYITNO,'
      '    GONDEREN,'
      '    NUMUNE_ALINDIGIYER,'
      '    NUMUNE_SAHIBI,'
      '    ETIKET_NO,'
      '    BASVURU_NO,'
      '    MUHUR_NO,'
      '    GONDEREN_PERSONEL,'
      '    MENSEI,'
      '    ULKE,'
      '    URUN_ID,'
      '    URUN_MIKTARI,'
      '    MIKTAR_BIRIM,'
      '    GELIS_TARIHI,'
      '    GELIS_SAATI,'
      '    TESLIM_EDEN,'
      '    TESLIM_ALAN,'
      '    UCRET_DURUMU,'
      '    DURUMU,'
      '    ONHAZIRLIK,'
      '    BARKOD,'
      '    SILINDI,'
      '    ACIKLAMALAR,'
      '    FIRMA_ID,'
      '    ANALIZ_SONUCU,'
      '    LOTNO'
      ')'
      'VALUES('
      '    :ID,'
      '    :NUMUNE_TURU,'
      '    :NUMUNE_ALT_TURU,'
      '    :LAB_KAYITNO,'
      '    :GONDEREN,'
      '    :NUMUNE_ALINDIGIYER,'
      '    :NUMUNE_SAHIBI,'
      '    :ETIKET_NO,'
      '    :BASVURU_NO,'
      '    :MUHUR_NO,'
      '    :GONDEREN_PERSONEL,'
      '    :MENSEI,'
      '    :ULKE,'
      '    :URUN_ID,'
      '    :URUN_MIKTARI,'
      '    :MIKTAR_BIRIM,'
      '    :GELIS_TARIHI,'
      '    :GELIS_SAATI,'
      '    :TESLIM_EDEN,'
      '    :TESLIM_ALAN,'
      '    :UCRET_DURUMU,'
      '    :DURUMU,'
      '    :ONHAZIRLIK,'
      '    :BARKOD,'
      '    :SILINDI,'
      '    :ACIKLAMALAR,'
      '    :FIRMA_ID,'
      '    :ANALIZ_SONUCU,'
      '    :LOTNO'
      ')')
    RefreshSQL.Strings = (
      'SELECT'
      '    lab_numuneler.*,urunler.urunadi_t'
      'FROM'
      'lab_numuneler'
      'left join urunler on (urunler.id=lab_numuneler.urun_id)'
      ''
      ' WHERE '
      '        LAB_NUMUNELER.ID = :OLD_ID'
      '    ')
    SelectSQL.Strings = (
      '        SELECT'
      '               ETIKET_NO,'
      '               basvuru_no,'
      '               numune_turu,'
      '               numune_alt_turu,'
      '               gelis_tarihi,'
      '               lab_kayitno,'
      '               muhur_no,'
      '               urunler.URUNADI_T,'
      '               urunler.etmenler,'
      
        '               CAST(urun_miktari AS DECIMAL(10, 2)) as urunmikta' +
        'rlari'
      '        FROM'
      '        lab_numuneler'
      '        left join urunler on (urunler.id=lab_numuneler.urun_id)'
      ''
      '  /*'
      'SELECT'
      '       basvuru_no,'
      '       numune_turu,'
      '       numune_alt_turu,'
      '       gelis_tarihi,       '
      '       list(lab_kayitno,'#39','#39') as labkayitnolar,'
      '       list(muhur_no,'#39','#39') as muhurnolar,'
      '       list(urunler.URUNADI_T,'#39','#39') as urunadlari,'
      '--       list(urun_miktari,'#39'|'#39') as urunmiktarlari'
      
        '       list(CAST(urun_miktari AS DECIMAL(10, 2)),'#39'|'#39') as urunmik' +
        'tarlari'
      'FROM'
      'lab_numuneler'
      'left join urunler on (urunler.id=lab_numuneler.urun_id)'
      'where'
      ' BASVURU_NO=:BASVURU_NO'
      'group by basvuru_no,'
      '       numune_turu,'
      '       numune_alt_turu,'
      '       gelis_tarihi'
      ''
      '*/')
    AutoUpdateOptions.UpdateTableName = 'SERTIFIKA'
    AutoUpdateOptions.KeyFields = 'ID'
    AutoUpdateOptions.GeneratorName = 'GEN_SERTIFIKA_ID'
    AutoUpdateOptions.WhenGetGenID = wgOnNewRecord
    OnNewRecord = tblNumuneKabulNewRecord
    Transaction = UniMainModule.tr
    Database = UniMainModule.db
    Left = 189
    Top = 360
  end
  object tblGonderilenBirimler: TpFIBDataSet
    UpdateSQL.Strings = (
      'UPDATE LAB_NUMUNELER'
      'SET '
      '    NUMUNE_TURU = :NUMUNE_TURU,'
      '    NUMUNE_ALT_TURU = :NUMUNE_ALT_TURU,'
      '    LAB_KAYITNO = :LAB_KAYITNO,'
      '    GONDEREN = :GONDEREN,'
      '    NUMUNE_ALINDIGIYER = :NUMUNE_ALINDIGIYER,'
      '    NUMUNE_SAHIBI = :NUMUNE_SAHIBI,'
      '    ETIKET_NO = :ETIKET_NO,'
      '    BASVURU_NO = :BASVURU_NO,'
      '    MUHUR_NO = :MUHUR_NO,'
      '    GONDEREN_PERSONEL = :GONDEREN_PERSONEL,'
      '    MENSEI = :MENSEI,'
      '    ULKE = :ULKE,'
      '    URUN_ID = :URUN_ID,'
      '    URUN_MIKTARI = :URUN_MIKTARI,'
      '    MIKTAR_BIRIM = :MIKTAR_BIRIM,'
      '    GELIS_TARIHI = :GELIS_TARIHI,'
      '    GELIS_SAATI = :GELIS_SAATI,'
      '    TESLIM_EDEN = :TESLIM_EDEN,'
      '    TESLIM_ALAN = :TESLIM_ALAN,'
      '    UCRET_DURUMU = :UCRET_DURUMU,'
      '    DURUMU = :DURUMU,'
      '    ONHAZIRLIK = :ONHAZIRLIK,'
      '    BARKOD = :BARKOD,'
      '    SILINDI = :SILINDI,'
      '    ACIKLAMALAR = :ACIKLAMALAR,'
      '    FIRMA_ID = :FIRMA_ID,'
      '    ANALIZ_SONUCU = :ANALIZ_SONUCU,'
      '    LOTNO = :LOTNO'
      'WHERE'
      '    ID = :OLD_ID'
      '    ')
    DeleteSQL.Strings = (
      'DELETE FROM'
      '    LAB_NUMUNELER'
      'WHERE'
      '        ID = :OLD_ID'
      '    ')
    InsertSQL.Strings = (
      'INSERT INTO LAB_NUMUNELER('
      '    ID,'
      '    NUMUNE_TURU,'
      '    NUMUNE_ALT_TURU,'
      '    LAB_KAYITNO,'
      '    GONDEREN,'
      '    NUMUNE_ALINDIGIYER,'
      '    NUMUNE_SAHIBI,'
      '    ETIKET_NO,'
      '    BASVURU_NO,'
      '    MUHUR_NO,'
      '    GONDEREN_PERSONEL,'
      '    MENSEI,'
      '    ULKE,'
      '    URUN_ID,'
      '    URUN_MIKTARI,'
      '    MIKTAR_BIRIM,'
      '    GELIS_TARIHI,'
      '    GELIS_SAATI,'
      '    TESLIM_EDEN,'
      '    TESLIM_ALAN,'
      '    UCRET_DURUMU,'
      '    DURUMU,'
      '    ONHAZIRLIK,'
      '    BARKOD,'
      '    SILINDI,'
      '    ACIKLAMALAR,'
      '    FIRMA_ID,'
      '    ANALIZ_SONUCU,'
      '    LOTNO'
      ')'
      'VALUES('
      '    :ID,'
      '    :NUMUNE_TURU,'
      '    :NUMUNE_ALT_TURU,'
      '    :LAB_KAYITNO,'
      '    :GONDEREN,'
      '    :NUMUNE_ALINDIGIYER,'
      '    :NUMUNE_SAHIBI,'
      '    :ETIKET_NO,'
      '    :BASVURU_NO,'
      '    :MUHUR_NO,'
      '    :GONDEREN_PERSONEL,'
      '    :MENSEI,'
      '    :ULKE,'
      '    :URUN_ID,'
      '    :URUN_MIKTARI,'
      '    :MIKTAR_BIRIM,'
      '    :GELIS_TARIHI,'
      '    :GELIS_SAATI,'
      '    :TESLIM_EDEN,'
      '    :TESLIM_ALAN,'
      '    :UCRET_DURUMU,'
      '    :DURUMU,'
      '    :ONHAZIRLIK,'
      '    :BARKOD,'
      '    :SILINDI,'
      '    :ACIKLAMALAR,'
      '    :FIRMA_ID,'
      '    :ANALIZ_SONUCU,'
      '    :LOTNO'
      ')')
    RefreshSQL.Strings = (
      'SELECT'
      '    lab_numuneler.*,urunler.urunadi_t'
      'FROM'
      'lab_numuneler'
      'left join urunler on (urunler.id=lab_numuneler.urun_id)'
      ''
      ' WHERE '
      '        LAB_NUMUNELER.ID = :OLD_ID'
      '    ')
    SelectSQL.Strings = (
      
        'select lb.birimadi , lab_numuneler.LAB_KAYITNO,atama_tarihi from' +
        ' lab_atamalar'
      
        'inner join LABORATUVAR_BIRIMLER lb on (lb.id=lab_atamalar.birim_' +
        'id)'
      
        'inner join lab_numuneler on (lab_atamalar.numune_id=lab_numunele' +
        'r.id)'
      'where'
      '    lab_numuneler.basvuru_no=:basvuru_no')
    AutoUpdateOptions.UpdateTableName = 'SERTIFIKA'
    AutoUpdateOptions.KeyFields = 'ID'
    AutoUpdateOptions.GeneratorName = 'GEN_SERTIFIKA_ID'
    AutoUpdateOptions.WhenGetGenID = wgOnNewRecord
    OnNewRecord = tblNumuneKabulNewRecord
    Transaction = UniMainModule.tr
    Database = UniMainModule.db
    Left = 189
    Top = 432
  end
  object rpGonderilenBirimler: TfrxDBDataset
    UserName = 'rpGonderilenBirimler'
    CloseDataSource = False
    DataSet = tblGonderilenBirimler
    BCDToCurrency = False
    Left = 632
    Top = 424
  end
  object tblBirimBarkod: TpFIBDataSet
    UpdateSQL.Strings = (
      'UPDATE LAB_NUMUNELER'
      'SET '
      '    NUMUNE_TURU = :NUMUNE_TURU,'
      '    NUMUNE_ALT_TURU = :NUMUNE_ALT_TURU,'
      '    LAB_KAYITNO = :LAB_KAYITNO,'
      '    GONDEREN = :GONDEREN,'
      '    NUMUNE_ALINDIGIYER = :NUMUNE_ALINDIGIYER,'
      '    NUMUNE_SAHIBI = :NUMUNE_SAHIBI,'
      '    ETIKET_NO = :ETIKET_NO,'
      '    BASVURU_NO = :BASVURU_NO,'
      '    MUHUR_NO = :MUHUR_NO,'
      '    GONDEREN_PERSONEL = :GONDEREN_PERSONEL,'
      '    MENSEI = :MENSEI,'
      '    ULKE = :ULKE,'
      '    URUN_ID = :URUN_ID,'
      '    URUN_MIKTARI = :URUN_MIKTARI,'
      '    MIKTAR_BIRIM = :MIKTAR_BIRIM,'
      '    GELIS_TARIHI = :GELIS_TARIHI,'
      '    GELIS_SAATI = :GELIS_SAATI,'
      '    TESLIM_EDEN = :TESLIM_EDEN,'
      '    TESLIM_ALAN = :TESLIM_ALAN,'
      '    UCRET_DURUMU = :UCRET_DURUMU,'
      '    DURUMU = :DURUMU,'
      '    ONHAZIRLIK = :ONHAZIRLIK,'
      '    BARKOD = :BARKOD,'
      '    SILINDI = :SILINDI,'
      '    ACIKLAMALAR = :ACIKLAMALAR,'
      '    FIRMA_ID = :FIRMA_ID,'
      '    ANALIZ_SONUCU = :ANALIZ_SONUCU,'
      '    LOTNO = :LOTNO'
      'WHERE'
      '    ID = :OLD_ID'
      '    ')
    DeleteSQL.Strings = (
      'DELETE FROM'
      '    LAB_NUMUNELER'
      'WHERE'
      '        ID = :OLD_ID'
      '    ')
    InsertSQL.Strings = (
      'INSERT INTO LAB_NUMUNELER('
      '    ID,'
      '    NUMUNE_TURU,'
      '    NUMUNE_ALT_TURU,'
      '    LAB_KAYITNO,'
      '    GONDEREN,'
      '    NUMUNE_ALINDIGIYER,'
      '    NUMUNE_SAHIBI,'
      '    ETIKET_NO,'
      '    BASVURU_NO,'
      '    MUHUR_NO,'
      '    GONDEREN_PERSONEL,'
      '    MENSEI,'
      '    ULKE,'
      '    URUN_ID,'
      '    URUN_MIKTARI,'
      '    MIKTAR_BIRIM,'
      '    GELIS_TARIHI,'
      '    GELIS_SAATI,'
      '    TESLIM_EDEN,'
      '    TESLIM_ALAN,'
      '    UCRET_DURUMU,'
      '    DURUMU,'
      '    ONHAZIRLIK,'
      '    BARKOD,'
      '    SILINDI,'
      '    ACIKLAMALAR,'
      '    FIRMA_ID,'
      '    ANALIZ_SONUCU,'
      '    LOTNO'
      ')'
      'VALUES('
      '    :ID,'
      '    :NUMUNE_TURU,'
      '    :NUMUNE_ALT_TURU,'
      '    :LAB_KAYITNO,'
      '    :GONDEREN,'
      '    :NUMUNE_ALINDIGIYER,'
      '    :NUMUNE_SAHIBI,'
      '    :ETIKET_NO,'
      '    :BASVURU_NO,'
      '    :MUHUR_NO,'
      '    :GONDEREN_PERSONEL,'
      '    :MENSEI,'
      '    :ULKE,'
      '    :URUN_ID,'
      '    :URUN_MIKTARI,'
      '    :MIKTAR_BIRIM,'
      '    :GELIS_TARIHI,'
      '    :GELIS_SAATI,'
      '    :TESLIM_EDEN,'
      '    :TESLIM_ALAN,'
      '    :UCRET_DURUMU,'
      '    :DURUMU,'
      '    :ONHAZIRLIK,'
      '    :BARKOD,'
      '    :SILINDI,'
      '    :ACIKLAMALAR,'
      '    :FIRMA_ID,'
      '    :ANALIZ_SONUCU,'
      '    :LOTNO'
      ')')
    RefreshSQL.Strings = (
      'SELECT'
      '    lab_numuneler.*,urunler.urunadi_t'
      'FROM'
      'lab_numuneler'
      'left join urunler on (urunler.id=lab_numuneler.urun_id)'
      ''
      ' WHERE '
      '        LAB_NUMUNELER.ID = :OLD_ID'
      '    ')
    SelectSQL.Strings = (
      'select DISTINCT lb.birimadi   from lab_atamalar'
      
        'inner join LABORATUVAR_BIRIMLER lb on (lb.id=lab_atamalar.birim_' +
        'id)'
      
        'inner join lab_numuneler on (lab_atamalar.numune_id=lab_numunele' +
        'r.id)'
      'where'
      '    lab_numuneler.basvuru_no=:basvuru_no')
    AutoUpdateOptions.UpdateTableName = 'SERTIFIKA'
    AutoUpdateOptions.KeyFields = 'ID'
    AutoUpdateOptions.GeneratorName = 'GEN_SERTIFIKA_ID'
    AutoUpdateOptions.WhenGetGenID = wgOnNewRecord
    OnNewRecord = tblNumuneKabulNewRecord
    Transaction = UniMainModule.tr
    Database = UniMainModule.db
    Left = 189
    Top = 496
  end
  object rpBirimBarkod: TfrxDBDataset
    UserName = 'rpBirimBarkod'
    CloseDataSource = False
    DataSet = tblBirimBarkod
    BCDToCurrency = False
    Left = 632
    Top = 488
  end
  object rpNumuneKabul: TfrxDBDataset
    UserName = 'rpNumuneKabul'
    CloseDataSource = False
    DataSet = tblNumuneKabul
    BCDToCurrency = False
    Left = 632
    Top = 296
  end
  object labSonucYazdir: TpFIBDataSet
    UpdateSQL.Strings = (
      'UPDATE LAB_NUMUNELER'
      'SET '
      '    NUMUNE_TURU = :NUMUNE_TURU,'
      '    NUMUNE_ALT_TURU = :NUMUNE_ALT_TURU,'
      '    LAB_KAYITNO = :LAB_KAYITNO,'
      '    GONDEREN = :GONDEREN,'
      '    NUMUNE_ALINDIGIYER = :NUMUNE_ALINDIGIYER,'
      '    NUMUNE_SAHIBI = :NUMUNE_SAHIBI,'
      '    ETIKET_NO = :ETIKET_NO,'
      '    BASVURU_NO = :BASVURU_NO,'
      '    MUHUR_NO = :MUHUR_NO,'
      '    GONDEREN_PERSONEL = :GONDEREN_PERSONEL,'
      '    MENSEI = :MENSEI,'
      '    ULKE = :ULKE,'
      '    URUN_ID = :URUN_ID,'
      '    URUN_MIKTARI = :URUN_MIKTARI,'
      '    MIKTAR_BIRIM = :MIKTAR_BIRIM,'
      '    GELIS_TARIHI = :GELIS_TARIHI,'
      '    GELIS_SAATI = :GELIS_SAATI,'
      '    TESLIM_EDEN = :TESLIM_EDEN,'
      '    TESLIM_ALAN = :TESLIM_ALAN,'
      '    UCRET_DURUMU = :UCRET_DURUMU,'
      '    DURUMU = :DURUMU,'
      '    ONHAZIRLIK = :ONHAZIRLIK,'
      '    BARKOD = :BARKOD,'
      '    SILINDI = :SILINDI,'
      '    ACIKLAMALAR = :ACIKLAMALAR,'
      '    FIRMA_ID = :FIRMA_ID,'
      '    ANALIZ_SONUCU = :ANALIZ_SONUCU,'
      '    LOTNO = :LOTNO'
      'WHERE'
      '    ID = :OLD_ID'
      '    ')
    DeleteSQL.Strings = (
      'DELETE FROM'
      '    LAB_NUMUNELER'
      'WHERE'
      '        ID = :OLD_ID'
      '    ')
    InsertSQL.Strings = (
      'INSERT INTO LAB_NUMUNELER('
      '    ID,'
      '    NUMUNE_TURU,'
      '    NUMUNE_ALT_TURU,'
      '    LAB_KAYITNO,'
      '    GONDEREN,'
      '    NUMUNE_ALINDIGIYER,'
      '    NUMUNE_SAHIBI,'
      '    ETIKET_NO,'
      '    BASVURU_NO,'
      '    MUHUR_NO,'
      '    GONDEREN_PERSONEL,'
      '    MENSEI,'
      '    ULKE,'
      '    URUN_ID,'
      '    URUN_MIKTARI,'
      '    MIKTAR_BIRIM,'
      '    GELIS_TARIHI,'
      '    GELIS_SAATI,'
      '    TESLIM_EDEN,'
      '    TESLIM_ALAN,'
      '    UCRET_DURUMU,'
      '    DURUMU,'
      '    ONHAZIRLIK,'
      '    BARKOD,'
      '    SILINDI,'
      '    ACIKLAMALAR,'
      '    FIRMA_ID,'
      '    ANALIZ_SONUCU,'
      '    LOTNO'
      ')'
      'VALUES('
      '    :ID,'
      '    :NUMUNE_TURU,'
      '    :NUMUNE_ALT_TURU,'
      '    :LAB_KAYITNO,'
      '    :GONDEREN,'
      '    :NUMUNE_ALINDIGIYER,'
      '    :NUMUNE_SAHIBI,'
      '    :ETIKET_NO,'
      '    :BASVURU_NO,'
      '    :MUHUR_NO,'
      '    :GONDEREN_PERSONEL,'
      '    :MENSEI,'
      '    :ULKE,'
      '    :URUN_ID,'
      '    :URUN_MIKTARI,'
      '    :MIKTAR_BIRIM,'
      '    :GELIS_TARIHI,'
      '    :GELIS_SAATI,'
      '    :TESLIM_EDEN,'
      '    :TESLIM_ALAN,'
      '    :UCRET_DURUMU,'
      '    :DURUMU,'
      '    :ONHAZIRLIK,'
      '    :BARKOD,'
      '    :SILINDI,'
      '    :ACIKLAMALAR,'
      '    :FIRMA_ID,'
      '    :ANALIZ_SONUCU,'
      '    :LOTNO'
      ')')
    RefreshSQL.Strings = (
      'SELECT'
      '    lab_numuneler.*,urunler.urunadi_t'
      'FROM'
      'lab_numuneler'
      'left join urunler on (urunler.id=lab_numuneler.urun_id)'
      ''
      ' WHERE '
      '        LAB_NUMUNELER.ID = :OLD_ID'
      '    ')
    SelectSQL.Strings = (
      
        'select lb.birimadi ,lab_atamalar.analiz_tarihi,lab_atamalar.anal' +
        'iz_methodu,lab_atamalar.analiz_sonucu,lab_atamalar.bulasik_oldug' +
        'u_etmen'
      
        '--,(select list('#39'etmen_adi'#39','#39','#39') from LAB_SONUC_ETMENLER le wher' +
        'e le.sonuc_id=lab_atamalar.id)'
      'from lab_atamalar'
      
        'inner join LABORATUVAR_BIRIMLER lb on (lb.id=lab_atamalar.birim_' +
        'id)'
      
        'inner join lab_numuneler on (lab_atamalar.numune_id=lab_numunele' +
        'r.id)'
      'where'
      '    lab_atamalar.numune_id=:idno')
    AutoUpdateOptions.UpdateTableName = 'SERTIFIKA'
    AutoUpdateOptions.KeyFields = 'ID'
    AutoUpdateOptions.GeneratorName = 'GEN_SERTIFIKA_ID'
    AutoUpdateOptions.WhenGetGenID = wgOnNewRecord
    OnNewRecord = tblNumuneKabulNewRecord
    Transaction = UniMainModule.tr
    Database = UniMainModule.db
    Left = 333
    Top = 432
  end
  object rpsonuc: TfrxDBDataset
    UserName = 'rpsonuc'
    CloseDataSource = False
    DataSet = labSonucYazdir
    BCDToCurrency = False
    Left = 744
    Top = 464
  end
  object UniPopupMenu1: TUniPopupMenu
    Left = 760
    Top = 360
    object eslimTutanaYazdr1: TUniMenuItem
      Caption = 'Teslim Tutana'#287#305' Yazd'#305'r'
      OnClick = eslimTutanaYazdr1Click
    end
    object BirimBarkoduYazdr1: TUniMenuItem
      Caption = 'Birim Barkodu Yazd'#305'r'
      OnClick = BirimBarkoduYazdr1Click
    end
    object N1: TUniMenuItem
      Caption = '-'
    end
    object AnalizSonuYazdr1: TUniMenuItem
      Caption = 'Analiz Sonu'#231' Yazd'#305'r(Normal)'
      OnClick = AnalizSonuYazdr1Click
    end
    object AnalizSonuYazdrAkredite1: TUniMenuItem
      Caption = 'Analiz Sonu'#231' Yazd'#305'r(Akredite)'
      OnClick = AnalizSonuYazdrAkredite1Click
    end
  end
  object tmpSql: TpFIBQuery
    Transaction = UniMainModule.tr
    Database = UniMainModule.db
    Left = 488
    Top = 184
  end
end

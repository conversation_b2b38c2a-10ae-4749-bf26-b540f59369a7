# 🚀 Hızlı Başlangıç Kılavuzu

## 📍 Numune Girişi Nasıl Yapılır?

### 1. **<PERSON><PERSON>ş <PERSON>**
- Tarayıcınızda `http://localhost/laboratuvar/onlinelab/` adresine gidin
- **<PERSON>llanı<PERSON><PERSON> Adı**: `admin`
- **Şifre**: `admin123`

### 2. **Numune Kabul Sayfasına Gidin**
Numune girişi için 3 farklı yol:

#### 🔹 **Yol 1: Sol Menüden**
- Sol menüde **"Numune Kabul"** linkine tıklayın

#### 🔹 **Yol 2: Dashboard'dan**
- Ana sayfada **"+ Yeni Numune"** butonuna tıklayın

#### 🔹 **Yol 3: Numune Listesinden**
- **"Numune Listesi"** → **"+ Yeni Numune"** butonuna tıklayın

### 3. **Numune Bilgilerini Doldurun**

#### 📋 **Temel Bilgiler (Zorunlu)**
- **Numune Türü**: İç Karantina / Dış Karantina
- **Ürün**: Dropdown'dan <PERSON> (Buğday, Mısır, vb.)
- **Gönderen**: Gönderen kişi/kurum adı
- **Miktar**: Sayısal değer
- **Birim**: kg, gr, ton, lt, vb.

#### 📋 **Ek Bilgiler (İsteğe Bağlı)**
- **Numune Alt Türü**: Numune türüne göre otomatik dolar
- **Lab Kayıt No**: Otomatik oluşturulur
- **Gönderen Personel**: Personel adı
- **Firma**: Dropdown'dan seçin
- **Menşei**: Ülke seçin
- **Başvuru No, Mühür No, Etiket No**: İlgili numaralar
- **Açıklama**: Ek bilgiler

### 4. **Kaydedin**
- **"Kaydet"** butonuna tıklayın
- Başarılı mesajı görünce numune sisteme kaydedilmiştir

## 📊 Numune Takibi

### **Numune Durumları**
1. **Numune Kabul** ← Yeni kayıt
2. **Ön Hazırlık** ← Birim atama sonrası
3. **Analiz Aşamasında** ← Analiz başladığında
4. **Analiz Tamamlandı** ← Sonuçlar girildiğinde
5. **Rapor Hazırlandı** ← Rapor oluşturulduğunda
6. **Teslim Edildi** ← İşlem tamamlandığında

### **Numune İşlemleri**
- **Detay Görüntüle**: Numune bilgilerini incele
- **Düzenle**: Numune bilgilerini değiştir
- **Birim Ata**: Laboratuvar birimlerine ata
- **Sonuç Gir**: Analiz sonuçlarını kaydet
- **Yazdır**: Numune bilgilerini yazdır

## 🔧 Sistem Özellikleri

### **Dashboard**
- Genel istatistikler
- Bekleyen işler
- Grafikler ve raporlar
- Son numuneler

### **Filtreleme**
- Numune türüne göre
- Duruma göre
- Tarihe göre
- Lab kayıt numarasına göre
- Gönderene göre

### **Yetki Sistemi**
- **Numune Kabul**: Yeni numune ekleme/düzenleme
- **Numune Listesi**: Numune görüntüleme
- **Ön Hazırlık**: Birim atama
- **Sonuç Giriş**: Analiz sonuçları
- **Tanımlar**: Sistem ayarları

## 🛠️ Sistem Yönetimi

### **Kullanıcı Ekleme**
1. **"Kullanıcılar"** menüsüne gidin
2. **"+ Yeni Kullanıcı"** butonuna tıklayın
3. Kullanıcı bilgilerini doldurun
4. Yetkileri seçin
5. Kaydedin

### **Ürün/Firma Ekleme**
1. **"Tanımlar"** menüsüne gidin
2. İlgili kategoriyi seçin
3. **"+ Yeni"** butonuna tıklayın
4. Bilgileri doldurun
5. Kaydedin

## 📱 Mobil Uyumluluk

Sistem responsive tasarıma sahiptir:
- **Tablet**: Tam özellik desteği
- **Telefon**: Temel işlemler
- **Masaüstü**: Tam özellik desteği

## 🔒 Güvenlik

### **Şifre Değiştirme**
1. Sağ üst köşede kullanıcı adına tıklayın
2. **"Profil"** seçin
3. Yeni şifre girin
4. Kaydedin

### **Oturum Güvenliği**
- Otomatik oturum kapatma (1 saat)
- Güvenli şifre hashleme
- Yetki tabanlı erişim

## 📞 Destek

### **Sorun Giderme**
1. **Giriş Yapamıyorum**
   - Kullanıcı adı: `admin`
   - Şifre: `admin123`
   - Caps Lock kontrolü yapın

2. **Sayfa Yüklenmiyor**
   - Veritabanı bağlantısını kontrol edin
   - Apache/PHP servislerini kontrol edin

3. **Numune Kaydedilmiyor**
   - Zorunlu alanları doldurun
   - Hata mesajlarını okuyun

### **Teknik Destek**
- Log dosyalarını kontrol edin
- Hata mesajlarını kaydedin
- Sistem yöneticisine başvurun

## 🎯 İpuçları

### **Hızlı Kullanım**
- **Ctrl+S**: Form kaydetme
- **Enter**: Arama yapma
- **Escape**: Modal kapatma

### **Verimli Çalışma**
- Filtreleri kullanın
- Toplu işlemler yapın
- Kısayol tuşlarını kullanın

### **Veri Güvenliği**
- Düzenli yedek alın
- Şifreleri güçlü tutun
- Yetkileri doğru verin

---

## 📋 Hızlı Kontrol Listesi

### ✅ **İlk Kurulum**
- [ ] Veritabanı kuruldu
- [ ] Admin girişi yapıldı
- [ ] Şifre değiştirildi
- [ ] Test numunesi eklendi

### ✅ **Günlük Kullanım**
- [ ] Yeni numuneler kaydedildi
- [ ] Birim atamaları yapıldı
- [ ] Sonuçlar girildi
- [ ] Raporlar kontrol edildi

### ✅ **Haftalık Bakım**
- [ ] Kullanıcı yetkileri kontrol edildi
- [ ] Sistem performansı kontrol edildi
- [ ] Yedek alındı
- [ ] Log dosyaları temizlendi

Bu kılavuz ile sistemi hızlıca kullanmaya başlayabilirsiniz! 🎉

<?php
/**
 * <PERSON>uç Listesi Controller
 */

require_once 'models/Numune.php';
require_once 'models/LaboratuvarBirim.php';
require_once 'controllers/AuthController.php';

class SonucListesiController {
    private $numuneModel;
    private $birimModel;
    
    public function __construct() {
        $this->numuneModel = new Numune();
        $this->birimModel = new LaboratuvarBirim();
    }
    
    // Sonuç listesi ana sayfası
    public function index() {
        AuthController::requirePermission('NUMUNE_SONUC_GIREBILIR');

        $page = $_GET['page_num'] ?? 1;
        $filters = $_GET;

        // Hazırlığı yapılan ve analizi tamamlanan numuneleri getir
        $sql = "SELECT n.*, u.urunadi_t as urun_adi, f.izahat as firma_adi,
                       a.id as atama_id, a.durum as atama_durum, a.atama_tarihi,
                       a.kabul_tarihi, a.ha<PERSON><PERSON>_tarihi, a.analiz_kabul_tarihi,
                       a.analiz_tarihi, lb.birima<PERSON>, lb.id as birim_id,
                       COUNT(se.id) as etmen_sayisi
                FROM lab_numuneler n
                LEFT JOIN urunler u ON n.urun_id = u.id
                LEFT JOIN geneltanim f ON n.firma_id = f.id
                INNER JOIN lab_atamalar a ON n.id = a.numune_id
                INNER JOIN laboratuvar_birimler lb ON a.birim_id = lb.id
                LEFT JOIN lab_sonuc_etmenler se ON n.id = se.numune_id
                WHERE n.durumu IN ('Analiz Aşamasında', 'Analiz Tamamlandı') AND n.silindi = 0";
        
        // Filtreleme
        $params = [];
        
        if (!empty($filters['lab_kayit_no'])) {
            $sql .= " AND n.LAB_KAYITNO LIKE ?";
            $params[] = '%' . $filters['lab_kayit_no'] . '%';
        }
        
        if (!empty($filters['numune_turu'])) {
            $sql .= " AND n.numune_turu = ?";
            $params[] = $filters['numune_turu'];
        }
        
        if (!empty($filters['birim_id'])) {
            $sql .= " AND a.birim_id = ?";
            $params[] = $filters['birim_id'];
        }
        
        if (!empty($filters['baslangic_tarihi'])) {
            $sql .= " AND DATE(a.analiz_tarihi) >= ?";
            $params[] = $filters['baslangic_tarihi'];
        }
        
        if (!empty($filters['bitis_tarihi'])) {
            $sql .= " AND DATE(a.analiz_tarihi) <= ?";
            $params[] = $filters['bitis_tarihi'];
        }
        
        $sql .= " GROUP BY n.id, a.id ORDER BY a.analiz_tarihi DESC";
        
        // Toplam sayı
        $countSql = str_replace('SELECT n.*, u.urunadi_t as urun_adi, f.izahat as firma_adi,
                       a.id as atama_id, a.durum as atama_durum, a.atama_tarihi,
                       a.analiz_tarihi, lb.birimadi, lb.id as birim_id,
                       COUNT(se.id) as etmen_sayisi', 'SELECT COUNT(DISTINCT n.id) as total', $sql);
        $countSql = str_replace('GROUP BY n.id, a.id ORDER BY a.analiz_tarihi DESC', '', $countSql);
        
        $totalResult = $this->numuneModel->fetchOne($countSql, $params);
        $total = $totalResult['total'] ?? 0;
        
        // Sayfalama
        $sql .= " LIMIT " . (($page - 1) * RECORDS_PER_PAGE) . ", " . RECORDS_PER_PAGE;
        $numuneler = $this->numuneModel->fetchAll($sql, $params);
        
        // Aktif birimleri getir
        $birimler = $this->birimModel->getActiveBirimler();
        
        $data = [
            'numuneler' => $numuneler,
            'pagination' => [
                'total' => $total,
                'pages' => ceil($total / RECORDS_PER_PAGE),
                'current_page' => $page
            ],
            'birimler' => $birimler,
            'filters' => $filters,
            'user' => AuthController::getCurrentUser()
        ];
        
        require_once 'views/sonuc-listesi/index.php';
    }
    
    // Sonuç detayı
    public function detay($numuneId) {
        AuthController::requirePermission('NUMUNE_GENEL_GORUNUM');
        
        $numune = $this->numuneModel->getNumuneDetails($numuneId);
        if (!$numune) {
            $_SESSION['error'] = ERROR_MESSAGES['record_not_found'];
            header('Location: index.php?page=sonuc-listesi');
            exit;
        }
        
        // Atamalar
        $atamalar = $this->numuneModel->getAtamalar($numuneId);
        
        // Etmen sonuçları
        $sql = "SELECT * FROM lab_sonuc_etmenler WHERE numune_id = ? ORDER BY ETMEN_ADI";
        $etmenSonuclari = $this->numuneModel->fetchAll($sql, [$numuneId]);
        
        // Log kayıtları
        $sql = "SELECT * FROM lab_log WHERE numune_id = ? ORDER BY tarih DESC";
        $logKayitlari = $this->numuneModel->fetchAll($sql, [$numuneId]);
        
        $data = [
            'numune' => $numune,
            'atamalar' => $atamalar,
            'etmen_sonuclari' => $etmenSonuclari,
            'log_kayitlari' => $logKayitlari,
            'user' => AuthController::getCurrentUser()
        ];
        
        require_once 'views/sonuc-listesi/detay.php';
    }
    
    // Excel export
    public function export() {
        AuthController::requirePermission('NUMUNE_GENEL_GORUNUM');
        
        $filters = $_GET;
        
        // Analizi tamamlanan numuneleri getir
        $sql = "SELECT n.LAB_KAYITNO, n.numune_turu, n.gonderen, u.urunadi_t as urun_adi, 
                       f.izahat as firma_adi, n.URUN_MIKTARI, n.MIKTAR_BIRIM,
                       lb.birimadi, a.analiz_tarihi, n.gelis_tarihi,
                       GROUP_CONCAT(CONCAT(se.ETMEN_ADI, ': ', se.BULASIKMI, 
                                          CASE WHEN se.deger IS NOT NULL THEN CONCAT(' (', se.deger, ' ', se.birim, ')') ELSE '' END) 
                                   SEPARATOR '; ') as sonuclar
                FROM lab_numuneler n
                LEFT JOIN urunler u ON n.urun_id = u.id
                LEFT JOIN geneltanim f ON n.firma_id = f.id
                INNER JOIN lab_atamalar a ON n.id = a.numune_id
                INNER JOIN laboratuvar_birimler lb ON a.birim_id = lb.id
                LEFT JOIN lab_sonuc_etmenler se ON n.id = se.numune_id
                WHERE n.durumu = 'Analiz Tamamlandı' AND n.silindi = 0";
        
        // Filtreleme (aynı mantık)
        $params = [];
        
        if (!empty($filters['lab_kayit_no'])) {
            $sql .= " AND n.LAB_KAYITNO LIKE ?";
            $params[] = '%' . $filters['lab_kayit_no'] . '%';
        }
        
        if (!empty($filters['numune_turu'])) {
            $sql .= " AND n.numune_turu = ?";
            $params[] = $filters['numune_turu'];
        }
        
        if (!empty($filters['birim_id'])) {
            $sql .= " AND a.birim_id = ?";
            $params[] = $filters['birim_id'];
        }
        
        if (!empty($filters['baslangic_tarihi'])) {
            $sql .= " AND DATE(a.analiz_tarihi) >= ?";
            $params[] = $filters['baslangic_tarihi'];
        }
        
        if (!empty($filters['bitis_tarihi'])) {
            $sql .= " AND DATE(a.analiz_tarihi) <= ?";
            $params[] = $filters['bitis_tarihi'];
        }
        
        $sql .= " GROUP BY n.id ORDER BY a.analiz_tarihi DESC";
        
        $numuneler = $this->numuneModel->fetchAll($sql, $params);
        
        // CSV export
        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment; filename="sonuc_listesi_' . date('Y-m-d') . '.csv"');
        
        $output = fopen('php://output', 'w');
        
        // UTF-8 BOM ekle
        fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
        
        // Başlıklar
        fputcsv($output, [
            'Lab Kayıt No',
            'Numune Türü',
            'Gönderen',
            'Ürün',
            'Firma',
            'Miktar',
            'Birim',
            'Laboratuvar Birimi',
            'Geliş Tarihi',
            'Analiz Tarihi',
            'Sonuçlar'
        ], ';');
        
        // Veriler
        foreach ($numuneler as $numune) {
            fputcsv($output, [
                $numune['LAB_KAYITNO'],
                $numune['numune_turu'],
                $numune['gonderen'],
                $numune['urun_adi'],
                $numune['firma_adi'],
                $numune['URUN_MIKTARI'],
                $numune['MIKTAR_BIRIM'],
                $numune['birimadi'],
                date('d.m.Y H:i', strtotime($numune['gelis_tarihi'])),
                date('d.m.Y H:i', strtotime($numune['analiz_tarihi'])),
                $numune['sonuclar']
            ], ';');
        }
        
        fclose($output);
        exit;
    }

    // Analiz kabul et
    public function analizKabulEt() {
        AuthController::requirePermission('NUMUNE_SONUC_GIREBILIR');

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Location: index.php?page=sonuc-listesi');
            exit;
        }

        $atamaIds = $_POST['atama_ids'] ?? [];
        $kullaniciId = $_SESSION['user_id'];

        if (empty($atamaIds)) {
            $_SESSION['error'] = 'Kabul edilecek analiz seçilmedi.';
            header('Location: index.php?page=sonuc-listesi');
            exit;
        }

        $basarili = 0;
        $hatali = 0;

        foreach ($atamaIds as $atamaId) {
            try {
                // Atama durumunu güncelle
                $sql = "UPDATE lab_atamalar
                        SET durum = 'Analiz Kabul Edildi',
                            analiz_kabul_tarihi = NOW(),
                            analiz_kabul_eden_kullanici = ?
                        WHERE id = ? AND durum = 'Hazırlık Yapıldı'";

                $result = $this->numuneModel->query($sql, [$kullaniciId, $atamaId]);

                if ($result) {
                    // Log ekle
                    $atamaSql = "SELECT a.numune_id, lb.birimadi
                                FROM lab_atamalar a
                                INNER JOIN laboratuvar_birimler lb ON a.birim_id = lb.id
                                WHERE a.id = ?";
                    $atama = $this->numuneModel->fetchOne($atamaSql, [$atamaId]);

                    if ($atama) {
                        $this->numuneModel->addLog(
                            $atama['numune_id'],
                            "Analiz kabul edildi - Birim: " . $atama['birimadi'],
                            $_SESSION['user_name'] ?? 'Sistem'
                        );
                    }

                    $basarili++;
                } else {
                    $hatali++;
                }
            } catch (Exception $e) {
                $hatali++;
            }
        }

        if ($basarili > 0) {
            $_SESSION['success'] = "$basarili analiz başarıyla kabul edildi.";
        }

        if ($hatali > 0) {
            $_SESSION['warning'] = "$hatali analiz kabul edilirken hata oluştu.";
        }

        header('Location: index.php?page=sonuc-listesi');
        exit;
    }

    // Sonuç girişi sayfası
    public function sonucGir($numuneId) {
        AuthController::requirePermission('NUMUNE_SONUC_GIREBILIR');

        $numune = $this->numuneModel->getNumuneDetails($numuneId);
        if (!$numune) {
            $_SESSION['error'] = ERROR_MESSAGES['record_not_found'];
            header('Location: index.php?page=sonuc-listesi');
            exit;
        }

        // Atamalar ve sonuçlar
        $atamalar = $this->numuneModel->getAtamalar($numuneId);

        // Etmen sonuçları
        $sql = "SELECT * FROM lab_sonuc_etmenler WHERE numune_id = ? ORDER BY ETMEN_ADI";
        $etmenSonuclari = $this->numuneModel->fetchAll($sql, [$numuneId]);

        $data = [
            'numune' => $numune,
            'atamalar' => $atamalar,
            'etmen_sonuclari' => $etmenSonuclari,
            'etmenler' => $this->genelTanimModel->getEtmenler(),
            'user' => AuthController::getCurrentUser()
        ];

        require_once 'views/sonuc-listesi/gir.php';
    }

    // Sonuç kaydet
    public function sonucKaydet() {
        AuthController::requirePermission('NUMUNE_SONUC_GIREBILIR');

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Location: index.php?page=sonuc-listesi');
            exit;
        }

        $numuneId = $_POST['numune_id'] ?? 0;
        $etmenAdi = $_POST['etmen_adi'] ?? '';
        $bulasikMi = $_POST['bulasik_mi'] ?? 'Hayır';
        $deger = $_POST['deger'] ?? '';
        $birim = $_POST['birim'] ?? '';

        if ($numuneId > 0 && !empty($etmenAdi)) {
            try {
                // Önce mevcut kaydı kontrol et
                $sql = "SELECT id FROM lab_sonuc_etmenler WHERE numune_id = ? AND ETMEN_ADI = ?";
                $existing = $this->numuneModel->fetchOne($sql, [$numuneId, $etmenAdi]);

                if ($existing) {
                    // Güncelle
                    $sql = "UPDATE lab_sonuc_etmenler
                            SET BULASIKMI = ?, deger = ?, birim = ?
                            WHERE id = ?";
                    $this->numuneModel->query($sql, [$bulasikMi, $deger, $birim, $existing['id']]);
                } else {
                    // Yeni kayıt
                    $sql = "INSERT INTO lab_sonuc_etmenler (numune_id, ETMEN_ADI, BULASIKMI, deger, birim)
                            VALUES (?, ?, ?, ?, ?)";
                    $this->numuneModel->query($sql, [$numuneId, $etmenAdi, $bulasikMi, $deger, $birim]);
                }

                $_SESSION['success'] = 'Etmen sonucu kaydedildi.';
            } catch (Exception $e) {
                $_SESSION['error'] = 'Kayıt sırasında hata oluştu.';
            }
        } else {
            $_SESSION['error'] = 'Gerekli alanlar eksik.';
        }

        header("Location: index.php?page=sonuc-listesi&action=gir&id=$numuneId");
        exit;
    }

    // Analiz tamamla
    public function analizTamamla() {
        AuthController::requirePermission('NUMUNE_SONUC_GIREBILIR');

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Location: index.php?page=sonuc-listesi');
            exit;
        }

        $atamaIds = $_POST['atama_ids'] ?? [];
        $kullaniciId = $_SESSION['user_id'];

        if (empty($atamaIds)) {
            $_SESSION['error'] = 'Tamamlanacak analiz seçilmedi.';
            header('Location: index.php?page=sonuc-listesi');
            exit;
        }

        $basarili = 0;
        $hatali = 0;

        foreach ($atamaIds as $atamaId) {
            try {
                // Atama durumunu güncelle
                $sql = "UPDATE lab_atamalar
                        SET durum = 'Analiz Tamamlandı',
                            analiz_tarihi = NOW()
                        WHERE id = ? AND durum = 'Analiz Kabul Edildi'";

                $result = $this->numuneModel->query($sql, [$atamaId]);

                if ($result) {
                    // Numune durumunu güncelle
                    $atamaSql = "SELECT numune_id, lb.birimadi
                                FROM lab_atamalar a
                                INNER JOIN laboratuvar_birimler lb ON a.birim_id = lb.id
                                WHERE a.id = ?";
                    $atama = $this->numuneModel->fetchOne($atamaSql, [$atamaId]);

                    if ($atama) {
                        // Numune durumunu "Analiz Tamamlandı" yap
                        $this->numuneModel->update($atama['numune_id'], ['durumu' => 'Analiz Tamamlandı']);

                        // Log ekle
                        $this->numuneModel->addLog(
                            $atama['numune_id'],
                            "Analiz tamamlandı - Birim: " . $atama['birimadi'],
                            $_SESSION['user_name'] ?? 'Sistem'
                        );
                    }

                    $basarili++;
                } else {
                    $hatali++;
                }
            } catch (Exception $e) {
                $hatali++;
            }
        }

        if ($basarili > 0) {
            $_SESSION['success'] = "$basarili analiz tamamlandı.";
        }

        if ($hatali > 0) {
            $_SESSION['warning'] = "$hatali analiz tamamlanırken hata oluştu.";
        }

        header('Location: index.php?page=sonuc-listesi');
        exit;
    }
}
?>

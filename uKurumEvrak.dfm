object frmKurumEvrak: TfrmKurumEvrak
  Left = 0
  Top = 0
  ClientHeight = 419
  ClientWidth = 808
  Caption = 'Evraklar'
  OnShow = UniFormShow
  OldCreateOrder = False
  MonitoredKeys.Keys = <>
  PixelsPerInch = 96
  TextHeight = 13
  object UniPanel1: TUniPanel
    Left = 0
    Top = 0
    Width = 808
    Height = 32
    Align = alTop
    TabOrder = 0
    BorderStyle = ubsNone
    Color = 10418406
    object UniLabel2: TUniLabel
      Left = 39
      Top = 11
      Width = 48
      Height = 14
      Caption = 'Evraklar'
      ParentFont = False
      Font.Color = clBlack
      Font.Height = -12
      Font.Style = [fsBold]
      TabOrder = 1
    end
    object UniImage3: TUniImage
      Left = 0
      Top = 0
      Width = 33
      Height = 32
      Cursor = crHandPoint
      Center = True
      Picture.Data = {
        07544269746D617036030000424D360300000000000036000000280000001000
        000010000000010018000000000000030000C40E0000C40E0000000000000000
        0000FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
        FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFCFCFCFCCCCCCCCCCCCCCCCCCCCCC
        CCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCD0D0D0FFFFFFFF
        FFFF4E9DD34398D24094D03E92CF3E92CE3F92CE3F92CE3F92CE3F92CE3F92CE
        3F92CE3F92CE3F93CF4C9AD1F1F1F1FFFFFF4499D23F94D0ABFBFF9BF3FF92F1
        FF93F1FF93F1FF93F1FF93F1FF93F1FF93F1FF93F1FFA6F8FF65B8E3B2CADAFF
        FFFF4398D24FA6D98EDAF5A2EEFF82E5FE84E5FE84E5FE85E6FE85E6FE85E6FE
        85E6FE84E6FE96EBFF8CD8F570A7CFFFFFFF4296D16BBEE86DBDE6BBF2FF75DE
        FD77DEFC78DEFC7BDFFC7DDFFC7DDFFC7DDFFC7CDFFC80E0FDADF0FF4D9DD3F1
        F1F14095D08AD7F544A1D8DDFDFFDAFAFFDBFAFFDEFAFF74DCFC76DBFA75DAFA
        74DAFA74DAFA72D9FAA1E8FF7CBFE6B3CADB3E94D0ABF0FF449DD6368CCB368C
        CB368CCB378BCB5CBEEA6FD9FB6AD6FA68D5F967D4F966D4F982DEFCAAE0F66F
        A6CE3D92CFB9F4FF73DBFB6BCCF26CCDF36CCEF36DCEF3479CD456BAE9DAF8FF
        D7F6FFD6F6FFD5F6FFD5F7FFDBFCFF3E94D03C92CFC0F3FF71DAFB74DBFB75DB
        FC75DBFC76DCFC73DAFA449CD4378CCB368CCB358CCC348DCC3890CE3D94D052
        A0D63B92CFCAF6FF69D5F96CD5F96BD5F969D5F969D5FA6AD7FB68D4FA5EC7F1
        5EC7F25DC8F2B4E3F83D94D0B0D1E8FFFFFF3B92CFD5F7FF60D1F961D0F8B4EB
        FDD9F6FFDAF8FFDAF8FFDBF9FFDCFAFFDCFAFFDCFBFFE0FFFF3E95D0DAEBF6FF
        FFFF3D94D0DCFCFFD8F7FFD8F7FFDBFAFF358ECD3991CE3A92CF3A92CF3A92CF
        3A92CF3B92CF3D94D060A8D9FFFFFFFFFFFF7DB8E03D94D03A92CF3A92CF3D94
        D063A9D9FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
        FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
        FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
        FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
        FFFF}
      Align = alLeft
      Transparent = True
    end
  end
  object UniDBGrid3: TUniDBGrid
    AlignWithMargins = True
    Left = 5
    Top = 37
    Width = 798
    Height = 334
    Margins.Left = 5
    Margins.Top = 5
    Margins.Right = 5
    Margins.Bottom = 5
    DataSource = dsEvrak
    Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgConfirmDelete]
    LoadMask.Message = 'L'#195#188'tfen Bekleyiniz...'
    StripeRows = False
    Align = alClient
    TabOrder = 1
    Columns = <
      item
        FieldName = 'KONU'
        Title.Caption = 'Konu'
        Width = 219
      end
      item
        FieldName = 'ACIKLAMASI'
        Title.Caption = 'A'#231#305'klamas'#305
        Width = 250
      end
      item
        FieldName = 'DOSYAADI'
        Title.Caption = 'Dosya Ad'#305
        Width = 135
      end
      item
        FieldName = 'EKLEMEZAMANI'
        Title.Caption = 'Ekleme Zaman'#305
        Width = 136
      end>
  end
  object UniPanel5: TUniPanel
    Left = 0
    Top = 376
    Width = 808
    Height = 43
    Align = alBottom
    TabOrder = 2
    BorderStyle = ubsNone
    Color = 16447736
    object btnDuzenle: TUniSpeedButton
      AlignWithMargins = True
      Left = 644
      Top = 3
      Width = 80
      Height = 37
      Cursor = crHandPoint
      Caption = 'D'#252'zenle'
      Align = alRight
      ParentColor = False
      Color = clBtnFace
      TabOrder = 1
      OnClick = btnDuzenleClick
    end
    object UniSpeedButton4: TUniSpeedButton
      AlignWithMargins = True
      Left = 730
      Top = 3
      Width = 75
      Height = 37
      Cursor = crHandPoint
      Caption = 'Kapat'
      Align = alRight
      ParentColor = False
      Color = clBtnFace
      TabOrder = 2
      OnClick = UniSpeedButton4Click
    end
    object btnYeni: TUniSpeedButton
      AlignWithMargins = True
      Left = 563
      Top = 3
      Width = 75
      Height = 37
      Cursor = crHandPoint
      Caption = 'Yeni'
      Align = alRight
      ParentColor = False
      Color = clBtnFace
      TabOrder = 3
      OnClick = btnYeniClick
    end
    object lblDosyaAc: TUniLabel
      Left = 8
      Top = 16
      Width = 63
      Height = 13
      Cursor = crHandPoint
      Caption = 'Dosyay'#305' '#304'ndir'
      ParentFont = False
      Font.Color = clBlue
      Font.Style = [fsUnderline]
      TabOrder = 4
      OnClick = lblDosyaAcClick
    end
    object lblDurum: TUniLabel
      Left = 160
      Top = 14
      Width = 41
      Height = 13
      Visible = False
      Caption = 'lblDurum'
      TabOrder = 5
    end
  end
  object tblEvrak: TpFIBDataSet
    UpdateSQL.Strings = (
      'UPDATE DILEKCE_EVRAK'
      'SET '
      '    KONU = :KONU,'
      '    ACIKLAMASI = :ACIKLAMASI,'
      '    DOSYAADI = :DOSYAADI,'
      '    EKLEMEZAMANI = :EKLEMEZAMANI'
      'WHERE'
      '    ID = :OLD_ID'
      '    ')
    DeleteSQL.Strings = (
      'DELETE FROM'
      '    DILEKCE_EVRAK'
      'WHERE'
      '        ID = :OLD_ID'
      '    ')
    InsertSQL.Strings = (
      'INSERT INTO DILEKCE_EVRAK('
      '    ID,'
      '    DILEKCEID,'
      '    KONU,'
      '    ACIKLAMASI,'
      '    DOSYAADI,'
      '    EKLEMEZAMANI'
      ')'
      'VALUES('
      '    :ID,'
      '    :DILEKCEID,'
      '    :KONU,'
      '    :ACIKLAMASI,'
      '    :DOSYAADI,'
      '    :EKLEMEZAMANI'
      ')')
    RefreshSQL.Strings = (
      'select * from DILEKCE_EVRAK'
      'where(  dilekceid=:id'
      '     ) and (     DILEKCE_EVRAK.ID = :OLD_ID'
      '     )'
      '    ')
    SelectSQL.Strings = (
      'select * from DILEKCE_EVRAK'
      'where dilekceid=:id')
    AutoUpdateOptions.UpdateTableName = 'DILEKCE_EVRAK'
    AutoUpdateOptions.KeyFields = 'ID'
    AutoUpdateOptions.GeneratorName = 'GEN_DILEKCE_EVRAK_ID'
    AutoUpdateOptions.WhenGetGenID = wgOnNewRecord
    OnNewRecord = tblEvrakNewRecord
    Transaction = UniMainModule.tr
    Database = UniMainModule.db
    Left = 272
    Top = 160
  end
  object dsEvrak: TDataSource
    DataSet = tblEvrak
    OnDataChange = dsEvrakDataChange
    Left = 336
    Top = 160
  end
end

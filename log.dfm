object frmLog: TfrmLog
  Left = 0
  Top = 0
  ClientHeight = 413
  ClientWidth = 743
  Caption = 'Log'
  OldCreateOrder = False
  MonitoredKeys.Keys = <>
  PixelsPerInch = 96
  TextHeight = 13
  object UniDBGrid1: TUniDBGrid
    AlignWithMargins = True
    Left = 3
    Top = 3
    Width = 737
    Height = 363
    DataSource = dsLog
    ReadOnly = True
    WebOptions.Paged = False
    WebOptions.FetchAll = True
    LoadMask.Message = 'Yukleniyor...'
    ForceFit = True
    Align = alClient
    TabOrder = 0
    Columns = <
      item
        FieldName = 'ISLEM'
        Title.Caption = #304#351'lem'
        Width = 200
      end
      item
        FieldName = 'ISLEM_TARIHI'
        Title.Caption = #304#351'lem Tarihi'
        Width = 117
      end
      item
        FieldName = 'ISLEM_SAATI'
        Title.Caption = #304#351'lem Saati'
        Width = 70
      end
      item
        FieldName = 'ISLEMI_YAPAN'
        Title.Caption = #304#351'lemi Yapan'
        Width = 111
      end
      item
        FieldName = 'ONCEKI_DEGER'
        Title.Caption = #214'nceki De'#287'er'
        Width = 148
      end>
  end
  object UniPanel1: TUniPanel
    Left = 0
    Top = 369
    Width = 743
    Height = 44
    Align = alBottom
    TabOrder = 1
    ExplicitLeft = -230
    ExplicitWidth = 907
    object UniButton6: TUniButton
      AlignWithMargins = True
      Left = 649
      Top = 4
      Width = 90
      Height = 36
      Caption = 'Kapat'
      Align = alRight
      TabOrder = 1
      OnClick = UniButton6Click
      ExplicitLeft = 584
      ExplicitTop = 3
    end
  end
  object tblLog: TpFIBDataSet
    UpdateSQL.Strings = (
      'UPDATE LAB_NUMUNELER'
      'SET '
      '    NUMUNE_TURU = :NUMUNE_TURU,'
      '    NUMUNE_ALT_TURU = :NUMUNE_ALT_TURU,'
      '    LAB_KAYITNO = :LAB_KAYITNO,'
      '    GONDEREN = :GONDEREN,'
      '    NUMUNE_ALINDIGIYER = :NUMUNE_ALINDIGIYER,'
      '    NUMUNE_SAHIBI = :NUMUNE_SAHIBI,'
      '    ETIKET_NO = :ETIKET_NO,'
      '    BASVURU_NO = :BASVURU_NO,'
      '    MUHUR_NO = :MUHUR_NO,'
      '    GONDEREN_PERSONEL = :GONDEREN_PERSONEL,'
      '    MENSEI = :MENSEI,'
      '    ULKE = :ULKE,'
      '    URUN_ID = :URUN_ID,'
      '    URUN_MIKTARI = :URUN_MIKTARI,'
      '    MIKTAR_BIRIM = :MIKTAR_BIRIM,'
      '    GELIS_TARIHI = :GELIS_TARIHI,'
      '    GELIS_SAATI = :GELIS_SAATI,'
      '    TESLIM_EDEN = :TESLIM_EDEN,'
      '    TESLIM_ALAN = :TESLIM_ALAN,'
      '    UCRET_DURUMU = :UCRET_DURUMU,'
      '    DURUMU = :DURUMU,'
      '    ONHAZIRLIK = :ONHAZIRLIK'
      'WHERE'
      '    ID = :OLD_ID'
      '    ')
    DeleteSQL.Strings = (
      'DELETE FROM'
      '    LAB_NUMUNELER'
      'WHERE'
      '        ID = :OLD_ID'
      '    ')
    InsertSQL.Strings = (
      'INSERT INTO LAB_NUMUNELER('
      '    ID,'
      '    NUMUNE_TURU,'
      '    NUMUNE_ALT_TURU,'
      '    LAB_KAYITNO,'
      '    GONDEREN,'
      '    NUMUNE_ALINDIGIYER,'
      '    NUMUNE_SAHIBI,'
      '    ETIKET_NO,'
      '    BASVURU_NO,'
      '    MUHUR_NO,'
      '    GONDEREN_PERSONEL,'
      '    MENSEI,'
      '    ULKE,'
      '    URUN_ID,'
      '    URUN_MIKTARI,'
      '    MIKTAR_BIRIM,'
      '    GELIS_TARIHI,'
      '    GELIS_SAATI,'
      '    TESLIM_EDEN,'
      '    TESLIM_ALAN,'
      '    UCRET_DURUMU,'
      '    DURUMU,'
      '    ONHAZIRLIK'
      ')'
      'VALUES('
      '    :ID,'
      '    :NUMUNE_TURU,'
      '    :NUMUNE_ALT_TURU,'
      '    :LAB_KAYITNO,'
      '    :GONDEREN,'
      '    :NUMUNE_ALINDIGIYER,'
      '    :NUMUNE_SAHIBI,'
      '    :ETIKET_NO,'
      '    :BASVURU_NO,'
      '    :MUHUR_NO,'
      '    :GONDEREN_PERSONEL,'
      '    :MENSEI,'
      '    :ULKE,'
      '    :URUN_ID,'
      '    :URUN_MIKTARI,'
      '    :MIKTAR_BIRIM,'
      '    :GELIS_TARIHI,'
      '    :GELIS_SAATI,'
      '    :TESLIM_EDEN,'
      '    :TESLIM_ALAN,'
      '    :UCRET_DURUMU,'
      '    :DURUMU,'
      '    :ONHAZIRLIK'
      ')')
    RefreshSQL.Strings = (
      'SELECT'
      '    lab_numuneler.*,urunler.urunadi_t'
      'FROM'
      'lab_numuneler'
      'left join urunler on (urunler.id=lab_numuneler.urun_id)'
      ''
      ' WHERE '
      '        LAB_NUMUNELER.ID = :OLD_ID'
      '    ')
    SelectSQL.Strings = (
      'SELECT'
      ' *'
      'FROM'
      'LABORATUVAR_LOG'
      'where'
      ' numune_id=:idno')
    AutoUpdateOptions.UpdateTableName = 'SERTIFIKA'
    AutoUpdateOptions.KeyFields = 'ID'
    AutoUpdateOptions.GeneratorName = 'GEN_SERTIFIKA_ID'
    AutoUpdateOptions.WhenGetGenID = wgOnNewRecord
    Transaction = UniMainModule.tr
    Database = UniMainModule.db
    Left = 168
    Top = 192
  end
  object dsLog: TDataSource
    DataSet = tblLog
    Left = 168
    Top = 256
  end
end

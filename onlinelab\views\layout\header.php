<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $pageTitle ?? APP_NAME ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- DataTables CSS -->
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css">
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.bootstrap5.min.css">
    <!-- DataTables JS -->
    <script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
    <script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>
    <!-- DataTables Buttons -->
    <script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/buttons/2.4.2/js/dataTables.buttons.min.js"></script>
    <script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.bootstrap5.min.js"></script>
    <script type="text/javascript" charset="utf8" src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
    <script type="text/javascript" charset="utf8" src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
    <script type="text/javascript" charset="utf8" src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
    <script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.html5.min.js"></script>
    <script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.print.min.js"></script>
    <!-- DataTables FixedColumns -->
    <script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/fixedcolumns/4.3.0/js/dataTables.fixedColumns.min.js"></script>
    <!-- Choices.js CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/choices.js@10.2.0/public/assets/styles/choices.min.css">
    
    <!-- DataTables CSS -->
    <link href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/fixedcolumns/4.1.0/css/fixedColumns.bootstrap5.min.css" rel="stylesheet">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Custom CSS -->
    <style>
        /* Sidebar Styles */
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 50%, #2c3e50 100%);
            box-shadow: 4px 0 15px rgba(0,0,0,0.1);
            overflow-y: auto;
        }

        /* Mobile Sidebar */
        @media (max-width: 991.98px) {
            .sidebar {
                position: fixed;
                top: 0;
                left: 0;
                z-index: 1000;
                width: 280px;
                transform: translateX(-100%);
                transition: transform 0.3s ease-in-out;
            }

            .sidebar.show {
                transform: translateX(0);
            }
        }

        .sidebar-brand {
            padding: 25px 20px;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            background: rgba(0,0,0,0.1);
        }

        .sidebar-brand h5 {
            color: #fff;
            font-weight: 700;
            font-size: 1.3rem;
            margin: 0;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .sidebar-brand small {
            color: rgba(255,255,255,0.7);
            font-size: 0.8rem;
        }

        .sidebar .nav-link {
            color: rgba(255,255,255,0.85);
            padding: 15px 20px;
            border-radius: 0;
            margin: 0;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .sidebar .nav-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 0;
            height: 100%;
            background: linear-gradient(90deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
            transition: width 0.3s ease;
            z-index: 1;
        }

        .sidebar .nav-link:hover::before,
        .sidebar .nav-link.active::before {
            width: 100%;
        }

        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: #fff;
            background: rgba(255,255,255,0.1);
            border-left-color: #3498db;
            transform: translateX(5px);
            box-shadow: inset 0 0 20px rgba(255,255,255,0.1);
        }

        .sidebar .nav-link i {
            width: 20px;
            margin-right: 12px;
            position: relative;
            z-index: 2;
            font-size: 1.1rem;
        }

        .sidebar .nav-link span {
            position: relative;
            z-index: 2;
        }

        /* Hamburger Menu */
        .hamburger-menu {
            display: none;
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1001;
            background: #2c3e50;
            border: none;
            border-radius: 8px;
            padding: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            transition: all 0.3s ease;
        }

        .hamburger-menu:hover {
            background: #34495e;
            transform: scale(1.05);
        }

        .hamburger-menu i {
            color: #fff;
            font-size: 1.2rem;
        }

        /* Overlay */
        .sidebar-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 999;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .sidebar-overlay.show {
            opacity: 1;
        }

        .main-content {
            background-color: #f8f9fa;
            min-height: 100vh;
            margin-left: 0;
            transition: margin-left 0.3s ease;
            padding: 20px;
        }

        /* Desktop Styles */
        @media (min-width: 992px) {
            .hamburger-menu {
                display: none !important;
            }

            .sidebar-overlay {
                display: none !important;
            }
        }

        /* Mobile Styles */
        @media (max-width: 991.98px) {
            .hamburger-menu {
                display: block;
            }

            .sidebar-overlay.show {
                display: block;
            }

            .main-content {
                padding: 80px 15px 20px;
            }
        }
        
        .navbar-brand {
            font-weight: bold;
            color: #667eea !important;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            transition: transform 0.2s ease;
        }
        
        .card:hover {
            transform: translateY(-2px);
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .stat-card .card-body {
            padding: 2rem;
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 8px;
            padding: 10px 20px;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        
        .table {
            border-radius: 10px;
            overflow: hidden;
        }
        
        .table thead th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            font-weight: 600;
        }
        
        .badge {
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 0.85em;
        }
        
        .alert {
            border: none;
            border-radius: 10px;
            padding: 15px 20px;
        }
        
        .form-control, .form-select {
            border-radius: 8px;
            border: 2px solid #e9ecef;
            padding: 10px 15px;
            transition: all 0.3s ease;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .page-header {
            background: white;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        
        .spinner-border {
            color: #667eea;
        }

        /* Dropdown menü düzeltmeleri */
        .dropdown-menu {
            border: none;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            border-radius: 8px;
            min-width: 160px;
            z-index: 1050;
        }

        .dropdown-menu-end {
            --bs-position: end;
        }

        .dropstart .dropdown-menu {
            right: 100%;
            left: auto;
            margin-right: 0.125rem;
        }

        .dropdown-item {
            padding: 8px 16px;
            transition: all 0.2s ease;
        }

        .dropdown-item:hover {
            background-color: #f8f9fa;
            transform: translateX(2px);
        }

        .dropdown-item i {
            width: 16px;
            text-align: center;
        }

        /* Tablo responsive düzeltmeleri */
        .table-responsive {
            border-radius: 8px;
        }

        @media (max-width: 768px) {
            .dropstart .dropdown-menu {
                position: static !important;
                transform: none !important;
                right: auto;
                left: auto;
                margin: 0;
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <!-- Hamburger Menu Button -->
    <button class="hamburger-menu" id="hamburgerMenu">
        <i class="fas fa-bars"></i>
    </button>

    <!-- Sidebar Overlay -->
    <div class="sidebar-overlay" id="sidebarOverlay"></div>

    <div class="container-fluid p-0">
        <div class="row g-0">
            <!-- Sidebar -->
            <nav class="col-lg-3 col-xl-2 d-lg-block sidebar" id="sidebar">
                <div class="sidebar-brand">
                    <h5>
                        <i class="fas fa-microscope me-2"></i>
                        Lab Yönetim
                    </h5>
                    <small>v<?= APP_VERSION ?></small>
                </div>
                    
                <?php if (isset($_SESSION['user_id'])): ?>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link <?= ($_GET['page'] ?? 'dashboard') === 'dashboard' ? 'active' : '' ?>"
                               href="index.php?page=dashboard">
                                <i class="fas fa-tachometer-alt"></i>
                                <span>Dashboard</span>
                            </a>
                        </li>

                        <?php if (isset($_SESSION['user_id']) && AuthController::checkPermission('NUMUNE_LISTESI_GOREBILIR')): ?>
                        <li class="nav-item">
                            <a class="nav-link <?= ($_GET['page'] ?? '') === 'numune-listesi' ? 'active' : '' ?>"
                               href="index.php?page=numune-listesi">
                                <i class="fas fa-list"></i>
                                <span>Numune Listesi</span>
                            </a>
                        </li>
                        <?php endif; ?>





                        <?php if (isset($_SESSION['user_id']) && AuthController::checkPermission('NUMUNE_ONHAZIRLIK_YAPABILIR')): ?>
                        <li class="nav-item">
                            <a class="nav-link <?= ($_GET['page'] ?? '') === 'onhazirlik' ? 'active' : '' ?>"
                               href="index.php?page=onhazirlik">
                                <i class="fas fa-cogs"></i>
                                <span>Ön Hazırlık</span>
                            </a>
                        </li>
                        <?php endif; ?>

                        <?php if (isset($_SESSION['user_id']) && AuthController::checkPermission('NUMUNE_SONUC_GIREBILIR')): ?>
                        <li class="nav-item">
                            <a class="nav-link <?= ($_GET['page'] ?? '') === 'sonuc-listesi' ? 'active' : '' ?>"
                               href="index.php?page=sonuc-listesi">
                                <i class="fas fa-flask"></i>
                                <span>Sonuç Yönetimi</span>
                            </a>
                        </li>
                        <?php endif; ?>

                        <?php if (isset($_SESSION['user_id']) && AuthController::checkPermission('TANIMLAMA_YAPABILIR')): ?>
                        <li class="nav-item">
                            <a class="nav-link <?= ($_GET['page'] ?? '') === 'tanimlar' ? 'active' : '' ?>"
                               href="index.php?page=tanimlar">
                                <i class="fas fa-cog"></i>
                                <span>Tanımlar</span>
                            </a>
                        </li>
                        <?php endif; ?>

                        <?php if (isset($_SESSION['user_id']) && AuthController::checkPermission('TANIMLAMA_YAPABILIR')): ?>
                        <li class="nav-item">
                            <a class="nav-link <?= ($_GET['page'] ?? '') === 'kullanicilar' ? 'active' : '' ?>"
                               href="index.php?page=kullanicilar">
                                <i class="fas fa-users"></i>
                                <span>Kullanıcılar</span>
                            </a>
                        </li>
                        <?php endif; ?>

                        <li class="nav-item">
                            <a class="nav-link <?= ($_GET['page'] ?? '') === 'raporlar' ? 'active' : '' ?>"
                               href="index.php?page=raporlar">
                                <i class="fas fa-chart-bar"></i>
                                <span>Raporlar</span>
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link <?= in_array($_GET['page'] ?? '', ['log', 'log-listesi', 'log-detay']) ? 'active' : '' ?>"
                               href="index.php?page=log-listesi">
                                <i class="fas fa-history"></i>
                                <span>İşlem Geçmişi</span>
                            </a>
                        </li>

                        <li class="nav-item mt-3" style="border-top: 1px solid rgba(255,255,255,0.1); padding-top: 15px;">
                            <a class="nav-link" href="index.php?page=logout">
                                <i class="fas fa-sign-out-alt"></i>
                                <span>Çıkış</span>
                            </a>
                        </li>
                    </ul>
                <?php endif; ?>
            </nav>

            <!-- Main content -->
            <main class="col-lg-9 col-xl-10 ms-sm-auto main-content">
                <?php if (isset($_SESSION['user_id'])): ?>
                <!-- Top navbar -->
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3">
                    <div>
                        <h4 class="mb-0"><?= $pageTitle ?? 'Dashboard' ?></h4>
                        <small class="text-muted"><?= $pageSubtitle ?? '' ?></small>
                    </div>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="dropdown">
                            <button class="btn btn-outline-secondary dropdown-toggle" type="button" 
                                    data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-user me-2"></i>
                                <?= $_SESSION['user_name'] ?? 'Kullanıcı' ?>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="index.php?page=profile">
                                    <i class="fas fa-user-edit me-2"></i>Profil
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="index.php?page=logout">
                                    <i class="fas fa-sign-out-alt me-2"></i>Çıkış
                                </a></li>
                            </ul>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
                
                <!-- Alert messages -->
                <?php if (isset($_SESSION['success'])): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i>
                        <?= $_SESSION['success'] ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    <?php unset($_SESSION['success']); ?>
                <?php endif; ?>
                
                <?php if (isset($_SESSION['error'])): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        <?= $_SESSION['error'] ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    <?php unset($_SESSION['error']); ?>
                <?php endif; ?>
                
                <?php if (isset($_SESSION['warning'])): ?>
                    <div class="alert alert-warning alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <?= $_SESSION['warning'] ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    <?php unset($_SESSION['warning']); ?>
                <?php endif; ?>
                
                <?php if (isset($_SESSION['info'])): ?>
                    <div class="alert alert-info alert-dismissible fade show" role="alert">
                        <i class="fas fa-info-circle me-2"></i>
                        <?= $_SESSION['info'] ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    <?php unset($_SESSION['info']); ?>
                <?php endif; ?>

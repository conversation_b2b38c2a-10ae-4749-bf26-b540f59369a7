<?php
/**
 * Birim Atama Controller
 */

require_once 'models/Numune.php';
require_once 'models/LaboratuvarBirim.php';
require_once 'controllers/AuthController.php';

class AtamaController {
    private $numuneModel;
    private $birimModel;
    
    public function __construct() {
        $this->numuneModel = new Numune();
        $this->birimModel = new LaboratuvarBirim();
    }
    
    // Birim atama ana sayfası - Ödenen numuneleri listeler
    public function index() {
        AuthController::requirePermission('NUMUNE_ATAMA_YAPABILIR');
        
        $page = $_GET['page_num'] ?? 1;
        $filters = [
            'ucret_durumu' => 'Ödendi',
            'durumu' => 'Numune Kabul'
        ];
        
        // Ödenen ve henüz atanmamış numuneleri getir
        $result = $this->numuneModel->getNumuneList($page, $filters);
        
        // Aktif birimleri getir
        $birimler = $this->birimModel->getActiveBirimler();
        
        $data = [
            'numuneler' => $result['numuneler'],
            'pagination' => [
                'total' => $result['total'],
                'pages' => $result['pages'],
                'current_page' => $result['current_page']
            ],
            'birimler' => $birimler,
            'user' => AuthController::getCurrentUser()
        ];
        
        require_once 'views/atama/index.php';
    }
    
    // Tekli birim atama
    public function atamaYap() {
        AuthController::requirePermission('NUMUNE_ATAMA_YAPABILIR');
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Location: index.php?page=atama');
            exit;
        }
        
        $numuneId = $_POST['numune_id'] ?? 0;
        $birimId = $_POST['birim_id'] ?? 0;
        $atayanKullanici = $_SESSION['user_id'];
        
        if ($numuneId <= 0 || $birimId <= 0) {
            $_SESSION['error'] = 'Geçersiz numune veya birim seçimi.';
            header('Location: index.php?page=atama');
            exit;
        }
        
        // Numune kontrolü
        $numune = $this->numuneModel->getById($numuneId);
        if (!$numune || $numune['UCRET_DURUMU'] !== 'Ödendi') {
            $_SESSION['error'] = 'Numune bulunamadı veya ödeme durumu uygun değil.';
            header('Location: index.php?page=atama');
            exit;
        }
        
        // Daha önce atanmış mı kontrol et
        $mevcutAtama = $this->numuneModel->fetchOne(
            "SELECT id FROM lab_atamalar WHERE numune_id = ? AND birim_id = ?",
            [$numuneId, $birimId]
        );
        
        if ($mevcutAtama) {
            $_SESSION['warning'] = 'Bu numune zaten seçilen birime atanmış.';
            header('Location: index.php?page=atama');
            exit;
        }
        
        // Atama yap
        $result = $this->numuneModel->assignToBirim($numuneId, $birimId, $atayanKullanici);
        
        if ($result) {
            $_SESSION['success'] = 'Numune başarıyla birime atandı.';
        } else {
            $_SESSION['error'] = 'Atama sırasında hata oluştu.';
        }
        
        header('Location: index.php?page=atama');
        exit;
    }
    
    // Toplu birim atama
    public function topluAtama() {
        AuthController::requirePermission('NUMUNE_ATAMA_YAPABILIR');
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Location: index.php?page=atama');
            exit;
        }
        
        $numuneIds = $_POST['numune_ids'] ?? [];
        $birimId = $_POST['birim_id'] ?? 0;
        $atayanKullanici = $_SESSION['user_id'];
        
        if (empty($numuneIds) || $birimId <= 0) {
            $_SESSION['error'] = 'Numune ve birim seçimi yapılmalıdır.';
            header('Location: index.php?page=atama');
            exit;
        }
        
        $basarili = 0;
        $hatali = 0;
        
        foreach ($numuneIds as $numuneId) {
            // Numune kontrolü
            $numune = $this->numuneModel->getById($numuneId);
            if (!$numune || $numune['UCRET_DURUMU'] !== 'Ödendi') {
                $hatali++;
                continue;
            }
            
            // Daha önce atanmış mı kontrol et
            $mevcutAtama = $this->numuneModel->fetchOne(
                "SELECT id FROM lab_atamalar WHERE numune_id = ? AND birim_id = ?",
                [$numuneId, $birimId]
            );
            
            if (!$mevcutAtama) {
                $result = $this->numuneModel->assignToBirim($numuneId, $birimId, $atayanKullanici);
                if ($result) {
                    $basarili++;
                } else {
                    $hatali++;
                }
            } else {
                $hatali++;
            }
        }
        
        if ($basarili > 0) {
            $_SESSION['success'] = "$basarili numune başarıyla atandı.";
        }
        
        if ($hatali > 0) {
            $_SESSION['warning'] = "$hatali numune atanırken hata oluştu veya zaten atanmış.";
        }
        
        header('Location: index.php?page=atama');
        exit;
    }
}
?>

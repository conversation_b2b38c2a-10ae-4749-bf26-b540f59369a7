-- <PERSON><PERSON><PERSON> alan<PERSON> lab_numuneler tablosuna ekle
-- Bu script mevcut veritabanını günceller

USE laboratuvar_db;

-- <PERSON><PERSON><PERSON> alan<PERSON> ekle (hata verirse zaten mevcut demektir)
-- ulke alanı
SET @sql = 'ALTER TABLE lab_numuneler ADD COLUMN ulke VARCHAR(100) AFTER mensei';
SET @sql_check = (SELECT COUNT(*) FROM information_schema.columns WHERE table_schema = 'laboratuvar_db' AND table_name = 'lab_numuneler' AND column_name = 'ulke');
SET @sql = IF(@sql_check = 0, @sql, 'SELECT "ulke alanı zaten mevcut" as Mesaj');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- numune_alindigiyer alanı
SET @sql = 'ALTER TABLE lab_numuneler ADD COLUMN numune_alindigiyer VARCHAR(255) AFTER ulke';
SET @sql_check = (SELECT COUNT(*) FROM information_schema.columns WHERE table_schema = 'laboratuvar_db' AND table_name = 'lab_numuneler' AND column_name = 'numune_alindigiyer');
SET @sql = IF(@sql_check = 0, @sql, 'SELECT "numune_alindigiyer alanı zaten mevcut" as Mesaj');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- numune_sahibi alanı
SET @sql = 'ALTER TABLE lab_numuneler ADD COLUMN numune_sahibi VARCHAR(255) AFTER numune_alindigiyer';
SET @sql_check = (SELECT COUNT(*) FROM information_schema.columns WHERE table_schema = 'laboratuvar_db' AND table_name = 'lab_numuneler' AND column_name = 'numune_sahibi');
SET @sql = IF(@sql_check = 0, @sql, 'SELECT "numune_sahibi alanı zaten mevcut" as Mesaj');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- lotno alanı
SET @sql = 'ALTER TABLE lab_numuneler ADD COLUMN lotno VARCHAR(50) AFTER etiket_no';
SET @sql_check = (SELECT COUNT(*) FROM information_schema.columns WHERE table_schema = 'laboratuvar_db' AND table_name = 'lab_numuneler' AND column_name = 'lotno');
SET @sql = IF(@sql_check = 0, @sql, 'SELECT "lotno alanı zaten mevcut" as Mesaj');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- teslim_eden alanı
SET @sql = 'ALTER TABLE lab_numuneler ADD COLUMN teslim_eden VARCHAR(100) AFTER birim';
SET @sql_check = (SELECT COUNT(*) FROM information_schema.columns WHERE table_schema = 'laboratuvar_db' AND table_name = 'lab_numuneler' AND column_name = 'teslim_eden');
SET @sql = IF(@sql_check = 0, @sql, 'SELECT "teslim_eden alanı zaten mevcut" as Mesaj');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- teslim_alan alanı
SET @sql = 'ALTER TABLE lab_numuneler ADD COLUMN teslim_alan VARCHAR(100) AFTER teslim_eden';
SET @sql_check = (SELECT COUNT(*) FROM information_schema.columns WHERE table_schema = 'laboratuvar_db' AND table_name = 'lab_numuneler' AND column_name = 'teslim_alan');
SET @sql = IF(@sql_check = 0, @sql, 'SELECT "teslim_alan alanı zaten mevcut" as Mesaj');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- gelis_saati alanı
SET @sql = 'ALTER TABLE lab_numuneler ADD COLUMN gelis_saati TIME AFTER gelis_tarihi';
SET @sql_check = (SELECT COUNT(*) FROM information_schema.columns WHERE table_schema = 'laboratuvar_db' AND table_name = 'lab_numuneler' AND column_name = 'gelis_saati');
SET @sql = IF(@sql_check = 0, @sql, 'SELECT "gelis_saati alanı zaten mevcut" as Mesaj');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

SELECT "Veritabanı güncelleme tamamlandı!" as Mesaj;

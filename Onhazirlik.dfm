object frmOnhazirlik: TfrmOnhazirlik
  Left = 0
  Top = 0
  ClientHeight = 591
  ClientWidth = 1142
  Caption = 'Numune Haz'#305'rl'#305'k'
  OnShow = UniFormShow
  WindowState = wsMaximized
  OldCreateOrder = False
  OnClose = UniFormClose
  MonitoredKeys.Keys = <>
  DesignSize = (
    1142
    591)
  PixelsPerInch = 96
  TextHeight = 13
  object UniPanel1: TUniPanel
    Left = 0
    Top = 547
    Width = 1142
    Height = 44
    Align = alBottom
    TabOrder = 0
    object UniButton3: TUniButton
      AlignWithMargins = True
      Left = 103
      Top = 4
      Width = 135
      Height = 36
      Visible = False
      Caption = #214'n Haz'#305'rl'#305'k Yap'#305'lmad'#305
      Align = alLeft
      TabOrder = 1
      OnClick = UniButton3Click
    end
    object UniButton5: TUniButton
      AlignWithMargins = True
      Left = 907
      Top = 4
      Width = 135
      Height = 36
      Caption = 'Haz'#305'rl'#305'k Yap'#305'ld'#305
      Align = alRight
      TabOrder = 2
      OnClick = UniButton5Click
    end
    object UniButton6: TUniButton
      AlignWithMargins = True
      Left = 1048
      Top = 4
      Width = 90
      Height = 36
      Caption = 'Kapat'
      Align = alRight
      TabOrder = 3
      OnClick = UniButton6Click
    end
    object UniButton2: TUniButton
      AlignWithMargins = True
      Left = 244
      Top = 4
      Width = 112
      Height = 36
      Caption = 'Geri  G'#246'nder'
      Align = alLeft
      TabOrder = 4
      OnClick = UniButton2Click
    end
    object UniButton4: TUniButton
      AlignWithMargins = True
      Left = 362
      Top = 4
      Width = 112
      Height = 36
      Caption = 'Yazd'#305'r'
      Align = alLeft
      TabOrder = 5
      OnClick = UniButton4Click
    end
    object UniButton9: TUniButton
      AlignWithMargins = True
      Left = 4
      Top = 4
      Width = 93
      Height = 36
      Caption = 'Kabul Et'
      Align = alLeft
      TabOrder = 6
      OnClick = UniButton9Click
    end
  end
  object UniDBGrid1: TUniDBGrid
    AlignWithMargins = True
    Left = 3
    Top = 79
    Width = 1136
    Height = 465
    DataSource = dsNumuneKabul
    Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgRowSelect, dgCheckSelect, dgConfirmDelete, dgMultiSelect, dgAutoRefreshRow, dgDontShowSelected]
    ReadOnly = True
    LoadMask.Message = 'Yukleniyor...'
    Align = alClient
    TabOrder = 1
    OnDrawColumnCell = UniDBGrid1DrawColumnCell
    Columns = <
      item
        FieldName = 'ONHAZIRLIKCI_KABULETTI'
        Title.Caption = 'Haz'#305'rl'#305'k'#231#305' Kabul Etti'
        Width = 84
        Alignment = taLeftJustify
        CheckBoxField.BooleanFieldOnly = False
        CheckBoxField.FieldValues = '1;0'
        CheckBoxField.DisplayValues = 'Evet;Hay'#305'r'
      end
      item
        FieldName = 'SONUCCU_KABULETTI'
        Title.Caption = 'Birim Kabul Etti'
        Width = 84
        Alignment = taLeftJustify
        CheckBoxField.BooleanFieldOnly = False
        CheckBoxField.FieldValues = '1;0'
        CheckBoxField.DisplayValues = 'Evet;Hay'#305'r'
      end
      item
        FieldName = 'ONHAZIRLIK'
        Title.Caption = #214'n Haz'#305'rl'#305'k'
        Width = 112
      end
      item
        FieldName = 'atamabirimadi'
        Title.Caption = 'Birim Ad'#305
        Width = 100
      end
      item
        FieldName = 'GELIS_TARIHI'
        Title.Caption = 'Numune Geli'#351' Tarihi'
        Width = 110
      end
      item
        FieldName = 'GELIS_SAATI'
        Title.Caption = 'Numune Geli'#351' Saati'
        Width = 105
      end
      item
        FieldName = 'NUMUNE_TURU'
        Title.Caption = 'Numune T'#252'r'#252
        Width = 123
      end
      item
        FieldName = 'NUMUNE_ALT_TURU'
        Title.Caption = 'Alt T'#252'r'#252
        Width = 124
      end
      item
        FieldName = 'LAB_KAYITNO'
        Title.Caption = 'Lab.Kay'#305't No'
        Width = 136
      end
      item
        FieldName = 'ETIKET_NO'
        Title.Caption = 'Etiket No'
        Width = 80
      end
      item
        FieldName = 'BASVURU_NO'
        Title.Caption = 'Ba'#351'vuru No'
        Width = 80
      end
      item
        FieldName = 'MUHUR_NO'
        Title.Caption = 'M'#252'h'#252'r No'
        Width = 80
      end
      item
        FieldName = 'URUNADI_T'
        Title.Caption = #220'r'#252'n Ad'#305
        Width = 204
      end
      item
        FieldName = 'URUN_MIKTARI'
        Title.Caption = #220'r'#252'n Miktar'#305
        Width = 100
      end
      item
        FieldName = 'MIKTAR_BIRIM'
        Title.Caption = 'Miktar Birim'
        Width = 80
      end
      item
        FieldName = 'SONUCCU_KABULTARIHI'
        Title.Caption = 'Birim Kabul Tarihi'
        Width = 150
      end
      item
        FieldName = 'SONUCCU_KABULSAATI'
        Title.Caption = 'Birim Kabul Saati'
        Width = 150
      end
      item
        FieldName = 'ONHAZIRLIKCI_KABULTARIHI'
        Title.Caption = #214'n Haz'#305'rl'#305'k Kabul Tarihi'
        Width = 99
      end
      item
        FieldName = 'ONHAZIRLIKCI_KABULSAATI'
        Title.Caption = #214'n Haz'#305'rl'#305'k Kabul Saati'
        Width = 88
      end
      item
        FieldName = 'ONHAZIRLIKCI_HAZYAPTARIH'
        Title.Caption = 'Haz'#305'rl'#305'k Yap'#305'ld'#305'-Tarih'
        Width = 150
      end
      item
        FieldName = 'ONHAZIRLIKCI_HAZYAPSAAT'
        Title.Caption = 'Haz'#305'rl'#305'k Yap'#305'ld'#305'-Saat'
        Width = 150
      end
      item
        FieldName = 'KULLANILAN_NUMUNE'
        Title.Caption = 'Kalan Numune Miktar'#305
        Width = 64
      end
      item
        WidgetColumn.Enabled = True
        WidgetColumn.Widget = UniButtonWidget2
        Title.Caption = ' '
        Width = 64
      end>
  end
  object UniPanel2: TUniPanel
    Left = 0
    Top = 0
    Width = 1142
    Height = 76
    Align = alTop
    TabOrder = 2
    BorderStyle = ubsNone
    object chkOnhazirlik: TUniRadioButton
      Left = 9
      Top = 10
      Width = 185
      Height = 17
      Caption = #214'n Haz'#305'rl'#305'k Yap'#305'lanlar'
      TabOrder = 1
    end
    object chkOnhazirlikOlmayan: TUniRadioButton
      Left = 9
      Top = 32
      Width = 185
      Height = 17
      Checked = True
      Caption = #214'n Haz'#305'rl'#305'k Yap'#305'lmayanlar'
      TabOrder = 2
    end
    object chkTumu: TUniRadioButton
      Left = 9
      Top = 54
      Width = 113
      Height = 17
      Caption = 'T'#252'm'#252
      TabOrder = 3
    end
    object UniDateTimePicker1: TUniDateTimePicker
      Left = 264
      Top = 31
      Width = 120
      DateTime = 45320.000000000000000000
      DateFormat = 'dd/MM/yyyy'
      TimeFormat = 'HH:mm:ss'
      TabOrder = 4
    end
    object UniLabel1: TUniLabel
      Left = 264
      Top = 12
      Width = 73
      Height = 13
      Caption = 'Ba'#351'lang'#305#231' Tarihi'
      TabOrder = 5
    end
    object UniDateTimePicker2: TUniDateTimePicker
      Left = 393
      Top = 31
      Width = 120
      DateTime = 45320.000000000000000000
      DateFormat = 'dd/MM/yyyy'
      TimeFormat = 'HH:mm:ss'
      TabOrder = 6
    end
    object UniLabel2: TUniLabel
      Left = 393
      Top = 12
      Width = 48
      Height = 13
      Caption = 'Biti'#351' Tarihi'
      TabOrder = 7
    end
    object UniButton1: TUniButton
      Left = 783
      Top = 12
      Width = 75
      Height = 41
      Caption = 'Listele'
      TabOrder = 8
      OnClick = UniButton1Click
    end
    object UniButton10: TUniButton
      AlignWithMargins = True
      Left = 982
      Top = 5
      Width = 157
      Height = 56
      Margins.Left = 16
      Margins.Top = 5
      Margins.Bottom = 15
      Caption = 'Ekran Ayarlar'#305'n'#305' Kaydet'
      Align = alRight
      TabOrder = 9
      OnClick = UniButton10Click
      ExplicitLeft = 1112
      ExplicitTop = 3
      ExplicitHeight = 43
    end
    object edtMuhurNo: TUniEdit
      Left = 648
      Top = 31
      Width = 121
      TabOrder = 10
    end
    object edtKayitNo: TUniEdit
      Left = 525
      Top = 31
      Width = 121
      TabOrder = 11
    end
    object UniLabel3: TUniLabel
      Left = 526
      Top = 14
      Width = 40
      Height = 13
      Caption = 'Kay'#305't No'
      TabOrder = 12
    end
    object UniLabel4: TUniLabel
      Left = 649
      Top = 14
      Width = 46
      Height = 13
      Caption = 'M'#252'h'#252'r No'
      TabOrder = 13
    end
  end
  object pnlYazdir: TUniPanel
    Left = 488
    Top = 136
    Width = 289
    Height = 273
    Visible = False
    Anchors = []
    TabOrder = 3
    TitleVisible = True
    TitleAlign = taCenter
    Title = 'Birimler'
    object UniPanel4: TUniPanel
      Left = 1
      Top = 232
      Width = 287
      Height = 40
      Align = alBottom
      TabOrder = 1
      BorderStyle = ubsNone
      Color = 16447736
      object UniButton7: TUniButton
        AlignWithMargins = True
        Left = 128
        Top = 3
        Width = 75
        Height = 34
        Caption = 'Yazd'#305'r'
        Align = alRight
        TabOrder = 1
        OnClick = UniButton7Click
      end
      object UniButton8: TUniButton
        AlignWithMargins = True
        Left = 209
        Top = 3
        Width = 75
        Height = 34
        Caption = 'Kapat'
        Align = alRight
        TabOrder = 2
        OnClick = UniButton8Click
      end
    end
    object UniDBGrid2: TUniDBGrid
      AlignWithMargins = True
      Left = 4
      Top = 4
      Width = 281
      Height = 225
      DataSource = dsBirimler
      Options = [dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgConfirmDelete, dgAutoRefreshRow]
      WebOptions.Paged = False
      WebOptions.FetchAll = True
      LoadMask.Message = 'Loading data...'
      ForceFit = True
      Align = alClient
      Font.Height = -19
      ParentFont = False
      TabOrder = 2
      Columns = <
        item
          FieldName = 'birimadi'
          Title.Caption = 'birimadi'
          Width = 64
        end>
    end
  end
  object UniHiddenPanel1: TUniHiddenPanel
    Left = 720
    Top = 240
    Width = 160
    Height = 256
    Visible = True
    object UniButtonWidget2: TUniButtonWidget
      Left = 3
      Top = 40
      Width = 135
      Height = 32
      ShowCaption = True
      ShowValue = False
      OnClick = UniButtonWidget2Click
      Caption = 'Kalan'#305' Gir'
    end
  end
  object dsNumuneKabul: TDataSource
    DataSet = tblNumuneKabul
    Left = 168
    Top = 256
  end
  object tblNumuneKabul: TpFIBDataSet
    UpdateSQL.Strings = (
      'UPDATE LAB_NUMUNELER'
      'SET '
      '    NUMUNE_TURU = :NUMUNE_TURU,'
      '    NUMUNE_ALT_TURU = :NUMUNE_ALT_TURU,'
      '    LAB_KAYITNO = :LAB_KAYITNO,'
      '    GONDEREN = :GONDEREN,'
      '    NUMUNE_ALINDIGIYER = :NUMUNE_ALINDIGIYER,'
      '    NUMUNE_SAHIBI = :NUMUNE_SAHIBI,'
      '    ETIKET_NO = :ETIKET_NO,'
      '    BASVURU_NO = :BASVURU_NO,'
      '    MUHUR_NO = :MUHUR_NO,'
      '    GONDEREN_PERSONEL = :GONDEREN_PERSONEL,'
      '    MENSEI = :MENSEI,'
      '    ULKE = :ULKE,'
      '    URUN_ID = :URUN_ID,'
      '    URUN_MIKTARI = :URUN_MIKTARI,'
      '    MIKTAR_BIRIM = :MIKTAR_BIRIM,'
      '    GELIS_TARIHI = :GELIS_TARIHI,'
      '    GELIS_SAATI = :GELIS_SAATI,'
      '    TESLIM_EDEN = :TESLIM_EDEN,'
      '    TESLIM_ALAN = :TESLIM_ALAN,'
      '    UCRET_DURUMU = :UCRET_DURUMU,'
      '    DURUMU = :DURUMU,'
      '    ONHAZIRLIK = :ONHAZIRLIK,'
      '    BARKOD = :BARKOD,'
      '    SILINDI = :SILINDI,'
      '    ACIKLAMALAR = :ACIKLAMALAR,'
      '    FIRMA_ID = :FIRMA_ID,'
      '    ANALIZ_SONUCU = :ANALIZ_SONUCU,'
      '    LOTNO = :LOTNO,'
      '    KULLANILAN_NUMUNE = :KULLANILAN_NUMUNE'
      'WHERE'
      '    ID = :OLD_ID'
      '    ')
    DeleteSQL.Strings = (
      'DELETE FROM'
      '    LAB_NUMUNELER'
      'WHERE'
      '        ID = :OLD_ID'
      '    ')
    InsertSQL.Strings = (
      'INSERT INTO LAB_NUMUNELER('
      '    ID,'
      '    NUMUNE_TURU,'
      '    NUMUNE_ALT_TURU,'
      '    LAB_KAYITNO,'
      '    GONDEREN,'
      '    NUMUNE_ALINDIGIYER,'
      '    NUMUNE_SAHIBI,'
      '    ETIKET_NO,'
      '    BASVURU_NO,'
      '    MUHUR_NO,'
      '    GONDEREN_PERSONEL,'
      '    MENSEI,'
      '    ULKE,'
      '    URUN_ID,'
      '    URUN_MIKTARI,'
      '    MIKTAR_BIRIM,'
      '    GELIS_TARIHI,'
      '    GELIS_SAATI,'
      '    TESLIM_EDEN,'
      '    TESLIM_ALAN,'
      '    UCRET_DURUMU,'
      '    DURUMU,'
      '    ONHAZIRLIK,'
      '    BARKOD,'
      '    SILINDI,'
      '    ACIKLAMALAR,'
      '    FIRMA_ID,'
      '    ANALIZ_SONUCU,'
      '    LOTNO,'
      '    KULLANILAN_NUMUNE'
      ')'
      'VALUES('
      '    :ID,'
      '    :NUMUNE_TURU,'
      '    :NUMUNE_ALT_TURU,'
      '    :LAB_KAYITNO,'
      '    :GONDEREN,'
      '    :NUMUNE_ALINDIGIYER,'
      '    :NUMUNE_SAHIBI,'
      '    :ETIKET_NO,'
      '    :BASVURU_NO,'
      '    :MUHUR_NO,'
      '    :GONDEREN_PERSONEL,'
      '    :MENSEI,'
      '    :ULKE,'
      '    :URUN_ID,'
      '    :URUN_MIKTARI,'
      '    :MIKTAR_BIRIM,'
      '    :GELIS_TARIHI,'
      '    :GELIS_SAATI,'
      '    :TESLIM_EDEN,'
      '    :TESLIM_ALAN,'
      '    :UCRET_DURUMU,'
      '    :DURUMU,'
      '    :ONHAZIRLIK,'
      '    :BARKOD,'
      '    :SILINDI,'
      '    :ACIKLAMALAR,'
      '    :FIRMA_ID,'
      '    :ANALIZ_SONUCU,'
      '    :LOTNO,'
      '    :KULLANILAN_NUMUNE'
      ')')
    RefreshSQL.Strings = (
      '    SELECT lab_numuneler.*,urunler.urunadi_t,'
      
        '    lab_atamalar.SONUCCU_KABULETTI,lab_atamalar.SONUCCU_KABULTAR' +
        'IHI,lab_atamalar.SONUCCU_KABULSAATI,lb.birimadi as atamabirimadi'
      
        '    ,lab_atamalar.ONHAZIRLIKCI_KABULETTI,lab_atamalar.ONHAZIRLIK' +
        'CI_KABULTARIHI,lab_atamalar.ONHAZIRLIKCI_KABULSAATI '
      '     FROM lab_numuneler'
      '    left join urunler on (urunler.id=lab_numuneler.urun_id)'
      
        '    left join lab_atamalar on (LAB_ATAMALAR.NUMUNE_ID=lab_numune' +
        'ler.id)'
      
        '    left join LABORATUVAR_BIRIMLER lb on (lb.id=lab_atamalar.bir' +
        'im_id)'
      ''
      ' WHERE '
      '        LAB_NUMUNELER.ID = :OLD_ID'
      '    ')
    SelectSQL.Strings = (
      '    SELECT lab_numuneler.*,urunler.urunadi_t,'
      
        '    lab_atamalar.SONUCCU_KABULETTI,lab_atamalar.SONUCCU_KABULTAR' +
        'IHI,lab_atamalar.SONUCCU_KABULSAATI,lb.birimadi as atamabirimadi'
      
        '    ,lab_atamalar.ONHAZIRLIKCI_KABULETTI,lab_atamalar.ONHAZIRLIK' +
        'CI_KABULTARIHI,lab_atamalar.ONHAZIRLIKCI_KABULSAATI '
      '     FROM lab_numuneler'
      '    left join urunler on (urunler.id=lab_numuneler.urun_id)'
      
        '    left join lab_atamalar on (LAB_ATAMALAR.NUMUNE_ID=lab_numune' +
        'ler.id)'
      
        '    left join LABORATUVAR_BIRIMLER lb on (lb.id=lab_atamalar.bir' +
        'im_id)')
    AutoUpdateOptions.UpdateTableName = 'SERTIFIKA'
    AutoUpdateOptions.KeyFields = 'ID'
    AutoUpdateOptions.GeneratorName = 'GEN_SERTIFIKA_ID'
    AutoUpdateOptions.WhenGetGenID = wgOnNewRecord
    Transaction = UniMainModule.tr
    Database = UniMainModule.db
    Left = 168
    Top = 192
  end
  object tblBirimler: TpFIBDataSet
    UpdateSQL.Strings = (
      'UPDATE LAB_NUMUNELER'
      'SET '
      '    NUMUNE_TURU = :NUMUNE_TURU,'
      '    NUMUNE_ALT_TURU = :NUMUNE_ALT_TURU,'
      '    LAB_KAYITNO = :LAB_KAYITNO,'
      '    GONDEREN = :GONDEREN,'
      '    NUMUNE_ALINDIGIYER = :NUMUNE_ALINDIGIYER,'
      '    NUMUNE_SAHIBI = :NUMUNE_SAHIBI,'
      '    ETIKET_NO = :ETIKET_NO,'
      '    BASVURU_NO = :BASVURU_NO,'
      '    MUHUR_NO = :MUHUR_NO,'
      '    GONDEREN_PERSONEL = :GONDEREN_PERSONEL,'
      '    MENSEI = :MENSEI,'
      '    ULKE = :ULKE,'
      '    URUN_ID = :URUN_ID,'
      '    URUN_MIKTARI = :URUN_MIKTARI,'
      '    MIKTAR_BIRIM = :MIKTAR_BIRIM,'
      '    GELIS_TARIHI = :GELIS_TARIHI,'
      '    GELIS_SAATI = :GELIS_SAATI,'
      '    TESLIM_EDEN = :TESLIM_EDEN,'
      '    TESLIM_ALAN = :TESLIM_ALAN,'
      '    UCRET_DURUMU = :UCRET_DURUMU,'
      '    DURUMU = :DURUMU,'
      '    ONHAZIRLIK = :ONHAZIRLIK,'
      '    BARKOD = :BARKOD,'
      '    SILINDI = :SILINDI,'
      '    ACIKLAMALAR = :ACIKLAMALAR,'
      '    FIRMA_ID = :FIRMA_ID'
      'WHERE'
      '    ID = :OLD_ID'
      '    ')
    DeleteSQL.Strings = (
      'DELETE FROM'
      '    LAB_NUMUNELER'
      'WHERE'
      '        ID = :OLD_ID'
      '    ')
    InsertSQL.Strings = (
      'INSERT INTO LAB_NUMUNELER('
      '    ID,'
      '    NUMUNE_TURU,'
      '    NUMUNE_ALT_TURU,'
      '    LAB_KAYITNO,'
      '    GONDEREN,'
      '    NUMUNE_ALINDIGIYER,'
      '    NUMUNE_SAHIBI,'
      '    ETIKET_NO,'
      '    BASVURU_NO,'
      '    MUHUR_NO,'
      '    GONDEREN_PERSONEL,'
      '    MENSEI,'
      '    ULKE,'
      '    URUN_ID,'
      '    URUN_MIKTARI,'
      '    MIKTAR_BIRIM,'
      '    GELIS_TARIHI,'
      '    GELIS_SAATI,'
      '    TESLIM_EDEN,'
      '    TESLIM_ALAN,'
      '    UCRET_DURUMU,'
      '    DURUMU,'
      '    ONHAZIRLIK,'
      '    BARKOD,'
      '    SILINDI,'
      '    ACIKLAMALAR,'
      '    FIRMA_ID'
      ')'
      'VALUES('
      '    :ID,'
      '    :NUMUNE_TURU,'
      '    :NUMUNE_ALT_TURU,'
      '    :LAB_KAYITNO,'
      '    :GONDEREN,'
      '    :NUMUNE_ALINDIGIYER,'
      '    :NUMUNE_SAHIBI,'
      '    :ETIKET_NO,'
      '    :BASVURU_NO,'
      '    :MUHUR_NO,'
      '    :GONDEREN_PERSONEL,'
      '    :MENSEI,'
      '    :ULKE,'
      '    :URUN_ID,'
      '    :URUN_MIKTARI,'
      '    :MIKTAR_BIRIM,'
      '    :GELIS_TARIHI,'
      '    :GELIS_SAATI,'
      '    :TESLIM_EDEN,'
      '    :TESLIM_ALAN,'
      '    :UCRET_DURUMU,'
      '    :DURUMU,'
      '    :ONHAZIRLIK,'
      '    :BARKOD,'
      '    :SILINDI,'
      '    :ACIKLAMALAR,'
      '    :FIRMA_ID'
      ')')
    RefreshSQL.Strings = (
      'SELECT'
      '    lab_numuneler.*,urunler.urunadi_t'
      'FROM'
      'lab_numuneler'
      'left join urunler on (urunler.id=lab_numuneler.urun_id)'
      ''
      ' WHERE '
      '        LAB_NUMUNELER.ID = :OLD_ID'
      '    ')
    SelectSQL.Strings = (
      'SELECT'
      '    lab_numuneler.*,urunler.urunadi_t'
      'FROM'
      'lab_numuneler'
      'left join urunler on (urunler.id=lab_numuneler.urun_id)')
    AutoUpdateOptions.UpdateTableName = 'SERTIFIKA'
    AutoUpdateOptions.KeyFields = 'ID'
    AutoUpdateOptions.GeneratorName = 'GEN_SERTIFIKA_ID'
    AutoUpdateOptions.WhenGetGenID = wgOnNewRecord
    Transaction = UniMainModule.tr
    Database = UniMainModule.db
    Left = 845
    Top = 224
  end
  object dsBirimler: TDataSource
    DataSet = tblBirimler
    Left = 845
    Top = 288
  end
  object frxReport1: TfrxReport
    Version = '5.2.12'
    DotMatrixReport = False
    IniFile = '\Software\Fast Reports'
    PreviewOptions.Buttons = [pbPrint, pbLoad, pbSave, pbExport, pbZoom, pbFind, pbOutline, pbPageSetup, pbTools, pbEdit, pbNavigator, pbExportQuick]
    PreviewOptions.Zoom = 1.000000000000000000
    PrintOptions.Printer = 'Varsay'#305'lan'
    PrintOptions.PrintOnSheet = 0
    ReportOptions.CreateDate = 45480.560149201390000000
    ReportOptions.LastChange = 45480.560149201390000000
    ScriptLanguage = 'PascalScript'
    ScriptText.Strings = (
      ''
      'begin'
      ''
      'end.')
    OnGetValue = frxReport1GetValue
    Left = 264
    Top = 232
    Datasets = <>
    Variables = <>
    Style = <>
    object Data: TfrxDataPage
      Height = 1000.000000000000000000
      Width = 1000.000000000000000000
    end
    object Page1: TfrxReportPage
      PaperWidth = 215.900000000000000000
      PaperHeight = 279.400000000000000000
      PaperSize = 1
      LeftMargin = 10.000000000000000000
      RightMargin = 10.000000000000000000
      TopMargin = 10.000000000000000000
      BottomMargin = 10.000000000000000000
      object ReportTitle1: TfrxReportTitle
        FillType = ftBrush
        Height = 22.677180000000000000
        Top = 18.897650000000000000
        Width = 740.787880000000000000
      end
      object MasterData1: TfrxMasterData
        FillType = ftBrush
        Height = 22.677180000000000000
        Top = 102.047310000000000000
        Width = 740.787880000000000000
        RowCount = 0
      end
      object PageFooter1: TfrxPageFooter
        FillType = ftBrush
        Height = 22.677180000000000000
        Top = 185.196970000000000000
        Width = 740.787880000000000000
        object Memo1: TfrxMemoView
          Left = 665.197280000000000000
          Width = 75.590600000000000000
          Height = 18.897650000000000000
          HAlign = haRight
          Memo.UTF8W = (
            '[Page#]')
        end
      end
    end
  end
  object rpnumune_genel: TfrxDBDataset
    UserName = 'rpnumune_genel'
    CloseDataSource = False
    DataSource = dsNumuneKabul
    BCDToCurrency = False
    Left = 352
    Top = 232
  end
  object frxPDFExport1: TfrxPDFExport
    UseFileCache = True
    ShowProgress = True
    OverwritePrompt = False
    DataOnly = False
    PrintOptimized = False
    Outline = False
    Background = False
    HTMLTags = True
    Quality = 95
    Transparency = False
    Author = 'FastReport'
    Subject = 'FastReport PDF export'
    ProtectionFlags = [ePrint, eModify, eCopy, eAnnot]
    HideToolbar = False
    HideMenubar = False
    HideWindowUI = False
    FitWindow = False
    CenterWindow = False
    PrintScaling = False
    PdfA = False
    Left = 320
    Top = 312
  end
  object tmpSql: TpFIBQuery
    Transaction = UniMainModule.tr
    Database = UniMainModule.db
    Left = 408
    Top = 352
  end
end

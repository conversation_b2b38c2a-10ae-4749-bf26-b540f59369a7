unit log;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics,
  Controls, Forms, uniGUITypes, uniGUIAbstractClasses,
  uniGUIClasses, uniGUIForm, uniButton, uniPanel, uniGUIBaseClasses,
  uniBasicGrid, uniDBGrid, Data.DB, FIBDataSet, pFIBDataSet;

type
  TfrmLog = class(TUniForm)
    tblLog: TpFIBDataSet;
    dsLog: TDataSource;
    UniDBGrid1: TUniDBGrid;
    UniPanel1: TUniPanel;
    UniButton6: TUniButton;
    procedure UniButton6Click(Sender: TObject);
  private
    { Private declarations }
  public
    { Public declarations }
  end;

function frmLog: TfrmLog;

implementation

{$R *.dfm}

uses
  MainModule, uniGUIApplication;

function frmLog: TfrmLog;
begin
  Result := TfrmLog(UniMainModule.GetFormInstance(TfrmLog));
end;

procedure TfrmLog.UniButton6Click(Sender: TObject);
begin
  close;
end;

end.

<?php
/**
 * <PERSON><PERSON><PERSON> G<PERSON>şi Sayfası
 */

require_once 'views/layout/header.php';

$numune = $data['numune'] ?? [];
$atamalar = $data['atamalar'] ?? [];
$etmenSonuclari = $data['etmen_sonuclari'] ?? [];
$etmenler = $data['etmenler'] ?? [];

// Mesajları göster
if (isset($_SESSION['success'])) {
    echo '<div class="alert alert-success alert-dismissible fade show" role="alert">';
    echo $_SESSION['success'];
    echo '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>';
    echo '</div>';
    unset($_SESSION['success']);
}

if (isset($_SESSION['error'])) {
    echo '<div class="alert alert-danger alert-dismissible fade show" role="alert">';
    echo $_SESSION['error'];
    echo '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>';
    echo '</div>';
    unset($_SESSION['error']);
}
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="row align-items-center">
                        <div class="col">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-flask me-2"></i>Sonuç Girişi
                            </h5>
                            <small class="text-muted">
                                Lab Kayıt No: <strong><?= safe_html($numune['LAB_KAYITNO']) ?></strong>
                            </small>
                        </div>
                        <div class="col-auto">
                            <a href="index.php?page=sonuc-listesi" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Geri Dön
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="card-body">
                    <!-- Numune Bilgileri -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h6 class="text-primary border-bottom pb-2 mb-3">
                                <i class="fas fa-info-circle me-2"></i>Numune Bilgileri
                            </h6>
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>Numune Türü:</strong></td>
                                    <td><?= safe_html($numune['numune_turu']) ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Gönderen:</strong></td>
                                    <td><?= safe_html($numune['gonderen']) ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Ürün:</strong></td>
                                    <td><?= safe_html($numune['urun_adi']) ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Miktar:</strong></td>
                                    <td><?= safe_html($numune['URUN_MIKTARI']) ?> <?= safe_html($numune['MIKTAR_BIRIM']) ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Geliş Tarihi:</strong></td>
                                    <td><?= date('d.m.Y H:i', strtotime($numune['gelis_tarihi'])) ?></td>
                                </tr>
                            </table>
                        </div>
                        
                        <div class="col-md-6">
                            <h6 class="text-primary border-bottom pb-2 mb-3">
                                <i class="fas fa-share-alt me-2"></i>Atama Bilgileri
                            </h6>
                            <?php if (!empty($atamalar)): ?>
                                <?php foreach ($atamalar as $atama): ?>
                                    <div class="card mb-2">
                                        <div class="card-body p-2">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div>
                                                    <strong><?= safe_html($atama['birimadi']) ?></strong>
                                                    <br>
                                                    <small class="text-muted">
                                                        Atama: <?= date('d.m.Y H:i', strtotime($atama['atama_tarihi'])) ?>
                                                    </small>
                                                </div>
                                                <span class="badge bg-<?= $atama['durum'] === 'Analiz Tamamlandı' ? 'success' : 'info' ?>">
                                                    <?= safe_html($atama['durum']) ?>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <p class="text-muted">Henüz atama yapılmamış.</p>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <!-- Etmen Sonuçları -->
                    <div class="row">
                        <div class="col-12">
                            <h6 class="text-primary border-bottom pb-2 mb-3">
                                <i class="fas fa-microscope me-2"></i>Etmen Sonuçları
                            </h6>
                            
                            <!-- Yeni Etmen Sonucu Ekleme -->
                            <div class="card mb-3">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">Yeni Etmen Sonucu Ekle</h6>
                                </div>
                                <div class="card-body">
                                    <form method="POST" action="index.php?page=sonuc-listesi&action=kaydet">
                                        <input type="hidden" name="numune_id" value="<?= $numune['id'] ?>">
                                        
                                        <div class="row g-3">
                                            <div class="col-md-3">
                                                <label for="etmen_adi" class="form-label">Etmen Adı</label>
                                                <select class="form-select" id="etmen_adi" name="etmen_adi" required>
                                                    <option value="">Etmen Seçin</option>
                                                    <?php if (!empty($etmenler)): ?>
                                                        <?php foreach ($etmenler as $etmen): ?>
                                                            <option value="<?= safe_html($etmen['izahat']) ?>">
                                                                <?= safe_html($etmen['izahat']) ?>
                                                            </option>
                                                        <?php endforeach; ?>
                                                    <?php else: ?>
                                                        <option value="E.coli">E.coli</option>
                                                        <option value="Salmonella">Salmonella</option>
                                                        <option value="Listeria">Listeria</option>
                                                        <option value="Koliform">Koliform</option>
                                                        <option value="Maya-Küf">Maya-Küf</option>
                                                    <?php endif; ?>
                                                </select>
                                            </div>
                                            
                                            <div class="col-md-2">
                                                <label for="bulasik_mi" class="form-label">Bulaşık mı?</label>
                                                <select class="form-select" id="bulasik_mi" name="bulasik_mi" required>
                                                    <option value="Hayır">Hayır</option>
                                                    <option value="Evet">Evet</option>
                                                </select>
                                            </div>
                                            
                                            <div class="col-md-3">
                                                <label for="deger" class="form-label">Değer</label>
                                                <input type="text" class="form-control" id="deger" name="deger" 
                                                       placeholder="Örn: <10, 100, vb.">
                                            </div>
                                            
                                            <div class="col-md-2">
                                                <label for="birim" class="form-label">Birim</label>
                                                <input type="text" class="form-control" id="birim" name="birim" 
                                                       placeholder="Örn: cfu/g">
                                            </div>
                                            
                                            <div class="col-md-2">
                                                <label class="form-label">&nbsp;</label>
                                                <button type="submit" class="btn btn-primary d-block">
                                                    <i class="fas fa-plus me-2"></i>Ekle
                                                </button>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                            
                            <!-- Mevcut Etmen Sonuçları -->
                            <?php if (!empty($etmenSonuclari)): ?>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead class="table-light">
                                            <tr>
                                                <th>Etmen Adı</th>
                                                <th>Bulaşık mı?</th>
                                                <th>Değer</th>
                                                <th>Birim</th>
                                                <th width="100">İşlemler</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($etmenSonuclari as $sonuc): ?>
                                                <tr>
                                                    <td><strong><?= safe_html($sonuc['ETMEN_ADI']) ?></strong></td>
                                                    <td>
                                                        <span class="badge bg-<?= $sonuc['BULASIKMI'] === 'Evet' ? 'danger' : 'success' ?>">
                                                            <?= safe_html($sonuc['BULASIKMI']) ?>
                                                        </span>
                                                    </td>
                                                    <td><?= safe_html($sonuc['deger']) ?></td>
                                                    <td><?= safe_html($sonuc['birim']) ?></td>
                                                    <td>
                                                        <button type="button" class="btn btn-sm btn-outline-danger" 
                                                                onclick="silEtmenSonucu(<?= $sonuc['id'] ?>)">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php else: ?>
                                <div class="text-center py-4">
                                    <i class="fas fa-flask fa-3x text-muted mb-3"></i>
                                    <h6 class="text-muted">Henüz etmen sonucu girilmemiş</h6>
                                    <p class="text-muted">Yukarıdaki formu kullanarak etmen sonuçlarını ekleyebilirsiniz.</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function silEtmenSonucu(id) {
    if (confirm('Bu etmen sonucunu silmek istediğinizden emin misiniz?')) {
        // AJAX ile silme işlemi yapılabilir
        window.location.href = 'index.php?page=sonuc-listesi&action=sil-etmen&id=' + id + '&numune_id=<?= $numune['id'] ?>';
    }
}
</script>

<?php require_once 'views/layout/footer.php'; ?>

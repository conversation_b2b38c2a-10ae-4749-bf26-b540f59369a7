object frmDashboard: TfrmDashboard
  Left = 0
  Top = 0
  ClientHeight = 631
  ClientWidth = 1185
  Caption = 'Genel G'#246'r'#252'n'#252'm'
  OnShow = UniFormShow
  OldCreateOrder = False
  MonitoredKeys.Keys = <>
  PixelsPerInch = 96
  TextHeight = 13
  object pg: TUniPageControl
    AlignWithMargins = True
    Left = 3
    Top = 3
    Width = 1179
    Height = 625
    ActivePage = tabgrafik
    Align = alClient
    TabOrder = 0
    OnChange = pgChange
    ExplicitLeft = 0
    ExplicitTop = 0
    ExplicitWidth = 1185
    ExplicitHeight = 695
    object tabgrafik: TUniTabSheet
      Caption = 'Genel G'#246'r'#252'n'#252'm'
      object UniPanel1: TUniPanel
        Left = 0
        Top = 0
        Width = 1171
        Height = 84
        Align = alTop
        TabOrder = 0
        BorderStyle = ubsNone
        Color = 16447736
        object UniLabel1: TUniLabel
          Left = 24
          Top = 19
          Width = 68
          Height = 13
          Caption = 'Ba'#351'lama Tarihi'
          TabOrder = 1
        end
        object UniLabel2: TUniLabel
          Left = 150
          Top = 19
          Width = 48
          Height = 13
          Caption = 'Biti'#351' Tarihi'
          TabOrder = 2
        end
        object edttar1: TUniDateTimePicker
          Left = 24
          Top = 38
          Width = 120
          DateTime = 45436.000000000000000000
          DateFormat = 'dd/MM/yyyy'
          TimeFormat = 'HH:mm:ss'
          TabOrder = 3
        end
        object edtTar2: TUniDateTimePicker
          Left = 150
          Top = 38
          Width = 120
          DateTime = 45436.000000000000000000
          DateFormat = 'dd/MM/yyyy'
          TimeFormat = 'HH:mm:ss'
          TabOrder = 4
        end
        object UniLabel3: TUniLabel
          Left = 276
          Top = 19
          Width = 22
          Height = 13
          Caption = 'T'#252'r'#252
          TabOrder = 5
        end
        object UniButton1: TUniButton
          AlignWithMargins = True
          Left = 1090
          Top = 6
          Width = 75
          Height = 72
          Margins.Left = 6
          Margins.Top = 6
          Margins.Right = 6
          Margins.Bottom = 6
          Caption = 'Kapat'
          Align = alRight
          TabOrder = 6
          OnClick = UniButton1Click
        end
        object UniButton2: TUniButton
          AlignWithMargins = True
          Left = 1003
          Top = 6
          Width = 75
          Height = 72
          Margins.Left = 6
          Margins.Top = 6
          Margins.Right = 6
          Margins.Bottom = 6
          Caption = 'Listele'
          Align = alRight
          TabOrder = 7
          OnClick = UniButton2Click
        end
        object cmbNumuneTuru: TUniComboBox
          Left = 276
          Top = 38
          Width = 145
          Items.Strings = (
            ''
            #304#231' Karantina'
            'D'#305#351' Karantina')
          TabOrder = 8
          IconItems = <>
        end
      end
      object UniContainerPanel1: TUniContainerPanel
        Left = 0
        Top = 84
        Width = 1171
        Height = 513
        ParentColor = False
        Align = alClient
        AutoScroll = True
        TabOrder = 1
        ScrollHeight = 513
        ScrollWidth = 1171
        object UniDBGrid1: TUniDBGrid
          Left = 11
          Top = 16
          Width = 438
          Height = 209
          HeaderTitle = 'Analiz Etmen Say'#305'lar'#305
          DataSource = dsRapor_etmensayisi
          Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgConfirmDelete, dgAutoRefreshRow]
          WebOptions.Paged = False
          WebOptions.FetchAll = True
          LoadMask.Message = 'Loading data...'
          ForceFit = True
          TabOrder = 1
          Columns = <
            item
              FieldName = 'BIRIMADI'
              Title.Caption = 'Birim Ad'#305
              Width = 163
            end
            item
              FieldName = 'Toplam_Etmen'
              Title.Caption = 'Etmen Say'#305's'#305
              Width = 70
            end
            item
              FieldName = 'Temiz'
              Title.Caption = 'Temiz'
              Width = 70
            end
            item
              FieldName = 'Bulasik'
              Title.Caption = 'Bula'#351#305'k'
              Width = 70
            end>
        end
        object UniDBGrid2: TUniDBGrid
          Left = 467
          Top = 16
          Width = 438
          Height = 209
          HeaderTitle = 'Analiz Method Raporu'
          DataSource = dsRapor_Method
          Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgConfirmDelete, dgAutoRefreshRow]
          WebOptions.Paged = False
          WebOptions.FetchAll = True
          LoadMask.Message = 'Loading data...'
          ForceFit = True
          TabOrder = 2
          Columns = <
            item
              FieldName = 'BIRIMADI'
              Title.Caption = 'Birim Ad'#305
              Width = 149
            end
            item
              FieldName = 'ANALIZ_METHODU'
              Title.Caption = 'Analiz Methodu'
              Width = 136
            end
            item
              FieldName = 'toplam_metod'
              Title.Caption = 'Miktar'
              Width = 70
            end>
        end
        object UniDBGrid3: TUniDBGrid
          Left = 911
          Top = 240
          Width = 237
          Height = 273
          HeaderTitle = #220'cret Durum Raporu'
          DataSource = ds_ucret
          Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgConfirmDelete, dgAutoRefreshRow]
          WebOptions.Paged = False
          WebOptions.FetchAll = True
          LoadMask.Message = 'Loading data...'
          ForceFit = True
          TabOrder = 3
          Columns = <
            item
              FieldName = 'ucret_durumu'
              Title.Caption = #350'ekli'
              Width = 149
            end
            item
              FieldName = 'durum'
              Title.Caption = 'Miktar'#305
              Width = 136
            end>
        end
        object UniDBGrid4: TUniDBGrid
          Left = 11
          Top = 240
          Width = 894
          Height = 273
          HeaderTitle = 'Etmenler ve Say'#305'lar'#305
          DataSource = dsEtmen
          Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgConfirmDelete, dgAutoRefreshRow]
          WebOptions.Paged = False
          WebOptions.FetchAll = True
          LoadMask.Message = 'Loading data...'
          ForceFit = True
          TabOrder = 4
          Columns = <
            item
              FieldName = 'BIRIMADI'
              Title.Caption = 'Birim Ad'#305
              Width = 222
            end
            item
              FieldName = 'ANALIZ_METHODU'
              Title.Caption = 'Analiz Metodu'
              Width = 156
            end
            item
              FieldName = 'ETMEN_ADI'
              Title.Caption = 'Etmen Ad'#305
              Width = 211
            end
            item
              FieldName = 'TOPLAM_ETMEN'
              Title.Caption = 'Toplam Say'#305
              Width = 72
            end
            item
              FieldName = 'TEMIZ'
              Title.Caption = 'Temiz'
              Width = 56
            end
            item
              FieldName = 'BULASIK'
              Title.Caption = 'Bula'#351#305'k'
              Width = 56
            end>
        end
        object UniPanel4: TUniPanel
          Left = 911
          Top = 16
          Width = 237
          Height = 209
          TabOrder = 5
          object UniLabel19: TUniLabel
            Left = 53
            Top = 44
            Width = 100
            Height = 23
            Caption = 'Analiz Say'#305's'#305
            ParentFont = False
            Font.Height = -19
            TabOrder = 1
          end
          object lblAnalizSayisi: TUniLabel
            Left = 10
            Top = 81
            Width = 214
            Height = 40
            Alignment = taCenter
            AutoSize = False
            Caption = '0'
            ParentFont = False
            Font.Height = -29
            TabOrder = 2
          end
        end
      end
    end
    object tabListe: TUniTabSheet
      Caption = 'Rapor'
      object UniPanel2: TUniPanel
        Left = 0
        Top = 0
        Width = 1171
        Height = 175
        Align = alTop
        TabOrder = 0
        ExplicitTop = -3
        object UniLabel4: TUniLabel
          Left = 24
          Top = 19
          Width = 68
          Height = 13
          Caption = 'Ba'#351'lama Tarihi'
          TabOrder = 1
        end
        object UniLabel5: TUniLabel
          Left = 150
          Top = 19
          Width = 48
          Height = 13
          Caption = 'Biti'#351' Tarihi'
          TabOrder = 2
        end
        object UniDateTimePicker1: TUniDateTimePicker
          Left = 24
          Top = 38
          Width = 120
          DateTime = 45436.000000000000000000
          DateFormat = 'dd/MM/yyyy'
          TimeFormat = 'HH:mm:ss'
          TabOrder = 3
        end
        object UniDateTimePicker2: TUniDateTimePicker
          Left = 150
          Top = 38
          Width = 120
          DateTime = 45436.000000000000000000
          DateFormat = 'dd/MM/yyyy'
          TimeFormat = 'HH:mm:ss'
          TabOrder = 4
        end
        object UniLabel6: TUniLabel
          Left = 276
          Top = 19
          Width = 22
          Height = 13
          Caption = 'T'#252'r'#252
          TabOrder = 5
        end
        object UniComboBox1: TUniComboBox
          Left = 276
          Top = 38
          Width = 145
          Items.Strings = (
            ''
            #304#231' Karantina'
            'D'#305#351' Karantina')
          TabOrder = 6
          IconItems = <>
          OnChange = UniComboBox1Change
        end
        object cmbAltTuru: TUniComboBox
          Left = 428
          Top = 37
          Width = 231
          Height = 24
          Margins.Left = 4
          Margins.Top = 4
          Margins.Right = 4
          Margins.Bottom = 4
          TabOrder = 7
          ClientEvents.ExtEvents.Strings = (
            
              'change=function change(sender, newValue, oldValue, eOpts)'#13#10'{'#13#10'  ' +
              '    for (var i=0; i<newValue.length; i++)'#13#10'    {'#13#10'       var fou' +
              'nd=false;'#13#10'       '#13#10'       for (var j=0; j<oldValue.length; j++)' +
              #13#10'         if (newValue[i]==oldValue[j])   { found=true; break;}' +
              #13#10'       '#13#10'       if (!found)  ajaxRequest(sender, "checkevent",' +
              ' [ "text="+newValue[i] ]);'#13#10'    }'#13#10#13#10'    for (var i=0; i<oldValu' +
              'e.length; i++)'#13#10'    {'#13#10'       var found=false;'#13#10'       '#13#10'       ' +
              'for (var j=0; j<newValue.length; j++)'#13#10'         if (oldValue[i]=' +
              '=newValue[j])   { found=true; break;}'#13#10'       '#13#10'       if (!foun' +
              'd)  ajaxRequest(sender, "uncheckevent", [ "text="+oldValue[i] ])' +
              ';'#13#10'    }'#13#10'}')
          ClientEvents.UniEvents.Strings = (
            
              'beforeInit=function beforeInit(sender, config)'#13#10'{'#13#10'   Ext.apply(' +
              'sender, {'#13#10'      emptyText: '#39'T'#252'm'#252#39','#13#10'      multiSelect: true,'#13#10' ' +
              '     plugins: Ext.create('#39'Ext.ux.form.plugin.CheckComboBox'#39')'#13#10'  ' +
              ' });'#13#10'}')
          FieldLabelFont.Height = -13
          CaseSensitive = True
          IconItems = <>
        end
        object UniLabel7: TUniLabel
          Left = 428
          Top = 17
          Width = 80
          Height = 13
          Caption = 'Numune Alt T'#252'r'#252
          TabOrder = 8
        end
        object cmbBirimler: TUniComboBox
          Left = 667
          Top = 36
          Width = 231
          Height = 24
          Margins.Left = 4
          Margins.Top = 4
          Margins.Right = 4
          Margins.Bottom = 4
          TabOrder = 9
          ClientEvents.ExtEvents.Strings = (
            
              'change=function change(sender, newValue, oldValue, eOpts)'#13#10'{'#13#10'  ' +
              '    for (var i=0; i<newValue.length; i++)'#13#10'    {'#13#10'       var fou' +
              'nd=false;'#13#10'       '#13#10'       for (var j=0; j<oldValue.length; j++)' +
              #13#10'         if (newValue[i]==oldValue[j])   { found=true; break;}' +
              #13#10'       '#13#10'       if (!found)  ajaxRequest(sender, "checkevent",' +
              ' [ "text="+newValue[i] ]);'#13#10'    }'#13#10#13#10'    for (var i=0; i<oldValu' +
              'e.length; i++)'#13#10'    {'#13#10'       var found=false;'#13#10'       '#13#10'       ' +
              'for (var j=0; j<newValue.length; j++)'#13#10'         if (oldValue[i]=' +
              '=newValue[j])   { found=true; break;}'#13#10'       '#13#10'       if (!foun' +
              'd)  ajaxRequest(sender, "uncheckevent", [ "text="+oldValue[i] ])' +
              ';'#13#10'    }'#13#10'}')
          ClientEvents.UniEvents.Strings = (
            
              'beforeInit=function beforeInit(sender, config)'#13#10'{'#13#10'   Ext.apply(' +
              'sender, {'#13#10'      emptyText: '#39'T'#252'm'#252#39','#13#10'      multiSelect: true,'#13#10' ' +
              '     plugins: Ext.create('#39'Ext.ux.form.plugin.CheckComboBox'#39')'#13#10'  ' +
              ' });'#13#10'}')
          FieldLabelFont.Height = -13
          CaseSensitive = True
          IconItems = <>
        end
        object UniLabel8: TUniLabel
          Left = 667
          Top = 16
          Width = 34
          Height = 13
          Caption = 'Birimler'
          TabOrder = 10
        end
        object lkbUrunAdi: TUniDBLookupComboBox
          Left = 905
          Top = 36
          Width = 250
          ListField = 'URUNADI_T'
          ListSource = dsurun
          KeyField = 'ID'
          ListFieldIndex = 0
          TabOrder = 11
          Color = clWindow
          ClientEvents.ExtEvents.Strings = (
            
              'beforerender=function beforerender(sender, eOpts)'#13#10'{'#13#10'        se' +
              'nder.allowBlank=true; '#13#10'  sender.editable = true;'#13#10'}')
        end
        object UniLabel11: TUniLabel
          Left = 905
          Top = 17
          Width = 41
          Height = 13
          Caption = #220'r'#252'n Ad'#305
          TabOrder = 12
        end
        object UniLabel9: TUniLabel
          Left = 24
          Top = 71
          Width = 48
          Height = 13
          Caption = 'Etmen Ad'#305
          TabOrder = 13
        end
        object cmbEtmen: TUniComboBox
          Left = 24
          Top = 91
          Width = 246
          Height = 24
          Margins.Left = 4
          Margins.Top = 4
          Margins.Right = 4
          Margins.Bottom = 4
          TabOrder = 14
          ClientEvents.ExtEvents.Strings = (
            
              'change=function change(sender, newValue, oldValue, eOpts)'#13#10'{'#13#10'  ' +
              '    for (var i=0; i<newValue.length; i++)'#13#10'    {'#13#10'       var fou' +
              'nd=false;'#13#10'       '#13#10'       for (var j=0; j<oldValue.length; j++)' +
              #13#10'         if (newValue[i]==oldValue[j])   { found=true; break;}' +
              #13#10'       '#13#10'       if (!found)  ajaxRequest(sender, "checkevent",' +
              ' [ "text="+newValue[i] ]);'#13#10'    }'#13#10#13#10'    for (var i=0; i<oldValu' +
              'e.length; i++)'#13#10'    {'#13#10'       var found=false;'#13#10'       '#13#10'       ' +
              'for (var j=0; j<newValue.length; j++)'#13#10'         if (oldValue[i]=' +
              '=newValue[j])   { found=true; break;}'#13#10'       '#13#10'       if (!foun' +
              'd)  ajaxRequest(sender, "uncheckevent", [ "text="+oldValue[i] ])' +
              ';'#13#10'    }'#13#10'}')
          ClientEvents.UniEvents.Strings = (
            
              'beforeInit=function beforeInit(sender, config)'#13#10'{'#13#10'   Ext.apply(' +
              'sender, {'#13#10'      emptyText: '#39'T'#252'm'#252#39','#13#10'      multiSelect: true,'#13#10' ' +
              '     plugins: Ext.create('#39'Ext.ux.form.plugin.CheckComboBox'#39')'#13#10'  ' +
              ' });'#13#10'}')
          FieldLabelFont.Height = -13
          CaseSensitive = True
          IconItems = <>
        end
        object UniLabel10: TUniLabel
          Left = 277
          Top = 71
          Width = 102
          Height = 13
          Caption = 'Laboratuvar Kay'#305't No'
          TabOrder = 15
        end
        object edtKayitNo: TUniEdit
          Left = 276
          Top = 90
          Width = 145
          TabOrder = 16
        end
        object UniEdit1: TUniEdit
          Left = 428
          Top = 90
          Width = 231
          TabOrder = 17
        end
        object UniLabel12: TUniLabel
          Left = 429
          Top = 71
          Width = 49
          Height = 13
          Caption = 'Barkod No'
          TabOrder = 18
        end
        object UniLabel13: TUniLabel
          Left = 667
          Top = 71
          Width = 96
          Height = 13
          Caption = 'G'#246'nderen '#304'nspekt'#246'r'
          TabOrder = 19
        end
        object UniDBLookupComboBox4: TUniDBLookupComboBox
          Left = 665
          Top = 90
          Width = 233
          ListField = 'ADISOYADI'
          ListSource = dsPersonel
          KeyField = 'ADISOYADI'
          ListFieldIndex = 0
          TabOrder = 20
          Color = clWindow
          ClientEvents.ExtEvents.Strings = (
            
              'beforerender=function beforerender(sender, eOpts)'#13#10'{'#13#10'          ' +
              '  sender.allowBlank=true; '#13#10'  sender.editable = true;        '#13#10'}')
        end
        object UniDBLookupComboBox5: TUniDBLookupComboBox
          Left = 905
          Top = 88
          Width = 247
          ListField = 'ULKE_T'
          ListSource = dsUlkeler
          KeyField = 'ULKE_T'
          ListFieldIndex = 0
          TabOrder = 21
          Color = clWindow
          ClientEvents.ExtEvents.Strings = (
            
              'beforerender=function beforerender(sender, eOpts)'#13#10'{'#13#10'          ' +
              'sender.allowBlank=true; '#13#10'  sender.editable = true;'#13#10'}')
        end
        object UniLabel14: TUniLabel
          Left = 908
          Top = 71
          Width = 89
          Height = 13
          Caption = 'Numunenin Men'#351'ei'
          TabOrder = 22
        end
        object UniLabel15: TUniLabel
          Left = 24
          Top = 124
          Width = 93
          Height = 13
          Caption = 'G'#246'nderen M'#252'd'#252'rl'#252'k'
          TabOrder = 23
        end
        object UniDBLookupComboBox2: TUniDBLookupComboBox
          Left = 24
          Top = 141
          Width = 246
          ListField = 'IZAHAT'
          ListSource = dsMudurlukler
          KeyField = 'IZAHAT'
          ListFieldIndex = 0
          TabOrder = 24
          Color = clWindow
        end
        object UniLabel16: TUniLabel
          Left = 276
          Top = 124
          Width = 86
          Height = 13
          Caption = 'Ba'#351'vuru Numaras'#305
          TabOrder = 25
        end
        object UniEdit2: TUniEdit
          Left = 276
          Top = 141
          Width = 145
          TabOrder = 26
        end
        object UniEdit3: TUniEdit
          Left = 428
          Top = 141
          Width = 231
          TabOrder = 27
        end
        object UniLabel17: TUniLabel
          Left = 428
          Top = 124
          Width = 77
          Height = 13
          Caption = 'M'#252'h'#252'r Numaras'#305
          TabOrder = 28
        end
        object UniEdit4: TUniEdit
          Left = 665
          Top = 141
          Width = 166
          TabOrder = 29
        end
        object UniLabel18: TUniLabel
          Left = 665
          Top = 122
          Width = 125
          Height = 13
          Caption = 'Etiket Num/ '#220'st yaz'#305' say'#305's'#305
          TabOrder = 30
        end
        object UniButton3: TUniButton
          Left = 1070
          Top = 115
          Width = 82
          Height = 50
          Caption = 'Listele'
          TabOrder = 31
          LayoutConfig.Cls = 'btnYesil'
          LayoutConfig.BodyCls = 'btnYesil'
          OnClick = UniButton3Click
        end
        object UniLabel20: TUniLabel
          Left = 841
          Top = 124
          Width = 44
          Height = 13
          Caption = 'Firma Ad'#305
          TabOrder = 32
        end
        object lkbFirmaAdi: TUniDBLookupComboBox
          Left = 836
          Top = 141
          Width = 224
          ListField = 'IZAHAT'
          ListSource = dsFirma
          KeyField = 'ID'
          ListFieldIndex = 0
          TabOrder = 33
          Color = clWindow
        end
      end
      object UniPanel3: TUniPanel
        Left = 0
        Top = 553
        Width = 1171
        Height = 44
        Align = alBottom
        TabOrder = 1
        object UniButton4: TUniButton
          AlignWithMargins = True
          Left = 4
          Top = 4
          Width = 90
          Height = 36
          Caption = 'Excel'#39'e Aktar'
          Align = alLeft
          TabOrder = 1
          OnClick = UniButton4Click
        end
        object UniButton6: TUniButton
          AlignWithMargins = True
          Left = 1077
          Top = 4
          Width = 90
          Height = 36
          Caption = 'Kapat'
          Align = alRight
          TabOrder = 2
          OnClick = UniButton6Click
        end
      end
      object UniDBGrid5: TUniDBGrid
        AlignWithMargins = True
        Left = 3
        Top = 178
        Width = 1165
        Height = 372
        DataSource = dsRapor
        Options = [dgTitles, dgIndicator, dgColumnResize, dgColumnMove, dgColLines, dgRowLines, dgConfirmDelete, dgAutoRefreshRow, dgRowNumbers]
        ReadOnly = True
        WebOptions.Paged = False
        WebOptions.FetchAll = True
        LoadMask.Message = 'Y'#252'kleniyor...'
        Align = alClient
        TabOrder = 2
        Exporter.Enabled = True
        Exporter.FileName = 'Numune_liste'
        Exporter.Title = 'Numune Listesi'
        Columns = <
          item
            FieldName = 'DURUMU'
            Title.Caption = 'Durumu'
            Width = 38
            Sortable = True
            ImageOptions.Visible = True
          end
          item
            FieldName = 'DURUMU'
            Title.Caption = 'Durumu'
            Width = 184
            Sortable = True
          end
          item
            FieldName = 'GELIS_TARIHI'
            Title.Caption = 'Geli'#351' Tarihi'
            Width = 77
            Sortable = True
          end
          item
            FieldName = 'GELIS_SAATI'
            Title.Caption = 'Geli'#351' Saati'
            Width = 74
            Sortable = True
          end
          item
            FieldName = 'NUMUNE_TURU'
            Title.Caption = 'Numune T'#252'r'#252
            Width = 123
            Sortable = True
          end
          item
            FieldName = 'NUMUNE_ALT_TURU'
            Title.Caption = 'Alt T'#252'r'#252
            Width = 124
            Sortable = True
          end
          item
            FieldName = 'atamabirim'
            Title.Caption = 'Atama Yap'#305'lan Birimler'
            Width = 120
            Sortable = True
            DisplayMemo = True
          end
          item
            FieldName = 'URUNADI_T'
            Title.Caption = #220'r'#252'n Ad'#305
            Width = 204
            Sortable = True
          end
          item
            FieldName = 'LAB_KAYITNO'
            Title.Caption = 'Lab.Kay'#305't No'
            Width = 136
            Sortable = True
          end
          item
            FieldName = 'BARKOD'
            Title.Caption = 'Barkod'
            Width = 100
            Sortable = True
          end
          item
            FieldName = 'MENSEI'
            Title.Caption = 'Men'#351'ei'
            Width = 100
            Sortable = True
          end
          item
            FieldName = 'GONDEREN'
            Title.Caption = 'G'#246'nderen Kurum'
            Width = 150
            Sortable = True
          end
          item
            FieldName = 'GONDEREN_PERSONEL'
            Title.Caption = 'G'#246'nderen Personel'
            Width = 124
            Sortable = True
          end
          item
            FieldName = 'NUMUNE_ALINDIGIYER'
            Title.Caption = 'Numunenin Al'#305'nd'#305#287#305' Yer'
            Width = 120
            Sortable = True
          end
          item
            FieldName = 'BASVURU_NO'
            Title.Caption = 'Ba'#351'vuru No'
            Width = 80
            Sortable = True
          end
          item
            FieldName = 'MUHUR_NO'
            Title.Caption = 'M'#252'h'#252'r No'
            Width = 80
            Sortable = True
          end
          item
            FieldName = 'ETIKET_NO'
            Title.Caption = 'Etiket/'#220'st Yaz'#305' No'
            Width = 80
            Sortable = True
          end
          item
            FieldName = 'URUN_MIKTARI'
            Title.Caption = #220'r'#252'n Miktar'#305
            Width = 100
            Sortable = True
          end
          item
            FieldName = 'MIKTAR_BIRIM'
            Title.Caption = 'Miktar Birim'
            Width = 80
            Sortable = True
          end
          item
            FieldName = 'TESLIM_EDEN'
            Title.Caption = 'Teslim Eden'
            Width = 100
            Sortable = True
          end
          item
            FieldName = 'TESLIM_ALAN'
            Title.Caption = 'Teslim Alan'
            Width = 100
            Sortable = True
          end
          item
            FieldName = 'ACIKLAMALAR'
            Title.Caption = 'Notlar'
            Width = 150
            Sortable = True
          end>
      end
    end
  end
  object tblRapor_etmensayisi: TpFIBDataSet
    UpdateSQL.Strings = (
      'UPDATE LAB_SONUC_ETMENLER'
      'SET '
      '    SONUC_ID = :SONUC_ID,'
      '    ETMEN_ADI = :ETMEN_ADI,'
      '    NUMUNE_ID = :NUMUNE_ID,'
      '    BULASIKMI = :BULASIKMI'
      'WHERE'
      '    ID = :OLD_ID'
      '    ')
    DeleteSQL.Strings = (
      'DELETE FROM'
      '    LAB_SONUC_ETMENLER'
      'WHERE'
      '        ID = :OLD_ID'
      '    ')
    InsertSQL.Strings = (
      'INSERT INTO LAB_SONUC_ETMENLER('
      '    ID,'
      '    SONUC_ID,'
      '    ETMEN_ADI,'
      '    NUMUNE_ID,'
      '    BULASIKMI'
      ')'
      'VALUES('
      '    :ID,'
      '    :SONUC_ID,'
      '    :ETMEN_ADI,'
      '    :NUMUNE_ID,'
      '    :BULASIKMI'
      ')')
    RefreshSQL.Strings = (
      'SELECT'
      '    LAB_SONUC_ETMENLER.*'
      'FROM'
      'LAB_SONUC_ETMENLER'
      'where      LAB_SONUC_ETMENLER.ID = :OLD_ID'
      '      '
      '')
    SelectSQL.Strings = (
      'SELECT'
      '    LAB_SONUC_ETMENLER.*'
      'FROM'
      'LAB_SONUC_ETMENLER'
      'where sonuc_id=:id')
    AutoUpdateOptions.UpdateTableName = 'SERTIFIKA'
    AutoUpdateOptions.KeyFields = 'ID'
    AutoUpdateOptions.GeneratorName = 'GEN_SERTIFIKA_ID'
    AutoUpdateOptions.WhenGetGenID = wgOnNewRecord
    Transaction = UniMainModule.tr
    Database = UniMainModule.db
    Left = 152
    Top = 168
  end
  object dsRapor_etmensayisi: TDataSource
    DataSet = tblRapor_etmensayisi
    Left = 264
    Top = 168
  end
  object dsRapor_Method: TDataSource
    DataSet = tblRapor_Method
    Left = 264
    Top = 232
  end
  object tblRapor_Method: TpFIBDataSet
    UpdateSQL.Strings = (
      'UPDATE LAB_SONUC_ETMENLER'
      'SET '
      '    SONUC_ID = :SONUC_ID,'
      '    ETMEN_ADI = :ETMEN_ADI,'
      '    NUMUNE_ID = :NUMUNE_ID,'
      '    BULASIKMI = :BULASIKMI'
      'WHERE'
      '    ID = :OLD_ID'
      '    ')
    DeleteSQL.Strings = (
      'DELETE FROM'
      '    LAB_SONUC_ETMENLER'
      'WHERE'
      '        ID = :OLD_ID'
      '    ')
    InsertSQL.Strings = (
      'INSERT INTO LAB_SONUC_ETMENLER('
      '    ID,'
      '    SONUC_ID,'
      '    ETMEN_ADI,'
      '    NUMUNE_ID,'
      '    BULASIKMI'
      ')'
      'VALUES('
      '    :ID,'
      '    :SONUC_ID,'
      '    :ETMEN_ADI,'
      '    :NUMUNE_ID,'
      '    :BULASIKMI'
      ')')
    RefreshSQL.Strings = (
      'SELECT'
      '    LAB_SONUC_ETMENLER.*'
      'FROM'
      'LAB_SONUC_ETMENLER'
      'where      LAB_SONUC_ETMENLER.ID = :OLD_ID'
      '      '
      '')
    SelectSQL.Strings = (
      'SELECT'
      '    LAB_SONUC_ETMENLER.*'
      'FROM'
      'LAB_SONUC_ETMENLER'
      'where sonuc_id=:id')
    AutoUpdateOptions.UpdateTableName = 'SERTIFIKA'
    AutoUpdateOptions.KeyFields = 'ID'
    AutoUpdateOptions.GeneratorName = 'GEN_SERTIFIKA_ID'
    AutoUpdateOptions.WhenGetGenID = wgOnNewRecord
    Transaction = UniMainModule.tr
    Database = UniMainModule.db
    Left = 152
    Top = 232
  end
  object tblUcret: TpFIBDataSet
    UpdateSQL.Strings = (
      'UPDATE LAB_SONUC_ETMENLER'
      'SET '
      '    SONUC_ID = :SONUC_ID,'
      '    ETMEN_ADI = :ETMEN_ADI,'
      '    NUMUNE_ID = :NUMUNE_ID,'
      '    BULASIKMI = :BULASIKMI'
      'WHERE'
      '    ID = :OLD_ID'
      '    ')
    DeleteSQL.Strings = (
      'DELETE FROM'
      '    LAB_SONUC_ETMENLER'
      'WHERE'
      '        ID = :OLD_ID'
      '    ')
    InsertSQL.Strings = (
      'INSERT INTO LAB_SONUC_ETMENLER('
      '    ID,'
      '    SONUC_ID,'
      '    ETMEN_ADI,'
      '    NUMUNE_ID,'
      '    BULASIKMI'
      ')'
      'VALUES('
      '    :ID,'
      '    :SONUC_ID,'
      '    :ETMEN_ADI,'
      '    :NUMUNE_ID,'
      '    :BULASIKMI'
      ')')
    RefreshSQL.Strings = (
      'SELECT'
      '    LAB_SONUC_ETMENLER.*'
      'FROM'
      'LAB_SONUC_ETMENLER'
      'where      LAB_SONUC_ETMENLER.ID = :OLD_ID'
      '      '
      '')
    SelectSQL.Strings = (
      'SELECT'
      '    LAB_SONUC_ETMENLER.*'
      'FROM'
      'LAB_SONUC_ETMENLER'
      'where sonuc_id=:id')
    AutoUpdateOptions.UpdateTableName = 'SERTIFIKA'
    AutoUpdateOptions.KeyFields = 'ID'
    AutoUpdateOptions.GeneratorName = 'GEN_SERTIFIKA_ID'
    AutoUpdateOptions.WhenGetGenID = wgOnNewRecord
    Transaction = UniMainModule.tr
    Database = UniMainModule.db
    Left = 152
    Top = 304
  end
  object ds_ucret: TDataSource
    DataSet = tblUcret
    Left = 264
    Top = 304
  end
  object tblEtmen: TpFIBDataSet
    UpdateSQL.Strings = (
      'UPDATE LAB_SONUC_ETMENLER'
      'SET '
      '    SONUC_ID = :SONUC_ID,'
      '    ETMEN_ADI = :ETMEN_ADI,'
      '    NUMUNE_ID = :NUMUNE_ID,'
      '    BULASIKMI = :BULASIKMI'
      'WHERE'
      '    ID = :OLD_ID'
      '    ')
    DeleteSQL.Strings = (
      'DELETE FROM'
      '    LAB_SONUC_ETMENLER'
      'WHERE'
      '        ID = :OLD_ID'
      '    ')
    InsertSQL.Strings = (
      'INSERT INTO LAB_SONUC_ETMENLER('
      '    ID,'
      '    SONUC_ID,'
      '    ETMEN_ADI,'
      '    NUMUNE_ID,'
      '    BULASIKMI'
      ')'
      'VALUES('
      '    :ID,'
      '    :SONUC_ID,'
      '    :ETMEN_ADI,'
      '    :NUMUNE_ID,'
      '    :BULASIKMI'
      ')')
    RefreshSQL.Strings = (
      'SELECT'
      '    LAB_SONUC_ETMENLER.*'
      'FROM'
      'LAB_SONUC_ETMENLER'
      'where      LAB_SONUC_ETMENLER.ID = :OLD_ID'
      '      '
      '')
    SelectSQL.Strings = (
      'SELECT'
      '  lnb.BIRIMADI,'
      '  ls.ANALIZ_METHODU,'
      '  etmen.ETMEN_ADI,'
      '  count(1) as toplam_etmen,'
      
        '  SUM(COALESCE(CASE WHEN etmen.BULASIKMI = '#39'Evet'#39' THEN 1 ELSE 0 ' +
        'END, 0)) AS Temiz,'
      
        '  SUM(COALESCE(CASE WHEN etmen.BULASIKMI = '#39'Hay'#305'r'#39' THEN 1 ELSE 0' +
        ' END, 0)) AS Bulasik'
      '  '
      '--  SUM(COALESCE(ls.KARANTINA_ETMEN_SAYISI, 0)) AS Toplam_Etmen,'
      'FROM'
      '  LAB_NUMUNELER l'
      'INNER JOIN LAB_ATAMALAR ls ON (ls.numune_id = l.id)'
      'INNER JOIN LABORATUVAR_BIRIMLER lnb ON (lnb.id = ls.BIRIM_ID)'
      'inner join lab_sonuc_etmenler etmen on (etmen.NUMUNE_ID=l.id)'
      'GROUP BY '
      '  lnb.BIRIMADI,ls.ANALIZ_METHODU,etmen.etmen_adi;')
    AutoUpdateOptions.UpdateTableName = 'SERTIFIKA'
    AutoUpdateOptions.KeyFields = 'ID'
    AutoUpdateOptions.GeneratorName = 'GEN_SERTIFIKA_ID'
    AutoUpdateOptions.WhenGetGenID = wgOnNewRecord
    Transaction = UniMainModule.tr
    Database = UniMainModule.db
    Left = 144
    Top = 376
  end
  object dsEtmen: TDataSource
    DataSet = tblEtmen
    Left = 272
    Top = 368
  end
  object dsurun: TDataSource
    DataSet = tblUrun
    Left = 744
    Top = 336
  end
  object tblUrun: TpFIBDataSet
    UpdateSQL.Strings = (
      'UPDATE URUNLER'
      'SET '
      '    URUNADI_T = :URUNADI_T,'
      '    URUNADI_A = :URUNADI_A,'
      '    URUNADI_I = :URUNADI_I,'
      '    URUNADI_L = :URUNADI_L,'
      '    DURUMU = :DURUMU,'
      '    PRODCODE = :PRODCODE,'
      '    KAYDEDEN = :KAYDEDEN,'
      '    DEGISTIREN = :DEGISTIREN,'
      '    KAYITZAMANI = :KAYITZAMANI,'
      '    DEGISTIRMEZAMANI = :DEGISTIRMEZAMANI,'
      '    ALTGRUPID = :ALTGRUPID,'
      '    DS_FIYATGRUBU = :DS_FIYATGRUBU,'
      '    GTIPNO = :GTIPNO'
      'WHERE'
      '    ID = :OLD_ID'
      '    ')
    DeleteSQL.Strings = (
      'DELETE FROM'
      '    URUNLER'
      'WHERE'
      '        ID = :OLD_ID'
      '    ')
    InsertSQL.Strings = (
      'INSERT INTO URUNLER('
      '    ID,'
      '    URUNADI_T,'
      '    URUNADI_A,'
      '    URUNADI_I,'
      '    URUNADI_L,'
      '    DURUMU,'
      '    PRODCODE,'
      '    KAYDEDEN,'
      '    DEGISTIREN,'
      '    KAYITZAMANI,'
      '    DEGISTIRMEZAMANI,'
      '    ALTGRUPID,'
      '    DS_FIYATGRUBU,'
      '    GTIPNO'
      ')'
      'VALUES('
      '    :ID,'
      '    :URUNADI_T,'
      '    :URUNADI_A,'
      '    :URUNADI_I,'
      '    :URUNADI_L,'
      '    :DURUMU,'
      '    :PRODCODE,'
      '    :KAYDEDEN,'
      '    :DEGISTIREN,'
      '    :KAYITZAMANI,'
      '    :DEGISTIRMEZAMANI,'
      '    :ALTGRUPID,'
      '    :DS_FIYATGRUBU,'
      '    :GTIPNO'
      ')')
    RefreshSQL.Strings = (
      'SELECT'
      '    *'
      'FROM'
      'urunler'
      ''
      ' WHERE '
      '        URUNLER.ID = :OLD_ID'
      '    ')
    SelectSQL.Strings = (
      'SELECT'
      '    *'
      'FROM'
      'urunler')
    AutoUpdateOptions.UpdateTableName = 'SERTIFIKA'
    AutoUpdateOptions.KeyFields = 'ID'
    AutoUpdateOptions.GeneratorName = 'GEN_SERTIFIKA_ID'
    AutoUpdateOptions.WhenGetGenID = wgOnNewRecord
    Transaction = UniMainModule.trtanim
    Database = UniMainModule.db
    Left = 672
    Top = 336
  end
  object UniGridExcelExporter1: TUniGridExcelExporter
    FileExtention = 'xlsx'
    MimeType = 
      'application/vnd.openxmlformats-officedocument.spreadsheetml.shee' +
      't'
    CharSet = 'UTF-8'
    Left = 392
    Top = 272
  end
  object dsPersonel: TDataSource
    DataSet = tblPersonel
    Left = 747
    Top = 400
  end
  object tblPersonel: TpFIBDataSet
    UpdateSQL.Strings = (
      'UPDATE TANIM_PERSONEL'
      'SET '
      '    ADISOYADI = :ADISOYADI,'
      '    BOLUMU = :BOLUMU,'
      '    UNVANI = :UNVANI,'
      '    EVTEL = :EVTEL,'
      '    CEPTEL = :CEPTEL,'
      '    DAHILI = :DAHILI,'
      '    EVADRESI = :EVADRESI,'
      '    DURUMU = :DURUMU'
      'WHERE'
      '    ID = :OLD_ID'
      '    ')
    DeleteSQL.Strings = (
      'DELETE FROM'
      '    TANIM_PERSONEL'
      'WHERE'
      '        ID = :OLD_ID'
      '    ')
    InsertSQL.Strings = (
      'INSERT INTO TANIM_PERSONEL('
      '    ID,'
      '    ADISOYADI,'
      '    BOLUMU,'
      '    UNVANI,'
      '    EVTEL,'
      '    CEPTEL,'
      '    DAHILI,'
      '    EVADRESI,'
      '    DURUMU'
      ')'
      'VALUES('
      '    :ID,'
      '    :ADISOYADI,'
      '    :BOLUMU,'
      '    :UNVANI,'
      '    :EVTEL,'
      '    :CEPTEL,'
      '    :DAHILI,'
      '    :EVADRESI,'
      '    :DURUMU'
      ')')
    RefreshSQL.Strings = (
      'select * from tanim_personel'
      'where(  durumu=1'
      '     ) and (     TANIM_PERSONEL.ID = :OLD_ID'
      '     )'
      '     '
      '')
    SelectSQL.Strings = (
      'select * from tanim_personel'
      'where durumu=1 '
      ''
      'order by adisoyadi COLLATE PXW_TURK'
      ''
      '--select * from geneltanim'
      '--where sahibi=7')
    AutoUpdateOptions.UpdateTableName = 'TANIM_PERSONEL'
    AutoUpdateOptions.KeyFields = 'ID'
    AutoUpdateOptions.GeneratorName = 'GEN_TANIM_PERSONEL_ID'
    AutoUpdateOptions.WhenGetGenID = wgOnNewRecord
    Transaction = UniMainModule.trtanim
    Database = UniMainModule.db
    Left = 672
    Top = 400
  end
  object dsUlkeler: TDataSource
    DataSet = tblUlkeler
    Left = 752
    Top = 456
  end
  object tblUlkeler: TpFIBDataSet
    UpdateSQL.Strings = (
      'UPDATE ULKELER'
      'SET '
      '    ULKE_T = :ULKE_T'
      'WHERE'
      '    ID = :OLD_ID'
      '    ')
    DeleteSQL.Strings = (
      'DELETE FROM'
      '    ULKELER'
      'WHERE'
      '        ID = :OLD_ID'
      '    ')
    InsertSQL.Strings = (
      'INSERT INTO ULKELER('
      '    ID,'
      '    ULKE_T'
      ')'
      'VALUES('
      '    :ID,'
      '    :ULKE_T'
      ')')
    RefreshSQL.Strings = (
      'SELECT'
      '    id,ulke_t'
      'FROM'
      'ulkeler'
      ''
      ' WHERE '
      '        ULKELER.ID = :OLD_ID'
      '    ')
    SelectSQL.Strings = (
      'SELECT'
      '    id,ulke_t'
      'FROM'
      'ulkeler')
    AutoUpdateOptions.UpdateTableName = 'SERTIFIKA'
    AutoUpdateOptions.KeyFields = 'ID'
    AutoUpdateOptions.GeneratorName = 'GEN_SERTIFIKA_ID'
    AutoUpdateOptions.WhenGetGenID = wgOnNewRecord
    Transaction = UniMainModule.trtanim
    Database = UniMainModule.db
    Left = 672
    Top = 456
  end
  object tblMudurlukler: TpFIBDataSet
    UpdateSQL.Strings = (
      'UPDATE URUNLER'
      'SET '
      '    URUNADI_T = :URUNADI_T,'
      '    URUNADI_A = :URUNADI_A,'
      '    URUNADI_I = :URUNADI_I,'
      '    URUNADI_L = :URUNADI_L,'
      '    DURUMU = :DURUMU,'
      '    PRODCODE = :PRODCODE,'
      '    KAYDEDEN = :KAYDEDEN,'
      '    DEGISTIREN = :DEGISTIREN,'
      '    KAYITZAMANI = :KAYITZAMANI,'
      '    DEGISTIRMEZAMANI = :DEGISTIRMEZAMANI,'
      '    ALTGRUPID = :ALTGRUPID,'
      '    DS_FIYATGRUBU = :DS_FIYATGRUBU,'
      '    GTIPNO = :GTIPNO'
      'WHERE'
      '    ID = :OLD_ID'
      '    ')
    DeleteSQL.Strings = (
      'DELETE FROM'
      '    URUNLER'
      'WHERE'
      '        ID = :OLD_ID'
      '    ')
    InsertSQL.Strings = (
      'INSERT INTO URUNLER('
      '    ID,'
      '    URUNADI_T,'
      '    URUNADI_A,'
      '    URUNADI_I,'
      '    URUNADI_L,'
      '    DURUMU,'
      '    PRODCODE,'
      '    KAYDEDEN,'
      '    DEGISTIREN,'
      '    KAYITZAMANI,'
      '    DEGISTIRMEZAMANI,'
      '    ALTGRUPID,'
      '    DS_FIYATGRUBU,'
      '    GTIPNO'
      ')'
      'VALUES('
      '    :ID,'
      '    :URUNADI_T,'
      '    :URUNADI_A,'
      '    :URUNADI_I,'
      '    :URUNADI_L,'
      '    :DURUMU,'
      '    :PRODCODE,'
      '    :KAYDEDEN,'
      '    :DEGISTIREN,'
      '    :KAYITZAMANI,'
      '    :DEGISTIRMEZAMANI,'
      '    :ALTGRUPID,'
      '    :DS_FIYATGRUBU,'
      '    :GTIPNO'
      ')')
    RefreshSQL.Strings = (
      'SELECT'
      '    *'
      'FROM'
      'urunler'
      ''
      ' WHERE '
      '        URUNLER.ID = :OLD_ID'
      '    ')
    SelectSQL.Strings = (
      'SELECT'
      '    *'
      'FROM'
      'geneltanim'
      'where sahibi=80')
    AutoUpdateOptions.UpdateTableName = 'SERTIFIKA'
    AutoUpdateOptions.KeyFields = 'ID'
    AutoUpdateOptions.GeneratorName = 'GEN_SERTIFIKA_ID'
    AutoUpdateOptions.WhenGetGenID = wgOnNewRecord
    Transaction = UniMainModule.trtanim
    Database = UniMainModule.db
    Left = 664
    Top = 280
  end
  object dsMudurlukler: TDataSource
    DataSet = tblMudurlukler
    Left = 736
    Top = 280
  end
  object tblRapor: TpFIBDataSet
    UpdateSQL.Strings = (
      'UPDATE URUNLER'
      'SET '
      '    URUNADI_T = :URUNADI_T,'
      '    URUNADI_A = :URUNADI_A,'
      '    URUNADI_I = :URUNADI_I,'
      '    URUNADI_L = :URUNADI_L,'
      '    DURUMU = :DURUMU,'
      '    PRODCODE = :PRODCODE,'
      '    KAYDEDEN = :KAYDEDEN,'
      '    DEGISTIREN = :DEGISTIREN,'
      '    KAYITZAMANI = :KAYITZAMANI,'
      '    DEGISTIRMEZAMANI = :DEGISTIRMEZAMANI,'
      '    ALTGRUPID = :ALTGRUPID,'
      '    DS_FIYATGRUBU = :DS_FIYATGRUBU,'
      '    GTIPNO = :GTIPNO'
      'WHERE'
      '    ID = :OLD_ID'
      '    ')
    DeleteSQL.Strings = (
      'DELETE FROM'
      '    URUNLER'
      'WHERE'
      '        ID = :OLD_ID'
      '    ')
    InsertSQL.Strings = (
      'INSERT INTO URUNLER('
      '    ID,'
      '    URUNADI_T,'
      '    URUNADI_A,'
      '    URUNADI_I,'
      '    URUNADI_L,'
      '    DURUMU,'
      '    PRODCODE,'
      '    KAYDEDEN,'
      '    DEGISTIREN,'
      '    KAYITZAMANI,'
      '    DEGISTIRMEZAMANI,'
      '    ALTGRUPID,'
      '    DS_FIYATGRUBU,'
      '    GTIPNO'
      ')'
      'VALUES('
      '    :ID,'
      '    :URUNADI_T,'
      '    :URUNADI_A,'
      '    :URUNADI_I,'
      '    :URUNADI_L,'
      '    :DURUMU,'
      '    :PRODCODE,'
      '    :KAYDEDEN,'
      '    :DEGISTIREN,'
      '    :KAYITZAMANI,'
      '    :DEGISTIRMEZAMANI,'
      '    :ALTGRUPID,'
      '    :DS_FIYATGRUBU,'
      '    :GTIPNO'
      ')')
    RefreshSQL.Strings = (
      'SELECT'
      '    *'
      'FROM'
      'urunler'
      ''
      ' WHERE '
      '        URUNLER.ID = :OLD_ID'
      '    ')
    SelectSQL.Strings = (
      'SELECT'
      '    *'
      'FROM'
      'geneltanim'
      'where sahibi=80')
    Transaction = UniMainModule.trtanim
    Database = UniMainModule.db
    Left = 888
    Top = 280
  end
  object dsRapor: TDataSource
    DataSet = tblRapor
    Left = 960
    Top = 280
  end
  object tblFirma: TpFIBDataSet
    UpdateSQL.Strings = (
      'UPDATE URUNLER'
      'SET '
      '    URUNADI_T = :URUNADI_T,'
      '    URUNADI_A = :URUNADI_A,'
      '    URUNADI_I = :URUNADI_I,'
      '    URUNADI_L = :URUNADI_L,'
      '    DURUMU = :DURUMU,'
      '    PRODCODE = :PRODCODE,'
      '    KAYDEDEN = :KAYDEDEN,'
      '    DEGISTIREN = :DEGISTIREN,'
      '    KAYITZAMANI = :KAYITZAMANI,'
      '    DEGISTIRMEZAMANI = :DEGISTIRMEZAMANI,'
      '    ALTGRUPID = :ALTGRUPID,'
      '    DS_FIYATGRUBU = :DS_FIYATGRUBU,'
      '    GTIPNO = :GTIPNO'
      'WHERE'
      '    ID = :OLD_ID'
      '    ')
    DeleteSQL.Strings = (
      'DELETE FROM'
      '    URUNLER'
      'WHERE'
      '        ID = :OLD_ID'
      '    ')
    InsertSQL.Strings = (
      'INSERT INTO URUNLER('
      '    ID,'
      '    URUNADI_T,'
      '    URUNADI_A,'
      '    URUNADI_I,'
      '    URUNADI_L,'
      '    DURUMU,'
      '    PRODCODE,'
      '    KAYDEDEN,'
      '    DEGISTIREN,'
      '    KAYITZAMANI,'
      '    DEGISTIRMEZAMANI,'
      '    ALTGRUPID,'
      '    DS_FIYATGRUBU,'
      '    GTIPNO'
      ')'
      'VALUES('
      '    :ID,'
      '    :URUNADI_T,'
      '    :URUNADI_A,'
      '    :URUNADI_I,'
      '    :URUNADI_L,'
      '    :DURUMU,'
      '    :PRODCODE,'
      '    :KAYDEDEN,'
      '    :DEGISTIREN,'
      '    :KAYITZAMANI,'
      '    :DEGISTIRMEZAMANI,'
      '    :ALTGRUPID,'
      '    :DS_FIYATGRUBU,'
      '    :GTIPNO'
      ')')
    RefreshSQL.Strings = (
      'SELECT'
      '    *'
      'FROM'
      'urunler'
      ''
      ' WHERE '
      '        URUNLER.ID = :OLD_ID'
      '    ')
    SelectSQL.Strings = (
      'SELECT'
      '    *'
      'FROM'
      'geneltanim'
      'where sahibi=89')
    AutoUpdateOptions.UpdateTableName = 'SERTIFIKA'
    AutoUpdateOptions.KeyFields = 'ID'
    AutoUpdateOptions.GeneratorName = 'GEN_SERTIFIKA_ID'
    AutoUpdateOptions.WhenGetGenID = wgOnNewRecord
    Transaction = UniMainModule.trtanim
    Database = UniMainModule.db
    Left = 896
    Top = 360
  end
  object dsFirma: TDataSource
    DataSet = tblFirma
    Left = 952
    Top = 360
  end
end

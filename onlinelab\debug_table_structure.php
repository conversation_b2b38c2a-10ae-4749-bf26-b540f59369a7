<?php
require_once 'config/database.php';

try {
    $db = new Database();
    
    echo "<h2>lab_numuneler Tablo Yapısı</h2>";
    
    $sql = "DESCRIBE lab_numuneler";
    $columns = $db->fetchAll($sql);
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f0f0f0;'>";
    echo "<th><PERSON></th><th>Veri Tipi</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th>";
    echo "</tr>";
    
    foreach ($columns as $col) {
        echo "<tr>";
        echo "<td>{$col['Field']}</td>";
        echo "<td>{$col['Type']}</td>";
        echo "<td>{$col['Null']}</td>";
        echo "<td>{$col['Key']}</td>";
        echo "<td>{$col['Default']}</td>";
        echo "<td>{$col['Extra']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h3>Test Insert</h3>";
    
    // Test verisi
    $testData = [
        'numune_turu' => 'İç Karantina',
        'numune_alt_turu' => 'Proje',
        'LAB_KAYITNO' => 'TEST-2024-001',
        'gonderen' => 'Test Gönderen',
        'urun_id' => 1,
        'miktar' => 10.5,
        'birim' => 'kg',
        'gelis_tarihi' => date('Y-m-d H:i:s'),
        'gelis_saati' => date('H:i:s'),
        'durumu' => 'Numune Kabul'
    ];
    
    echo "<h4>Test Verisi:</h4>";
    echo "<pre>" . print_r($testData, true) . "</pre>";
    
    // SQL oluştur
    $fields = array_keys($testData);
    $placeholders = array_fill(0, count($fields), '?');
    
    $sql = "INSERT INTO lab_numuneler (" . implode(', ', $fields) . ") 
            VALUES (" . implode(', ', $placeholders) . ")";
    
    echo "<h4>SQL:</h4>";
    echo "<pre>" . htmlspecialchars($sql) . "</pre>";
    
    // Test et
    try {
        $id = $db->insert($sql, array_values($testData));
        echo "<div style='color: green;'>✅ Başarılı! Eklenen ID: $id</div>";
    } catch (Exception $e) {
        echo "<div style='color: red;'>❌ Hata: " . $e->getMessage() . "</div>";
        
        // Detaylı hata bilgisi
        $errorInfo = $db->getConnection()->errorInfo();
        echo "<h4>PDO Error Info:</h4>";
        echo "<pre>" . print_r($errorInfo, true) . "</pre>";
    }
    
} catch (Exception $e) {
    echo "<div style='color: red;'>Genel Hata: " . $e->getMessage() . "</div>";
}
?>

<?php
/**
 * Admin Şifre Düzeltme Scripti
 * Bu dosyayı bir kez çalıştırarak admin şifresini düzeltin
 */

// Konfigürasyonu yükle
require_once 'config/config.php';
require_once 'config/database.php';

echo "<h2>Admin Şifre Düzeltme Scripti</h2>";

try {
    $db = Database::getInstance();
    
    // Mevcut admin kullanıcısını kontrol et
    $admin = $db->fetch("SELECT * FROM kullanicilar WHERE kullaniciadi = 'admin'");
    
    if (!$admin) {
        echo "<p style='color: red;'>❌ Admin kullanıcısı bulunamadı!</p>";
        
        // Admin kullanıcısı yoksa oluştur
        $hashedPassword = password_hash('admin123', PASSWORD_DEFAULT);
        
        $sql = "INSERT INTO kullanicilar (
            kullanicia<PERSON>, sif<PERSON>i, adis<PERSON>di,
            NUMUNE_KABUL_YAPABILIR, NUMUNE_LISTESI_GOREBILIR,
            NUMUNE_ONHAZIRLIK_YAPABILIR, NUMUNE_SONUC_GIREBILIR,
            TANIMLAMA_YAPABILIR, NUMUNE_GENEL_GORUNUM, NUMUNE_ATAMA_YAPABILIR
        ) VALUES (
            'admin', ?, 'Sistem Yöneticisi',
            1, 1, 1, 1, 1, 1, 1
        )";
        
        $db->execute($sql, [$hashedPassword]);
        echo "<p style='color: green;'>✅ Admin kullanıcısı oluşturuldu!</p>";
        
    } else {
        echo "<p>📋 Mevcut admin kullanıcısı bulundu:</p>";
        echo "<ul>";
        echo "<li><strong>ID:</strong> " . $admin['id'] . "</li>";
        echo "<li><strong>Kullanıcı Adı:</strong> " . $admin['kullaniciadi'] . "</li>";
        echo "<li><strong>Ad Soyad:</strong> " . $admin['adisoyadi'] . "</li>";
        echo "<li><strong>Aktif:</strong> " . ($admin['aktif'] ? 'Evet' : 'Hayır') . "</li>";
        echo "<li><strong>Mevcut Şifre Hash:</strong> " . substr($admin['sifresi'], 0, 20) . "...</li>";
        echo "</ul>";
        
        // Şifreyi test et
        if (password_verify('admin123', $admin['sifresi'])) {
            echo "<p style='color: green;'>✅ Şifre doğru! Giriş yapabilmelisiniz.</p>";
        } else {
            echo "<p style='color: orange;'>⚠️ Şifre yanlış! Düzeltiliyor...</p>";
            
            // Şifreyi düzelt
            $newHashedPassword = password_hash('admin123', PASSWORD_DEFAULT);
            $db->execute("UPDATE kullanicilar SET sifresi = ? WHERE id = ?", [$newHashedPassword, $admin['id']]);
            
            echo "<p style='color: green;'>✅ Şifre başarıyla düzeltildi!</p>";
        }
        
        // Kullanıcının aktif olduğundan emin ol
        if (!$admin['aktif']) {
            $db->execute("UPDATE kullanicilar SET aktif = 1 WHERE id = ?", [$admin['id']]);
            echo "<p style='color: green;'>✅ Kullanıcı aktif hale getirildi!</p>";
        }
    }
    
    // Test girişi yap
    echo "<hr>";
    echo "<h3>🧪 Giriş Testi</h3>";
    
    $testAdmin = $db->fetch("SELECT * FROM kullanicilar WHERE kullaniciadi = 'admin' AND aktif = 1");
    
    if ($testAdmin && password_verify('admin123', $testAdmin['sifresi'])) {
        echo "<p style='color: green; font-size: 18px; font-weight: bold;'>✅ GİRİŞ TESTİ BAŞARILI!</p>";
        echo "<p>Artık aşağıdaki bilgilerle giriş yapabilirsiniz:</p>";
        echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; border-left: 4px solid #28a745;'>";
        echo "<strong>Kullanıcı Adı:</strong> admin<br>";
        echo "<strong>Şifre:</strong> admin123";
        echo "</div>";
    } else {
        echo "<p style='color: red;'>❌ Giriş testi başarısız! Veritabanı bağlantısını kontrol edin.</p>";
    }
    
    // Veritabanı bağlantı bilgilerini göster
    echo "<hr>";
    echo "<h3>🔧 Veritabanı Bilgileri</h3>";
    echo "<ul>";
    echo "<li><strong>Host:</strong> " . DB_HOST . "</li>";
    echo "<li><strong>Database:</strong> " . DB_NAME . "</li>";
    echo "<li><strong>User:</strong> " . DB_USER . "</li>";
    echo "<li><strong>Charset:</strong> " . DB_CHARSET . "</li>";
    echo "</ul>";
    
    // Tüm kullanıcıları listele
    echo "<hr>";
    echo "<h3>👥 Tüm Kullanıcılar</h3>";
    $users = $db->fetchAll("SELECT id, kullaniciadi, adisoyadi, aktif FROM kullanicilar ORDER BY id");
    
    if (empty($users)) {
        echo "<p>Hiç kullanıcı bulunamadı.</p>";
    } else {
        echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>Kullanıcı Adı</th><th>Ad Soyad</th><th>Aktif</th></tr>";
        foreach ($users as $user) {
            $aktifText = $user['aktif'] ? '✅ Evet' : '❌ Hayır';
            echo "<tr>";
            echo "<td>" . $user['id'] . "</td>";
            echo "<td>" . $user['kullaniciadi'] . "</td>";
            echo "<td>" . $user['adisoyadi'] . "</td>";
            echo "<td>" . $aktifText . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Hata: " . $e->getMessage() . "</p>";
    echo "<p>Veritabanı bağlantısını ve ayarlarını kontrol edin.</p>";
    
    echo "<h3>🔧 Kontrol Edilecekler:</h3>";
    echo "<ol>";
    echo "<li>MySQL servisi çalışıyor mu?</li>";
    echo "<li>Veritabanı oluşturuldu mu? (laboratuvar_db)</li>";
    echo "<li>config/config.php dosyasındaki DB ayarları doğru mu?</li>";
    echo "<li>install/database.sql dosyası çalıştırıldı mı?</li>";
    echo "</ol>";
}

echo "<hr>";
echo "<p><strong>Not:</strong> Bu dosyayı güvenlik nedeniyle kullandıktan sonra silebilirsiniz.</p>";
echo "<p><a href='index.php'>← Ana Sayfaya Dön</a></p>";
?>

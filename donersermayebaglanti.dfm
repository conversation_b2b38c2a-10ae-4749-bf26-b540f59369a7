object frmDonersermayebaglanti: TfrmDonersermayebaglanti
  Left = 0
  Top = 0
  ClientHeight = 604
  ClientWidth = 1189
  Caption = 'D'#246'nersermaye Ba'#287'lant'#305
  OnShow = UniFormShow
  OldCreateOrder = False
  OnClose = UniFormClose
  MonitoredKeys.Keys = <>
  PixelsPerInch = 96
  TextHeight = 13
  object UniPanel1: TUniPanel
    Left = 0
    Top = 0
    Width = 1189
    Height = 81
    Align = alTop
    TabOrder = 0
    BorderStyle = ubsNone
    object UniLabel1: TUniLabel
      Left = 16
      Top = 20
      Width = 44
      Height = 13
      Caption = 'Firma Ad'#305
      TabOrder = 1
    end
    object edtFirma: TUniEdit
      Left = 16
      Top = 39
      Width = 409
      TabOrder = 2
    end
    object UniButton1: TUniButton
      Left = 440
      Top = 20
      Width = 75
      Height = 41
      Caption = 'Listele'
      TabOrder = 3
      OnClick = UniButton1Click
    end
    object UniButton2: TUniButton
      Left = 536
      Top = 20
      Width = 75
      Height = 41
      Caption = 'Kapat'
      TabOrder = 4
      OnClick = UniButton2Click
    end
  end
  object UniDBGrid1: TUniDBGrid
    AlignWithMargins = True
    Left = 4
    Top = 85
    Width = 1181
    Height = 515
    Margins.Left = 4
    Margins.Top = 4
    Margins.Right = 4
    Margins.Bottom = 4
    ClientEvents.ExtEvents.Strings = (
      
        'checkboxModel.select=function checkboxModel.select(sender, recor' +
        'd, index, eOpts)'#13#10'{'#13#10'}')
    ClientEvents.UniEvents.Strings = (
      
        'beforeInit=function beforeInit(sender, config)'#13#10'{'#13#10'      config.' +
        'viewConfig.enableTextSelection = true;'#13#10'}')
    TitleFont.Height = -13
    DataSource = dsRapor
    Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgDontShowSelected]
    WebOptions.PageSize = 30
    WebOptions.FetchAll = True
    LoadMask.Message = 'L'#195#188'tfen Bekleyiniz...'
    EmptyText = 'G'#246'sterilecek Kay'#305't Yok'
    StripeRows = False
    Align = alClient
    TabOrder = 1
    TabStop = False
    Columns = <
      item
        FieldName = 'DURUMU'
        Title.Caption = #214'deme Durumu'
        Title.Font.Height = -13
        Width = 88
        Font.Height = -13
        Sortable = True
      end
      item
        FieldName = 'TARIH'
        Title.Caption = #304#351'lem Tarihi'
        Title.Font.Height = -13
        Width = 90
        Font.Height = -13
        Sortable = True
      end
      item
        FieldName = 'FISTURU'
        Title.Caption = 'Fi'#351' T'#252'r'#252
        Title.Font.Height = -13
        Width = 34
        Font.Height = -13
        Sortable = True
      end
      item
        FieldName = 'REFERANS'
        Title.Caption = #304#351'lem Yeri'
        Title.Font.Height = -13
        Width = 100
        Font.Height = -13
        Sortable = True
      end
      item
        FieldName = 'OZELALAN1'
        Title.Caption = 'Dilek'#231'e No'
        Title.Font.Height = -13
        Width = 149
        Font.Height = -13
        Sortable = True
      end
      item
        FieldName = 'KISIADI'
        Title.Caption = 'Firma Ad'#305
        Title.Font.Height = -13
        Width = 232
        Font.Height = -13
        Sortable = True
      end
      item
        FieldName = 'TUTARI'
        Title.Caption = 'Fi'#351' Tutar'#305
        Title.Font.Height = -13
        Width = 74
        Font.Height = -13
        Sortable = True
      end
      item
        FieldName = 'ODEMESEKLI'
        Title.Caption = #214'deme '#350'ekli'
        Title.Font.Height = -13
        Width = 100
        Font.Height = -13
        Sortable = True
      end
      item
        FieldName = 'TAHSILATTARIHI'
        Title.Caption = 'Tahsilat Tarihi'
        Title.Font.Height = -13
        Width = 86
        Font.Height = -13
        Sortable = True
      end
      item
        FieldName = 'TAHSILATACIKLAMA'
        Title.Caption = 'Tahsilat A'#231#305'klama'
        Title.Font.Height = -13
        Width = 100
        Font.Height = -13
        Sortable = True
      end>
  end
  object db: TpFIBDatabase
    DBName = 'D:\programlar\ihracat_programi\database\ihracat_yeni.FDB'
    DBParams.Strings = (
      'user_name=sysdba'
      'password=masterkey')
    SQLDialect = 3
    Timeout = 0
    LibraryName = 'C:\Program Files (x86)\Firebird\Firebird_2_5\bin\fbclient.dll'
    WaitForRestoreConnect = 0
    Left = 88
    Top = 192
  end
  object tr: TpFIBTransaction
    DefaultDatabase = db
    Left = 179
    Top = 192
  end
  object tblRapor: TpFIBDataSet
    UpdateSQL.Strings = (
      'UPDATE DONERSERMAYE'
      'SET '
      '    TARIH = :TARIH,'
      '    REFERANS = :REFERANS,'
      '    REFERANSNO = :REFERANSNO,'
      '    TUTARI = :TUTARI,'
      '    OZELALAN1 = :OZELALAN1,'
      '    OZELALAN2 = :OZELALAN2,'
      '    OZELALAN3 = :OZELALAN3,'
      '    KAYDEDEN = :KAYDEDEN,'
      '    DEGISTIREN = :DEGISTIREN,'
      '    KAYITZAMANI = :KAYITZAMANI,'
      '    DEGISTIRMEZAMANI = :DEGISTIRMEZAMANI,'
      '    DURUMU = :DURUMU,'
      '    TAHSILATTARIHI = :TAHSILATTARIHI,'
      '    TAHSILATACIKLAMA = :TAHSILATACIKLAMA,'
      '    GUMRUKCU_ID = :GUMRUKCU_ID,'
      '    PERSONELID = :PERSONELID,'
      '    FIRMAID = :FIRMAID,'
      '    KAYITNO = :KAYITNO,'
      '    SERTIFIKANO = :SERTIFIKANO,'
      '    DSKAYITNO = :DSKAYITNO,'
      '    TAHSILATTUTARI = :TAHSILATTUTARI,'
      '    ISLEMTURU = :ISLEMTURU,'
      '    BIRIMI = :BIRIMI,'
      '    FATURALANDI = :FATURALANDI,'
      '    FATURATOPLAM = :FATURATOPLAM,'
      '    FATURAKDVTOPLAM = :FATURAKDVTOPLAM,'
      '    FATURAGENELTOPLAM = :FATURAGENELTOPLAM,'
      '    KISIADI = :KISIADI,'
      '    FATURAUNVANI = :FATURAUNVANI,'
      '    FATURAADRESI = :FATURAADRESI,'
      '    FATURAVERGIDAIRESI = :FATURAVERGIDAIRESI,'
      '    FATURAVERGINO = :FATURAVERGINO,'
      '    ODEMESEKLI = :ODEMESEKLI,'
      '    FISTURU = :FISTURU,'
      '    VEZNEALINDIKESILDI = :VEZNEALINDIKESILDI,'
      '    FATURANO = :FATURANO,'
      '    FATURALAMATARIHI = :FATURALAMATARIHI,'
      '    FATURALAMASAATI = :FATURALAMASAATI,'
      '    GOREVLENDIRMEZAMANI = :GOREVLENDIRMEZAMANI,'
      '    AKTIF = :AKTIF,'
      '    IPTAL = :IPTAL,'
      '    CIKISSAATI = :CIKISSAATI,'
      '    DONUSSAATI = :DONUSSAATI,'
      '    ARACPLAKASI = :ARACPLAKASI,'
      '    HAFTASONUTATIL = :HAFTASONUTATIL,'
      '    GOREVLENDIRILENPERSONEL = :GOREVLENDIRILENPERSONEL,'
      '    ONAYLAYANPERSONEL = :ONAYLAYANPERSONEL,'
      '    GOREVYERI = :GOREVYERI,'
      '    SOFOR = :SOFOR,'
      '    BARKOD_YAZDIRILDI = :BARKOD_YAZDIRILDI,'
      '    TURU = :TURU,'
      '    ISLEMTIPI = :ISLEMTIPI,'
      '    ANAKASAAKTARILDI = :ANAKASAAKTARILDI,'
      '    VEZNEALINDINO = :VEZNEALINDINO'
      'WHERE'
      '    ID = :OLD_ID'
      '    ')
    DeleteSQL.Strings = (
      'DELETE FROM'
      '    DONERSERMAYE'
      'WHERE'
      '        ID = :OLD_ID'
      '    ')
    InsertSQL.Strings = (
      'INSERT INTO DONERSERMAYE('
      '    ID,'
      '    TARIH,'
      '    REFERANS,'
      '    REFERANSNO,'
      '    TUTARI,'
      '    OZELALAN1,'
      '    OZELALAN2,'
      '    OZELALAN3,'
      '    KAYDEDEN,'
      '    DEGISTIREN,'
      '    KAYITZAMANI,'
      '    DEGISTIRMEZAMANI,'
      '    DURUMU,'
      '    TAHSILATTARIHI,'
      '    TAHSILATACIKLAMA,'
      '    GUMRUKCU_ID,'
      '    PERSONELID,'
      '    FIRMAID,'
      '    KAYITNO,'
      '    SERTIFIKANO,'
      '    DSKAYITNO,'
      '    TAHSILATTUTARI,'
      '    ISLEMTURU,'
      '    BIRIMI,'
      '    FATURALANDI,'
      '    FATURATOPLAM,'
      '    FATURAKDVTOPLAM,'
      '    FATURAGENELTOPLAM,'
      '    KISIADI,'
      '    FATURAUNVANI,'
      '    FATURAADRESI,'
      '    FATURAVERGIDAIRESI,'
      '    FATURAVERGINO,'
      '    ODEMESEKLI,'
      '    FISTURU,'
      '    VEZNEALINDIKESILDI,'
      '    FATURANO,'
      '    FATURALAMATARIHI,'
      '    FATURALAMASAATI,'
      '    GOREVLENDIRMEZAMANI,'
      '    AKTIF,'
      '    IPTAL,'
      '    CIKISSAATI,'
      '    DONUSSAATI,'
      '    ARACPLAKASI,'
      '    HAFTASONUTATIL,'
      '    GOREVLENDIRILENPERSONEL,'
      '    ONAYLAYANPERSONEL,'
      '    GOREVYERI,'
      '    SOFOR,'
      '    BARKOD_YAZDIRILDI,'
      '    TURU,'
      '    ISLEMTIPI,'
      '    ANAKASAAKTARILDI,'
      '    VEZNEALINDINO'
      ')'
      'VALUES('
      '    :ID,'
      '    :TARIH,'
      '    :REFERANS,'
      '    :REFERANSNO,'
      '    :TUTARI,'
      '    :OZELALAN1,'
      '    :OZELALAN2,'
      '    :OZELALAN3,'
      '    :KAYDEDEN,'
      '    :DEGISTIREN,'
      '    :KAYITZAMANI,'
      '    :DEGISTIRMEZAMANI,'
      '    :DURUMU,'
      '    :TAHSILATTARIHI,'
      '    :TAHSILATACIKLAMA,'
      '    :GUMRUKCU_ID,'
      '    :PERSONELID,'
      '    :FIRMAID,'
      '    :KAYITNO,'
      '    :SERTIFIKANO,'
      '    :DSKAYITNO,'
      '    :TAHSILATTUTARI,'
      '    :ISLEMTURU,'
      '    :BIRIMI,'
      '    :FATURALANDI,'
      '    :FATURATOPLAM,'
      '    :FATURAKDVTOPLAM,'
      '    :FATURAGENELTOPLAM,'
      '    :KISIADI,'
      '    :FATURAUNVANI,'
      '    :FATURAADRESI,'
      '    :FATURAVERGIDAIRESI,'
      '    :FATURAVERGINO,'
      '    :ODEMESEKLI,'
      '    :FISTURU,'
      '    :VEZNEALINDIKESILDI,'
      '    :FATURANO,'
      '    :FATURALAMATARIHI,'
      '    :FATURALAMASAATI,'
      '    :GOREVLENDIRMEZAMANI,'
      '    :AKTIF,'
      '    :IPTAL,'
      '    :CIKISSAATI,'
      '    :DONUSSAATI,'
      '    :ARACPLAKASI,'
      '    :HAFTASONUTATIL,'
      '    :GOREVLENDIRILENPERSONEL,'
      '    :ONAYLAYANPERSONEL,'
      '    :GOREVYERI,'
      '    :SOFOR,'
      '    :BARKOD_YAZDIRILDI,'
      '    :TURU,'
      '    :ISLEMTIPI,'
      '    :ANAKASAAKTARILDI,'
      '    :VEZNEALINDINO'
      ')')
    RefreshSQL.Strings = (
      'select ds.*,s.ihracatcifirma firma,s.sertifikano,s.kayitno,'
      
        'f.firmaadi,f.faturaunvani,f.faturaadresi,f.vergidairesi,f.vergin' +
        'o from donersermaye ds'
      
        'inner join sertifika s on (s.id=ds.referansno and ds.referans='#39'S' +
        'ertifika'#39')'
      'left join firmalar f on (f.id = ds.FIRMAID)'
      ''
      ' WHERE '
      '        DS.ID = :OLD_ID'
      '    ')
    SelectSQL.Strings = (
      'select ds.*,s.ihracatcifirma firma,s.sertifikano,s.kayitno,'
      
        'f.firmaadi,f.faturaunvani,f.faturaadresi,f.vergidairesi,f.vergin' +
        'o from donersermaye ds'
      
        'inner join sertifika s on (s.id=ds.referansno and ds.referans='#39'S' +
        'ertifika'#39')'
      'left join firmalar f on (f.id = ds.FIRMAID)')
    Transaction = tr
    Database = db
    DefaultFormats.DateTimeDisplayFormat = 'dd.mm.yyyy hh:mm AM'
    DefaultFormats.NumericDisplayFormat = '0.00'
    Left = 376
    Top = 352
  end
  object dsRapor: TDataSource
    DataSet = tblRapor
    Left = 427
    Top = 352
  end
end

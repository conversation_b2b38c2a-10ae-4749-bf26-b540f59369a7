object frmEtmenTanim: TfrmEtmenTanim
  Left = 0
  Top = 0
  ClientHeight = 449
  ClientWidth = 640
  Caption = 'Etmen Tan'#305'mlar'#305
  OnShow = UniFormShow
  OldCreateOrder = False
  MonitoredKeys.Keys = <>
  PixelsPerInch = 96
  TextHeight = 13
  object UniPanel1: TUniPanel
    Left = 0
    Top = 416
    Width = 640
    Height = 33
    Align = alBottom
    TabOrder = 0
    BorderStyle = ubsNone
    Color = 16447736
    object UniSpeedButton2: TUniSpeedButton
      AlignWithMargins = True
      Left = 492
      Top = 3
      Width = 72
      Height = 27
      Caption = 'Kaydet'
      Align = alRight
      ParentColor = False
      Color = clBtnFace
      TabOrder = 1
      OnClick = UniSpeedButton2Click
    end
    object UniSpeedButton3: TUniSpeedButton
      AlignWithMargins = True
      Left = 570
      Top = 3
      Width = 67
      Height = 27
      Caption = 'Kapat'
      Align = alRight
      ParentColor = False
      Color = clBtnFace
      TabOrder = 2
      OnClick = UniSpeedButton3Click
    end
    object UniSpeedButton5: TUniSpeedButton
      AlignWithMargins = True
      Left = 3
      Top = 3
      Width = 75
      Height = 27
      Caption = 'Sil'
      Align = alLeft
      ParentColor = False
      Color = clBtnFace
      TabOrder = 3
      OnClick = UniSpeedButton5Click
    end
    object lbldurum: TUniLabel
      Left = 96
      Top = 3
      Width = 40
      Height = 13
      Visible = False
      Caption = 'lbldurum'
      TabOrder = 4
    end
    object UniSpeedButton1: TUniSpeedButton
      AlignWithMargins = True
      Left = 414
      Top = 3
      Width = 72
      Height = 27
      Caption = 'Yeni'
      Align = alRight
      ParentColor = False
      Color = clBtnFace
      TabOrder = 5
      OnClick = UniSpeedButton1Click
    end
    object lblTur: TUniLabel
      Left = 96
      Top = 17
      Width = 26
      Height = 13
      Visible = False
      Caption = 'lblTur'
      TabOrder = 6
    end
    object lblurunid: TUniLabel
      Left = 168
      Top = 3
      Width = 40
      Height = 13
      Visible = False
      Caption = 'lblurunid'
      TabOrder = 7
    end
  end
  object UniDBGrid1: TUniDBGrid
    AlignWithMargins = True
    Left = 3
    Top = 73
    Width = 634
    Height = 340
    ClicksToEdit = 1
    DataSource = dsEtmenler
    Options = [dgEditing, dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgConfirmDelete]
    WebOptions.Paged = False
    WebOptions.FetchAll = True
    LoadMask.Message = 'L'#195#188'tfen Bekleyiniz...'
    ForceFit = True
    Align = alClient
    TabOrder = 1
    Columns = <
      item
        FieldName = 'ETMEN_ADI'
        Title.Caption = 'Etmenler'
        Width = 347
      end>
  end
  object UniPanel2: TUniPanel
    Left = 0
    Top = 0
    Width = 640
    Height = 70
    Align = alTop
    TabOrder = 2
    BorderStyle = ubsNone
    Color = 16447736
    object edtEtmen: TUniEdit
      Left = 16
      Top = 32
      Width = 276
      TabOrder = 1
    end
    object lkbBirim: TUniDBLookupComboBox
      Left = 295
      Top = 32
      Width = 227
      Visible = False
      ListField = 'BIRIMADI'
      ListSource = dsBirimler
      KeyField = 'ID'
      ListFieldIndex = 0
      TabOrder = 2
      Color = clWindow
      OnChange = lkbBirimChange
    end
    object UniButton1: TUniButton
      Left = 528
      Top = 24
      Width = 75
      Height = 30
      Caption = 'Ekle'
      TabOrder = 3
      OnClick = UniButton1Click
    end
    object UniLabel1: TUniLabel
      Left = 16
      Top = 13
      Width = 48
      Height = 13
      Caption = 'Etmen Ad'#305
      TabOrder = 4
    end
    object UniLabel2: TUniLabel
      Left = 295
      Top = 13
      Width = 40
      Height = 13
      Visible = False
      Caption = 'Birim Ad'#305
      TabOrder = 5
    end
    object lkbEtmenListesi: TUniDBLookupComboBox
      Left = 16
      Top = 32
      Width = 265
      Visible = False
      ListField = 'ETMEN_ADI'
      ListSource = dsEtmenListesi
      KeyField = 'ETMEN_ADI'
      ListFieldIndex = 0
      TabOrder = 6
      Color = clWindow
      ClientEvents.ExtEvents.Strings = (
        
          'beforerender=function beforerender(sender, eOpts)'#13#10'{'#13#10'        se' +
          'nder.allowBlank=true; '#13#10'  sender.editable = true;'#13#10'}')
    end
  end
  object tblEtmenler: TpFIBDataSet
    UpdateSQL.Strings = (
      'UPDATE LAB_ETMENLER'
      'SET '
      '    ETMEN_ADI = :ETMEN_ADI,'
      '    LABORATUVAR_BIRIM = :LABORATUVAR_BIRIM,'
      '    URUN_ID = :URUN_ID'
      'WHERE'
      '    ID = :OLD_ID'
      '    ')
    DeleteSQL.Strings = (
      'DELETE FROM'
      '    LAB_ETMENLER'
      'WHERE'
      '        ID = :OLD_ID'
      '    ')
    InsertSQL.Strings = (
      'INSERT INTO LAB_ETMENLER('
      '    ID,'
      '    ETMEN_ADI,'
      '    LABORATUVAR_BIRIM,'
      '    URUN_ID'
      ')'
      'VALUES('
      '    :ID,'
      '    :ETMEN_ADI,'
      '    :LABORATUVAR_BIRIM,'
      '    :URUN_ID'
      ')')
    RefreshSQL.Strings = (
      'SELECT'
      '    *'
      'FROM'
      '    LAB_ETMENLER'
      'where'
      '   LAB_ETMENLER.ID = :OLD_ID'
      ''
      '    '
      ' ')
    SelectSQL.Strings = (
      'SELECT'
      '    *'
      'FROM'
      '    LAB_ETMENLER'
      'where LABORATUVAR_BIRIM=:labbirim'
      'order by ETMEN_ADI COLLATE PXW_TURK'
      '')
    AutoUpdateOptions.UpdateTableName = 'SERTIFIKA'
    AutoUpdateOptions.KeyFields = 'ID'
    AutoUpdateOptions.GeneratorName = 'GEN_SERTIFIKA_ID'
    AutoUpdateOptions.WhenGetGenID = wgOnNewRecord
    Transaction = UniMainModule.trtanim
    Database = UniMainModule.db
    Left = 56
    Top = 176
  end
  object dsEtmenler: TDataSource
    DataSet = tblEtmenler
    Left = 144
    Top = 176
  end
  object tblBirimler: TpFIBDataSet
    UpdateSQL.Strings = (
      'UPDATE LABORATUVAR_BIRIMLER'
      'SET '
      '    BIRIMADI = :BIRIMADI,'
      '    PERSONEL = :PERSONEL'
      'WHERE'
      '    ID = :OLD_ID'
      '    ')
    DeleteSQL.Strings = (
      'DELETE FROM'
      '    LABORATUVAR_BIRIMLER'
      'WHERE'
      '        ID = :OLD_ID'
      '    ')
    InsertSQL.Strings = (
      'INSERT INTO LABORATUVAR_BIRIMLER('
      '    ID,'
      '    BIRIMADI,'
      '    PERSONEL'
      ')'
      'VALUES('
      '    :ID,'
      '    :BIRIMADI,'
      '    :PERSONEL'
      ')')
    RefreshSQL.Strings = (
      'select * from LABORATUVAR_BIRIMLER'
      ''
      ''
      ' WHERE '
      '        LABORATUVAR_BIRIMLER.ID = :OLD_ID'
      '    ')
    SelectSQL.Strings = (
      'select * from LABORATUVAR_BIRIMLER'
      '')
    Transaction = UniMainModule.trtanim
    Database = UniMainModule.db
    Left = 288
    Top = 232
  end
  object dsBirimler: TDataSource
    DataSet = tblBirimler
    Left = 344
    Top = 232
  end
  object tblEtmenListesi: TpFIBDataSet
    UpdateSQL.Strings = (
      'UPDATE LAB_ETMENLER'
      'SET '
      '    ETMEN_ADI = :ETMEN_ADI,'
      '    LABORATUVAR_BIRIM = :LABORATUVAR_BIRIM,'
      '    URUN_ID = :URUN_ID'
      'WHERE'
      '    ID = :OLD_ID'
      '    ')
    DeleteSQL.Strings = (
      'DELETE FROM'
      '    LAB_ETMENLER'
      'WHERE'
      '        ID = :OLD_ID'
      '    ')
    InsertSQL.Strings = (
      'INSERT INTO LAB_ETMENLER('
      '    ID,'
      '    ETMEN_ADI,'
      '    LABORATUVAR_BIRIM,'
      '    URUN_ID'
      ')'
      'VALUES('
      '    :ID,'
      '    :ETMEN_ADI,'
      '    :LABORATUVAR_BIRIM,'
      '    :URUN_ID'
      ')')
    RefreshSQL.Strings = (
      'SELECT'
      '    *'
      'FROM'
      '    LAB_ETMENLER'
      'where'
      '   LAB_ETMENLER.ID = :OLD_ID'
      ''
      '    '
      ' ')
    SelectSQL.Strings = (
      'SELECT'
      '    *'
      'FROM'
      '    LAB_ETMENLER'
      'where LABORATUVAR_BIRIM=:labbirim'
      'order by ETMEN_ADI COLLATE PXW_TURK'
      '')
    AutoUpdateOptions.UpdateTableName = 'SERTIFIKA'
    AutoUpdateOptions.KeyFields = 'ID'
    AutoUpdateOptions.GeneratorName = 'GEN_SERTIFIKA_ID'
    AutoUpdateOptions.WhenGetGenID = wgOnNewRecord
    Transaction = UniMainModule.trtanim
    Database = UniMainModule.db
    Left = 48
    Top = 248
  end
  object dsEtmenListesi: TDataSource
    DataSet = tblEtmenListesi
    Left = 136
    Top = 248
  end
end

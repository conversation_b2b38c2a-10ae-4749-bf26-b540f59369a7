<?php
$pageTitle = 'Numune Sonuç Girişi';
$pageSubtitle = 'Analiz sonuçları ve etmen değerleri';
require_once 'views/layout/header.php';

$numune = $data['numune'] ?? null;
$atamalar = $data['atamalar'] ?? [];
$etmenSonuclari = $data['etmen_sonuclari'] ?? [];
$etmenler = $data['etmenler'] ?? [];

if (!$numune) {
    echo '<div class="alert alert-danger">Numune bulunamadı!</div>';
    require_once 'views/layout/footer.php';
    exit;
}
?>

<div class="row">
    <!-- Numune Bilgileri -->
    <div class="col-lg-4">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-vial me-2"></i>Numune Bilgileri
                </h5>
            </div>
            <div class="card-body">
                <table class="table table-borderless table-sm">
                    <tr>
                        <td><strong>Lab Kayıt No:</strong></td>
                        <td><?= safe_html($numune['LAB_KAYITNO']) ?></td>
                    </tr>
                    <tr>
                        <td><strong>Tür:</strong></td>
                        <td>
                            <span class="badge <?= safe_equals($numune['numune_turu'], 'İç Karantina') ? 'bg-info' : 'bg-warning' ?>">
                                <?= safe_html($numune['numune_turu']) ?>
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>Ürün:</strong></td>
                        <td><?= safe_html($numune['urun_adi'] ?: 'Belirtilmemiş') ?></td>
                    </tr>
                    <tr>
                        <td><strong>Gönderen:</strong></td>
                        <td><?= safe_html($numune['gonderen']) ?></td>
                    </tr>
                    <tr>
                        <td><strong>Miktar:</strong></td>
                        <td><?= safe_number($numune['miktar'], 2) ?> <?= safe_html($numune['birim']) ?></td>
                    </tr>
                    <tr>
                        <td><strong>Durum:</strong></td>
                        <td>
                            <?php
                            $statusColors = [
                                'Numune Kabul' => 'bg-primary',
                                'Ön Hazırlık' => 'bg-warning',
                                'Analiz Aşamasında' => 'bg-info',
                                'Analiz Tamamlandı' => 'bg-success'
                            ];
                            $badgeClass = $statusColors[$numune['durumu']] ?? 'bg-secondary';
                            ?>
                            <span class="badge <?= get_status_badge_class($numune['durumu']) ?>">
                                <?= safe_html($numune['durumu']) ?>
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>Geliş Tarihi:</strong></td>
                        <td><?= date('d.m.Y', strtotime($numune['gelis_tarihi'])) ?></td>
                    </tr>
                </table>
            </div>
        </div>
        
        <!-- Atamalar -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-building me-2"></i>Birim Atamaları
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($atamalar)): ?>
                    <div class="text-center py-3">
                        <i class="fas fa-info-circle text-muted mb-2"></i>
                        <p class="text-muted mb-0">Henüz birim ataması yapılmamış.</p>
                    </div>
                <?php else: ?>
                    <?php foreach ($atamalar as $atama): ?>
                        <div class="d-flex justify-content-between align-items-center mb-2 p-2 bg-light rounded">
                            <div>
                                <strong><?= safe_html($atama['birimadi']) ?></strong>
                                <small class="d-block text-muted">
                                    <?= safe_date($atama['atama_tarihi'], 'd.m.Y') ?>
                                </small>
                            </div>
                            <div>
                                <?php if ($atama['analiz_tarihi']): ?>
                                    <span class="badge bg-success">Tamamlandı</span>
                                <?php else: ?>
                                    <span class="badge bg-warning">Bekliyor</span>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- Etmen Sonuçları -->
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-flask me-2"></i>Etmen Sonuçları
                        <span class="badge bg-primary"><?= count($etmenSonuclari) ?></span>
                    </h5>
                    
                    <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#yeniEtmenModal">
                        <i class="fas fa-plus me-2"></i>Yeni Etmen Sonucu
                    </button>
                </div>
            </div>
            <div class="card-body">
                <?php if (empty($etmenSonuclari)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-flask fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Henüz etmen sonucu girilmemiş</h5>
                        <p class="text-muted">Analiz sonuçlarını girmek için "Yeni Etmen Sonucu" butonunu kullanın.</p>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Etmen Adı</th>
                                    <th>Bulaşık mı?</th>
                                    <th>Değer</th>
                                    <th>Birim</th>
                                    <th>İşlemler</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($etmenSonuclari as $sonuc): ?>
                                    <tr>
                                        <td>
                                            <strong><?= safe_html($sonuc['ETMEN_ADI']) ?></strong>
                                        </td>
                                        <td>
                                            <?php if (safe_equals($sonuc['BULASIKMI'], 'Evet')): ?>
                                                <span class="badge bg-danger">
                                                    <i class="fas fa-exclamation-triangle me-1"></i>Evet
                                                </span>
                                            <?php else: ?>
                                                <span class="badge bg-success">
                                                    <i class="fas fa-check me-1"></i>Hayır
                                                </span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if (!empty($sonuc['deger'])): ?>
                                                <strong><?= safe_html($sonuc['deger']) ?></strong>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if (!empty($sonuc['birim'])): ?>
                                                <?= safe_html($sonuc['birim']) ?>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button type="button" class="btn btn-outline-primary"
                                                        onclick="editEtmen(<?= safe_html(json_encode($sonuc)) ?>)">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button type="button" class="btn btn-outline-danger" 
                                                        onclick="deleteEtmen(<?= $sonuc['id'] ?>)">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- Hızlı İşlemler -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i>Hızlı İşlemler
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="d-grid gap-2">
                            <?php if ($numune['durumu'] === 'Analiz Aşamasında'): ?>
                                <button type="button" class="btn btn-success" 
                                        onclick="updateDurum('Analiz Tamamlandı')">
                                    <i class="fas fa-check me-2"></i>Analizi Tamamla
                                </button>
                            <?php endif; ?>
                            
                            <button type="button" class="btn btn-info" 
                                    onclick="generateReport()">
                                <i class="fas fa-file-alt me-2"></i>Rapor Oluştur
                            </button>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="d-grid gap-2">
                            <a href="index.php?page=numune-detay&id=<?= $numune['id'] ?>" 
                               class="btn btn-outline-primary">
                                <i class="fas fa-eye me-2"></i>Detaylı Görünüm
                            </a>
                            
                            <a href="index.php?page=numune-listesi" 
                               class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Listeye Dön
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Yeni Etmen Sonucu Modal -->
<div class="modal fade" id="yeniEtmenModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-plus me-2"></i>Yeni Etmen Sonucu
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="index.php?page=numune-sonuc&action=save" id="etmenForm">
                <input type="hidden" name="numune_id" value="<?= $numune['id'] ?>">
                <input type="hidden" name="etmen_id" id="etmen_id" value="">
                
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="etmen_adi" class="form-label">
                            Etmen Adı <span class="text-danger">*</span>
                        </label>
                        <select class="form-select" id="etmen_adi" name="etmen_adi" required>
                            <option value="">Etmen seçiniz...</option>
                            <?php foreach ($etmenler as $etmen): ?>
                                <option value="<?= safe_html($etmen['izahat']) ?>">
                                    <?= safe_html($etmen['izahat']) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="bulasik_mi" class="form-label">
                            Bulaşık mı? <span class="text-danger">*</span>
                        </label>
                        <select class="form-select" id="bulasik_mi" name="bulasik_mi" required>
                            <option value="Hayır">Hayır</option>
                            <option value="Evet">Evet</option>
                        </select>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="deger" class="form-label">Değer</label>
                                <input type="text" class="form-control" id="deger" name="deger" 
                                       placeholder="Ölçüm değeri">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="birim" class="form-label">Birim</label>
                                <select class="form-select" id="birim" name="birim">
                                    <option value="">Seçiniz</option>
                                    <option value="ppm">ppm</option>
                                    <option value="mg/kg">mg/kg</option>
                                    <option value="µg/kg">µg/kg</option>
                                    <option value="cfu/g">cfu/g</option>
                                    <option value="%">%</option>
                                    <option value="adet/g">adet/g</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">İptal</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>Kaydet
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php
$numuneId = $numune['id'];
$customJS = <<<EOD
<script>
// Etmen düzenleme
function editEtmen(sonuc) {
    $('#etmen_id').val(sonuc.id);
    $('#etmen_adi').val(sonuc.ETMEN_ADI);
    $('#bulasik_mi').val(sonuc.BULASIKMI);
    $('#deger').val(sonuc.deger);
    $('#birim').val(sonuc.birim);

    $('#yeniEtmenModal .modal-title').html('<i class="fas fa-edit me-2"></i>Etmen Sonucu Düzenle');
    $('#yeniEtmenModal').modal('show');
}

// Etmen silme
function deleteEtmen(id) {
    if (confirm('Bu etmen sonucunu silmek istediğinizden emin misiniz?')) {
        window.location.href = 'index.php?page=numune-sonuc&action=delete-etmen&id=' + id + '&numune_id={$numuneId}';
    }
}

// Durum güncelleme
function updateDurum(yeniDurum) {
    if (confirm('Numune durumunu "' + yeniDurum + '" olarak değiştirmek istediğinizden emin misiniz?')) {
        const form = $('<form method="POST" action="index.php?page=numune-sonuc&action=update-durum"></form>');
        form.append('<input type="hidden" name="numune_id" value="{$numuneId}">');
        form.append('<input type="hidden" name="durum" value="' + yeniDurum + '">');
        $('body').append(form);
        form.submit();
    }
}

// Rapor oluşturma
function generateReport() {
    window.open('index.php?page=numune-rapor&id={$numuneId}', '_blank');
}

// Modal temizleme
$('#yeniEtmenModal').on('hidden.bs.modal', function() {
    $('#etmenForm')[0].reset();
    $('#etmen_id').val('');
    $('#yeniEtmenModal .modal-title').html('<i class="fas fa-plus me-2"></i>Yeni Etmen Sonucu');
});

// Form validasyonu
$('#etmenForm').on('submit', function(e) {
    const etmenAdi = $('#etmen_adi').val();
    const bulasikMi = $('#bulasik_mi').val();

    if (!etmenAdi || !bulasikMi) {
        e.preventDefault();
        alert('Lütfen zorunlu alanları doldurun.');
        return false;
    }
});

// Sayfa yüklendiğinde
$(document).ready(function() {
    // Tooltip'leri başlat
    $('[data-bs-toggle="tooltip"]').tooltip();
});
</script>
EOD;

require_once 'views/layout/footer.php';
?>

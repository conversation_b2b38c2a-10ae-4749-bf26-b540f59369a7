<?php
/**
 * Tanım Controller Sınıfı
 * Sistem tanımları yönetimi
 */

class TanimController {
    private $genelTanimModel;
    private $urunModel;
    private $birimModel;
    
    public function __construct() {
        AuthController::checkSession();
        AuthController::requirePermission('TANIMLAMA_YAPABILIR');
        
        $this->genelTanimModel = new GenelTanim();
        $this->urunModel = new Urun();
        $this->birimModel = new LaboratuvarBirim();
    }
    
    // Ana tanım sayfası
    public function index() {
        $data = [
            'user' => AuthController::getCurrentUser()
        ];
        
        require_once 'views/tanimlar/index.php';
    }
    
    // Ürünler
    public function urunler() {
        $page = $_GET['page_num'] ?? 1;
        $search = $_GET['search'] ?? '';
        
        $result = $this->urunModel->getUrunList($page, $search);
        
        $data = [
            'urunler' => $result['urunler'],
            'pagination' => [
                'total' => $result['total'],
                'pages' => $result['pages'],
                'current_page' => $result['current_page']
            ],
            'search' => $search,
            'user' => AuthController::getCurrentUser()
        ];
        
        require_once 'views/tanimlar/urunler.php';
    }
    
    // Laboratuvar birimleri
    public function birimler() {
        $page = $_GET['page_num'] ?? 1;
        $search = $_GET['search'] ?? '';
        
        $result = $this->birimModel->getBirimList($page, $search);
        
        $data = [
            'birimler' => $result['birimler'],
            'pagination' => [
                'total' => $result['total'],
                'pages' => $result['pages'],
                'current_page' => $result['current_page']
            ],
            'search' => $search,
            'user' => AuthController::getCurrentUser()
        ];
        
        require_once 'views/tanimlar/birimler.php';
    }
    
    // Genel tanımlar
    public function genelTanimlar() {
        $sahibi = $_GET['sahibi'] ?? GenelTanim::FIRMALAR;
        $page = $_GET['page_num'] ?? 1;
        $search = $_GET['search'] ?? '';
        
        $result = $this->genelTanimModel->getListBySahibi($sahibi, $page, $search);
        $sahibiAciklama = $this->genelTanimModel->getSahibiAciklama($sahibi);
        $sahibiKodlari = $this->genelTanimModel->getAllSahibiKodlari();
        
        $data = [
            'records' => $result['records'],
            'pagination' => [
                'total' => $result['total'],
                'pages' => $result['pages'],
                'current_page' => $result['current_page']
            ],
            'sahibi' => $sahibi,
            'sahibi_aciklama' => $sahibiAciklama,
            'sahibi_kodlari' => $sahibiKodlari,
            'search' => $search,
            'user' => AuthController::getCurrentUser()
        ];
        
        require_once 'views/tanimlar/genel.php';
    }
    
    // Kaydet
    public function save() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Location: index.php?page=tanimlar');
            exit;
        }
        
        $type = $_POST['type'] ?? '';
        $result = ['success' => false];
        
        switch ($type) {
            case 'urun':
                $result = $this->saveUrun();
                break;
                
            case 'birim':
                $result = $this->saveBirim();
                break;
                
            case 'genel':
                $result = $this->saveGenelTanim();
                break;
                
            default:
                $_SESSION['error'] = 'Geçersiz işlem türü.';
                header('Location: index.php?page=tanimlar');
                exit;
        }
        
        if ($result['success']) {
            $_SESSION['success'] = SUCCESS_MESSAGES['save_success'];
        } else {
            if (isset($result['errors'])) {
                $_SESSION['form_errors'] = $result['errors'];
            } else {
                $_SESSION['error'] = $result['error'] ?? ERROR_MESSAGES['save_error'];
            }
        }
        
        // Geri yönlendirme
        $redirectPage = $_POST['redirect'] ?? 'tanimlar';
        header("Location: index.php?page=$redirectPage");
        exit;
    }
    
    private function saveUrun() {
        $data = [
            'id' => $_POST['id'] ?? 0,
            'urunadi_t' => $_POST['urunadi_t'] ?? '',
            'aciklama' => $_POST['aciklama'] ?? '',
            'aktif' => isset($_POST['aktif']) ? 1 : 0
        ];
        
        return $this->urunModel->saveUrun($data);
    }
    
    private function saveBirim() {
        $data = [
            'id' => $_POST['id'] ?? 0,
            'birimadi' => $_POST['birimadi'] ?? '',
            'aciklama' => $_POST['aciklama'] ?? '',
            'aktif' => isset($_POST['aktif']) ? 1 : 0
        ];
        
        return $this->birimModel->saveBirim($data);
    }
    
    private function saveGenelTanim() {
        $id = $_POST['id'] ?? 0;
        $sahibi = $_POST['sahibi'] ?? 0;
        $izahat = $_POST['izahat'] ?? '';
        $aciklama = $_POST['aciklama'] ?? '';
        
        if ($id > 0) {
            return $this->genelTanimModel->updateTanim($id, $izahat, $aciklama);
        } else {
            return $this->genelTanimModel->addTanim($sahibi, $izahat, $aciklama);
        }
    }
    
    // Silme
    public function delete() {
        $type = $_GET['type'] ?? '';
        $id = $_GET['id'] ?? 0;
        
        if ($id <= 0) {
            $_SESSION['error'] = ERROR_MESSAGES['record_not_found'];
            header('Location: index.php?page=tanimlar');
            exit;
        }
        
        $result = false;
        
        switch ($type) {
            case 'urun':
                $result = $this->urunModel->delete($id);
                break;
                
            case 'birim':
                $result = $this->birimModel->delete($id);
                break;
                
            case 'genel':
                $result = $this->genelTanimModel->delete($id);
                break;
        }
        
        if ($result) {
            $_SESSION['success'] = SUCCESS_MESSAGES['delete_success'];
        } else {
            $_SESSION['error'] = ERROR_MESSAGES['delete_error'];
        }
        
        $redirectPage = $_GET['redirect'] ?? 'tanimlar';
        header("Location: index.php?page=$redirectPage");
        exit;
    }
    
    // Durum değiştirme (aktif/pasif)
    public function toggleStatus() {
        $type = $_GET['type'] ?? '';
        $id = $_GET['id'] ?? 0;
        
        if ($id <= 0) {
            $_SESSION['error'] = ERROR_MESSAGES['record_not_found'];
            header('Location: index.php?page=tanimlar');
            exit;
        }
        
        $result = false;
        
        switch ($type) {
            case 'urun':
                $result = $this->urunModel->toggleStatus($id);
                break;
                
            case 'birim':
                $result = $this->birimModel->toggleStatus($id);
                break;
        }
        
        if ($result) {
            $_SESSION['success'] = 'Durum başarıyla değiştirildi.';
        } else {
            $_SESSION['error'] = 'Durum değiştirme sırasında hata oluştu.';
        }
        
        $redirectPage = $_GET['redirect'] ?? 'tanimlar';
        header("Location: index.php?page=$redirectPage");
        exit;
    }
}
?>

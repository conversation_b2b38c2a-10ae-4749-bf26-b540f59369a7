<?php
require_once 'config/database.php';

try {
    $db = new Database();
    
    echo "<h2>lab_numuneler Tablo Yapısı</h2>";
    
    $sql = "DESCRIBE lab_numuneler";
    $columns = $db->fetchAll($sql);
    
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr style='background: #f0f0f0;'><th><PERSON></th><th>Veri Tipi</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    
    $existingColumns = [];
    foreach ($columns as $col) {
        $existingColumns[] = $col['Field'];
        echo "<tr>";
        echo "<td>{$col['Field']}</td>";
        echo "<td>{$col['Type']}</td>";
        echo "<td>{$col['Null']}</td>";
        echo "<td>{$col['Key']}</td>";
        echo "<td>{$col['Default']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h3>Eks<PERSON>ntrolü</h3>";
    
    $requiredColumns = [
        'ulke' => 'VARCHAR(100)',
        'numune_alindigiyer' => 'VARCHAR(255)',
        'numune_sahibi' => 'VARCHAR(255)',
        'lotno' => 'VARCHAR(50)',
        'teslim_eden' => 'VARCHAR(100)',
        'teslim_alan' => 'VARCHAR(100)',
        'gelis_saati' => 'TIME'
    ];
    
    $missingColumns = [];
    foreach ($requiredColumns as $column => $type) {
        if (!in_array($column, $existingColumns)) {
            $missingColumns[$column] = $type;
        }
    }
    
    if (empty($missingColumns)) {
        echo "<div style='color: green;'>✅ Tüm gerekli alanlar mevcut!</div>";
    } else {
        echo "<div style='color: red;'>❌ Eksik alanlar bulundu:</div>";
        echo "<ul>";
        foreach ($missingColumns as $column => $type) {
            echo "<li><strong>$column</strong> ($type)</li>";
        }
        echo "</ul>";
        
        echo "<h4>Eksik Alanları Eklemek İçin SQL Komutları:</h4>";
        echo "<textarea style='width: 100%; height: 200px;'>";
        foreach ($missingColumns as $column => $type) {
            echo "ALTER TABLE lab_numuneler ADD COLUMN $column $type;\n";
        }
        echo "</textarea>";
    }
    
} catch (Exception $e) {
    echo "<div style='color: red;'>Hata: " . $e->getMessage() . "</div>";
}
?>

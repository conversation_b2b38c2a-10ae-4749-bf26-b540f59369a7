<?php
require_once 'models/Log.php';

class LogController {
    private $logModel;

    public function __construct() {
        $this->logModel = new Log();
    }

    // Log listesi
    public function index() {
        AuthController::requirePermission('TANIMLAMA_YAPABILIR'); // Admin yetkisi gerekli

        // Filtreleme parametreleri
        $filters = [
            'user_name' => $_GET['user_name'] ?? '',
            'action' => $_GET['action'] ?? '',
            'table_name' => $_GET['table_name'] ?? '',
            'baslangic_tarihi' => $_GET['baslangic_tarihi'] ?? '',
            'bitis_tarihi' => $_GET['bitis_tarihi'] ?? ''
        ];

        $currentPage = max(1, intval($_GET['page'] ?? 1));

        // Log verilerini al
        $result = $this->logModel->getLogList($currentPage, $filters);

        // Dropdown verileri
        $tableNames = $this->logModel->getTableNames();
        $userNames = $this->logModel->getUserNames();

        $data = [
            'logs' => $result['logs'],
            'pagination' => [
                'total' => $result['total'],
                'pages' => $result['pages'],
                'current_page' => $result['current_page']
            ],
            'filters' => $filters,
            'table_names' => $tableNames,
            'user_names' => $userNames,
            'user' => AuthController::getCurrentUser()
        ];

        require_once 'views/log/index.php';
    }

    // Log detayları
    public function details($id) {
        AuthController::requirePermission('TANIMLAMA_YAPABILIR');

        $log = $this->logModel->getLogDetails($id);
        if (!$log) {
            $_SESSION['error'] = ERROR_MESSAGES['record_not_found'];
            header('Location: index.php?page=log-listesi');
            exit;
        }

        $data = [
            'log' => $log,
            'user' => AuthController::getCurrentUser()
        ];

        require_once 'views/log/details.php';
    }

    // Excel export
    public function export() {
        AuthController::requirePermission('TANIMLAMA_YAPABILIR');

        $filters = [
            'user_name' => $_GET['user_name'] ?? '',
            'action' => $_GET['action'] ?? '',
            'table_name' => $_GET['table_name'] ?? '',
            'baslangic_tarihi' => $_GET['baslangic_tarihi'] ?? '',
            'bitis_tarihi' => $_GET['bitis_tarihi'] ?? ''
        ];

        // Tüm kayıtları al (sayfalama olmadan)
        $result = $this->logModel->getLogList(1, $filters);
        $logs = $result['logs'];

        // Excel başlıkları
        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment; filename="log_gecmisi_' . date('Y-m-d') . '.xls"');
        header('Pragma: no-cache');
        header('Expires: 0');

        echo '<table border="1">';
        echo '<tr>';
        echo '<th>ID</th>';
        echo '<th>Tarih/Saat</th>';
        echo '<th>Kullanıcı</th>';
        echo '<th>İşlem</th>';
        echo '<th>Tablo</th>';
        echo '<th>Kayıt ID</th>';
        echo '<th>Lab Kayıt No</th>';
        echo '<th>Açıklama</th>';
        echo '<th>IP Adresi</th>';
        echo '</tr>';

        foreach ($logs as $log) {
            echo '<tr>';
            echo '<td>' . safe_html($log['id']) . '</td>';
            echo '<td>' . safe_date($log['created_at']) . '</td>';
            echo '<td>' . safe_html($log['user_name']) . '</td>';
            echo '<td>' . safe_html($log['action']) . '</td>';
            echo '<td>' . safe_html($log['table_name']) . '</td>';
            echo '<td>' . safe_html($log['record_id']) . '</td>';
            echo '<td>' . safe_html($log['LAB_KAYITNO']) . '</td>';
            echo '<td>' . safe_html($log['description']) . '</td>';
            echo '<td>' . safe_html($log['ip_address']) . '</td>';
            echo '</tr>';
        }

        echo '</table>';
        exit;
    }
}
?>

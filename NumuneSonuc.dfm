object frmNumuneSonuc: TfrmNumuneSonuc
  Left = 0
  Top = 0
  ClientHeight = 493
  ClientWidth = 981
  Caption = 'Numune Sonu'#231
  OnShow = UniFormShow
  WindowState = wsMaximized
  OldCreateOrder = False
  MonitoredKeys.Keys = <>
  PixelsPerInch = 96
  TextHeight = 13
  object UniPanel1: TUniPanel
    Left = 0
    Top = 449
    Width = 981
    Height = 44
    Align = alBottom
    TabOrder = 0
    object UniButton3: TUniButton
      AlignWithMargins = True
      Left = 746
      Top = 4
      Width = 135
      Height = 36
      Visible = False
      Caption = 'Sonu'#231' D'#252'zenle'
      Align = alRight
      TabOrder = 1
      OnClick = UniButton3Click
    end
    object UniButton5: TUniButton
      AlignWithMargins = True
      Left = 605
      Top = 4
      Width = 135
      Height = 36
      Caption = 'Sonu'#231' Gir'
      Align = alRight
      TabOrder = 2
      OnClick = UniButton5Click
    end
    object UniButton6: TUniButton
      AlignWithMargins = True
      Left = 887
      Top = 4
      Width = 90
      Height = 36
      Caption = 'Kapat'
      Align = alRight
      TabOrder = 3
      OnClick = UniButton6Click
    end
    object UniButton2: TUniButton
      AlignWithMargins = True
      Left = 4
      Top = 4
      Width = 93
      Height = 36
      Caption = 'Kabul Et'
      Align = alLeft
      TabOrder = 4
      OnClick = UniButton2Click
    end
    object UniButton4: TUniButton
      AlignWithMargins = True
      Left = 103
      Top = 4
      Width = 112
      Height = 36
      Caption = 'Geri  G'#246'nder'
      Align = alLeft
      TabOrder = 5
      OnClick = UniButton4Click
    end
  end
  object UniDBGrid1: TUniDBGrid
    AlignWithMargins = True
    Left = 3
    Top = 79
    Width = 975
    Height = 367
    DataSource = dsNumuneKabul
    Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgRowSelect, dgCheckSelect, dgAlwaysShowSelection, dgConfirmDelete, dgMultiSelect, dgAutoRefreshRow, dgDontShowSelected]
    ReadOnly = True
    LoadMask.Message = 'Yukleniyor...'
    Align = alClient
    TabOrder = 1
    OnColumnSort = UniDBGrid1ColumnSort
    OnDrawColumnCell = UniDBGrid1DrawColumnCell
    Columns = <
      item
        FieldName = 'SONUCCU_KABULETTI'
        Title.Caption = 'Kabul Edldimi'
        Width = 51
        Alignment = taLeftJustify
        Sortable = True
        CheckBoxField.BooleanFieldOnly = False
        CheckBoxField.FieldValues = '1;0'
        CheckBoxField.DisplayValues = 'Evet;Hay'#305'r'
      end
      item
        FieldName = 'birimadi'
        Title.Caption = 'Birim Ad'#305
        Width = 120
        Sortable = True
      end
      item
        FieldName = 'ANALIZ_SONUCU'
        Title.Caption = 'Analiz Sonucu'
        Width = 112
        Sortable = True
      end
      item
        FieldName = 'ANALIZ_TARIHI'
        Title.Caption = 'Analiz Tarihi'
        Width = 100
      end
      item
        FieldName = 'GELIS_TARIHI'
        Title.Caption = 'Geli'#351' Tarihi'
        Width = 77
        Sortable = True
      end
      item
        FieldName = 'GELIS_SAATI'
        Title.Caption = 'Geli'#351' Saati'
        Width = 74
        Sortable = True
      end
      item
        FieldName = 'NUMUNE_TURU'
        Title.Caption = 'Numune T'#252'r'#252
        Width = 123
        Sortable = True
      end
      item
        FieldName = 'NUMUNE_ALT_TURU'
        Title.Caption = 'Alt T'#252'r'#252
        Width = 124
        Sortable = True
      end
      item
        FieldName = 'LAB_KAYITNO'
        Title.Caption = 'Lab.Kay'#305't No'
        Width = 136
        Sortable = True
      end
      item
        FieldName = 'ETIKET_NO'
        Title.Caption = 'Etiket No'
        Width = 80
        Sortable = True
      end
      item
        FieldName = 'BASVURU_NO'
        Title.Caption = 'Ba'#351'vuru No'
        Width = 80
        Sortable = True
      end
      item
        FieldName = 'MUHUR_NO'
        Title.Caption = 'M'#252'h'#252'r No'
        Width = 80
        Sortable = True
      end
      item
        FieldName = 'URUNADI_T'
        Title.Caption = #220'r'#252'n Ad'#305
        Width = 204
        Sortable = True
      end
      item
        FieldName = 'URUN_MIKTARI'
        Title.Caption = #220'r'#252'n Miktar'#305
        Width = 100
        Sortable = True
      end
      item
        FieldName = 'MIKTAR_BIRIM'
        Title.Caption = 'Miktar Birim'
        Width = 80
        Sortable = True
      end
      item
        FieldName = 'ETMENADI'
        Title.Caption = 'Etmenler'
        Width = 222
        Sortable = True
        DisplayMemo = True
      end
      item
        FieldName = 'ANALIZ_METHODU'
        Title.Caption = 'Analiz Methodu'
        Width = 100
      end
      item
        FieldName = 'ONHAZIRLIKCI_KABULTARIHI'
        Title.Caption = #214'n Haz'#305'rl'#305'k Kabul Tarihi'
        Width = 150
      end
      item
        FieldName = 'ONHAZIRLIKCI_KABULSAATI'
        Title.Caption = #214'n Haz'#305'rl'#305'k Kabul Saati'
        Width = 150
      end
      item
        FieldName = 'ONHAZIRLIKCI_HAZYAPTARIH'
        Title.Caption = #214'n Haz'#305'rl'#305'k G'#246'nderim Tarihi'
        Width = 150
      end
      item
        FieldName = 'ONHAZIRLIKCI_HAZYAPSAAT'
        Title.Caption = #214'n Haz'#305'rl'#305'k G'#246'nderim Saati'
        Width = 150
      end>
  end
  object UniPanel2: TUniPanel
    Left = 0
    Top = 0
    Width = 981
    Height = 76
    Align = alTop
    TabOrder = 2
    BorderStyle = ubsNone
    object chkOnhazirlik: TUniRadioButton
      Left = 9
      Top = 10
      Width = 185
      Height = 17
      Caption = 'Sonu'#231' Girilenler'
      TabOrder = 1
    end
    object chkOnhazirlikOlmayan: TUniRadioButton
      Left = 9
      Top = 32
      Width = 185
      Height = 17
      Checked = True
      Caption = 'Sonu'#231' Girilmeyenler'
      TabOrder = 2
    end
    object UniRadioButton3: TUniRadioButton
      Left = 9
      Top = 54
      Width = 113
      Height = 17
      Caption = 'T'#252'm'#252
      TabOrder = 3
    end
    object UniDateTimePicker1: TUniDateTimePicker
      Left = 208
      Top = 31
      Width = 120
      DateTime = 45320.000000000000000000
      DateFormat = 'dd/MM/yyyy'
      TimeFormat = 'HH:mm:ss'
      TabOrder = 4
    end
    object UniLabel1: TUniLabel
      Left = 208
      Top = 12
      Width = 73
      Height = 13
      Caption = 'Ba'#351'lang'#305#231' Tarihi'
      TabOrder = 5
    end
    object UniDateTimePicker2: TUniDateTimePicker
      Left = 337
      Top = 31
      Width = 120
      DateTime = 45320.000000000000000000
      DateFormat = 'dd/MM/yyyy'
      TimeFormat = 'HH:mm:ss'
      TabOrder = 6
    end
    object UniLabel2: TUniLabel
      Left = 337
      Top = 12
      Width = 48
      Height = 13
      Caption = 'Biti'#351' Tarihi'
      TabOrder = 7
    end
    object UniButton1: TUniButton
      Left = 712
      Top = 14
      Width = 75
      Height = 41
      Caption = 'Listele'
      TabOrder = 8
      OnClick = UniButton1Click
    end
    object UniButton10: TUniButton
      AlignWithMargins = True
      Left = 821
      Top = 5
      Width = 157
      Height = 56
      Margins.Left = 16
      Margins.Top = 5
      Margins.Bottom = 15
      Caption = 'Ekran Ayarlar'#305'n'#305' Kaydet'
      Align = alRight
      TabOrder = 9
      OnClick = UniButton10Click
      ExplicitLeft = 1112
      ExplicitTop = 3
      ExplicitHeight = 43
    end
    object UniLabel3: TUniLabel
      Left = 463
      Top = 14
      Width = 40
      Height = 13
      Caption = 'Kay'#305't No'
      TabOrder = 10
    end
    object edtKayitNo: TUniEdit
      Left = 462
      Top = 31
      Width = 121
      TabOrder = 11
    end
    object edtMuhurNo: TUniEdit
      Left = 585
      Top = 31
      Width = 121
      TabOrder = 12
    end
    object UniLabel4: TUniLabel
      Left = 586
      Top = 14
      Width = 46
      Height = 13
      Caption = 'M'#252'h'#252'r No'
      TabOrder = 13
    end
  end
  object dsNumuneKabul: TDataSource
    DataSet = tblNumuneKabul
    Left = 272
    Top = 192
  end
  object tblNumuneKabul: TpFIBDataSet
    UpdateSQL.Strings = (
      'UPDATE LAB_ATAMALAR'
      'SET '
      '    NUMUNE_ID = :NUMUNE_ID,'
      '    BIRIM_ID = :BIRIM_ID,'
      '    ATAMA_TARIHI = :ATAMA_TARIHI,'
      '    ATAMA_SAATI = :ATAMA_SAATI,'
      '    ATAYAN_KULLANICI = :ATAYAN_KULLANICI,'
      '    KARANTINA_ETMEN_SAYISI = :KARANTINA_ETMEN_SAYISI,'
      '    ANALIZ_SONUCU = :ANALIZ_SONUCU,'
      '    BULASIKISE = :BULASIKISE,'
      '    BULASIK_OLDUGU_ETMEN = :BULASIK_OLDUGU_ETMEN,'
      '    ANALIZ_YAPAN_KULLANICI = :ANALIZ_YAPAN_KULLANICI,'
      '    ANALIZ_TARIHI = :ANALIZ_TARIHI,'
      '    ANALIZ_METHODU = :ANALIZ_METHODU,'
      '    SONUC_GIRIS_ZAMANI = :SONUC_GIRIS_ZAMANI,'
      '    SONUCCU_KABULETTI = :SONUCCU_KABULETTI,'
      '    SONUCCU_KABULTARIHI = :SONUCCU_KABULTARIHI,'
      '    SONUCCU_KABULSAATI = :SONUCCU_KABULSAATI,'
      '    ONHAZIRLIKCI_KABULETTI = :ONHAZIRLIKCI_KABULETTI,'
      '    ONHAZIRLIKCI_KABULTARIHI = :ONHAZIRLIKCI_KABULTARIHI,'
      '    ONHAZIRLIKCI_KABULSAATI = :ONHAZIRLIKCI_KABULSAATI'
      'WHERE'
      '    ID = :OLD_ID'
      '    ')
    DeleteSQL.Strings = (
      'DELETE FROM'
      '    LAB_ATAMALAR'
      'WHERE'
      '        ID = :OLD_ID'
      '    ')
    InsertSQL.Strings = (
      'INSERT INTO LAB_ATAMALAR('
      '    ID,'
      '    NUMUNE_ID,'
      '    BIRIM_ID,'
      '    ATAMA_TARIHI,'
      '    ATAMA_SAATI,'
      '    ATAYAN_KULLANICI,'
      '    KARANTINA_ETMEN_SAYISI,'
      '    ANALIZ_SONUCU,'
      '    BULASIKISE,'
      '    BULASIK_OLDUGU_ETMEN,'
      '    ANALIZ_YAPAN_KULLANICI,'
      '    ANALIZ_TARIHI,'
      '    ANALIZ_METHODU,'
      '    SONUC_GIRIS_ZAMANI,'
      '    SONUCCU_KABULETTI,'
      '    SONUCCU_KABULTARIHI,'
      '    SONUCCU_KABULSAATI,'
      '    ONHAZIRLIKCI_KABULETTI,'
      '    ONHAZIRLIKCI_KABULTARIHI,'
      '    ONHAZIRLIKCI_KABULSAATI'
      ')'
      'VALUES('
      '    :ID,'
      '    :NUMUNE_ID,'
      '    :BIRIM_ID,'
      '    :ATAMA_TARIHI,'
      '    :ATAMA_SAATI,'
      '    :ATAYAN_KULLANICI,'
      '    :KARANTINA_ETMEN_SAYISI,'
      '    :ANALIZ_SONUCU,'
      '    :BULASIKISE,'
      '    :BULASIK_OLDUGU_ETMEN,'
      '    :ANALIZ_YAPAN_KULLANICI,'
      '    :ANALIZ_TARIHI,'
      '    :ANALIZ_METHODU,'
      '    :SONUC_GIRIS_ZAMANI,'
      '    :SONUCCU_KABULETTI,'
      '    :SONUCCU_KABULTARIHI,'
      '    :SONUCCU_KABULSAATI,'
      '    :ONHAZIRLIKCI_KABULETTI,'
      '    :ONHAZIRLIKCI_KABULTARIHI,'
      '    :ONHAZIRLIKCI_KABULSAATI'
      ')')
    RefreshSQL.Strings = (
      'SELECT LAB_ATAMALAR.*,lab_numuneler.*,urunler.urunadi_t'
      ' ,LAB_ATAMALAR.ANALIZ_SONUCU'
      ' FROM LAB_ATAMALAR'
      
        'inner join lab_numuneler on (LAB_ATAMALAR.NUMUNE_ID=lab_numunele' +
        'r.id)'
      'left join urunler on (urunler.id=lab_numuneler.urun_id)'
      ''
      'where(  1=1'
      '     ) and (     LAB_ATAMALAR.ID = :OLD_ID'
      '     )'
      '    ')
    SelectSQL.Strings = (
      'SELECT LAB_ATAMALAR.*,lab_numuneler.*,urunler.urunadi_t'
      ' ,LAB_ATAMALAR.ANALIZ_SONUCU'
      ' FROM LAB_ATAMALAR'
      
        'inner join lab_numuneler on (LAB_ATAMALAR.NUMUNE_ID=lab_numunele' +
        'r.id)'
      'left join urunler on (urunler.id=lab_numuneler.urun_id)'
      ''
      'where 1=1')
    AutoUpdateOptions.UpdateTableName = 'SERTIFIKA'
    AutoUpdateOptions.KeyFields = 'ID'
    AutoUpdateOptions.GeneratorName = 'GEN_SERTIFIKA_ID'
    AutoUpdateOptions.WhenGetGenID = wgBeforePost
    Transaction = UniMainModule.tr
    Database = UniMainModule.db
    Left = 168
    Top = 192
  end
  object tmpSql: TpFIBQuery
    Transaction = UniMainModule.tr
    Database = UniMainModule.db
    Left = 424
    Top = 200
  end
end

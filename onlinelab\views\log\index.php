<?php
$pageTitle = 'İşlem Geçmişi';
$pageSubtitle = 'Sistem log kayıtları';
require_once 'views/layout/header.php';

// Sayfalama değişkenleri
$currentPage = $data['pagination']['current_page'];
$totalPages = $data['pagination']['pages'];
$totalRecords = $data['pagination']['total'];
?>

<style>
/* Log sayfası özel stilleri */
.log-filters {
    background: #f8f9fa;
    border-radius: 0.375rem;
    padding: 1rem;
    margin-bottom: 1rem;
}

.log-action-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

.log-table td {
    vertical-align: middle;
    font-size: 0.875rem;
}

.log-description {
    max-width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
</style>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-white">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-history me-2"></i>
                        İşlem Geçmişi
                        <span class="badge bg-primary"><?= safe_number($totalRecords) ?></span>
                    </h5>

                    <div class="btn-group">
                        <a href="index.php?page=log-listesi&action=export&<?= http_build_query($data['filters']) ?>"
                           class="btn btn-outline-success btn-sm">
                            <i class="fas fa-file-excel me-1"></i>Excel İndir
                        </a>
                    </div>
                </div>
            </div>

            <!-- Filtreleme -->
            <div class="card-body">
                <form method="GET" action="index.php" class="log-filters">
                    <input type="hidden" name="page" value="log-listesi">

                    <div class="row g-3">
                        <div class="col-md-3">
                            <label class="form-label">Kullanıcı</label>
                            <select class="form-select form-select-sm" name="user_name">
                                <option value="">Tüm kullanıcılar</option>
                                <?php foreach ($data['user_names'] as $userName): ?>
                                    <option value="<?= safe_html($userName) ?>"
                                            <?= safe_selected($data['filters']['user_name'], $userName) ?>>
                                        <?= safe_html($userName) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="col-md-2">
                            <label class="form-label">İşlem</label>
                            <input type="text" class="form-control form-control-sm"
                                   name="action" <?= safe_value($data['filters']['action']) ?>
                                   placeholder="INSERT, UPDATE...">
                        </div>

                        <div class="col-md-2">
                            <label class="form-label">Tablo</label>
                            <select class="form-select form-select-sm" name="table_name">
                                <option value="">Tüm tablolar</option>
                                <?php foreach ($data['table_names'] as $tableName): ?>
                                    <option value="<?= safe_html($tableName) ?>"
                                            <?= safe_selected($data['filters']['table_name'], $tableName) ?>>
                                        <?= safe_html($tableName) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="col-md-2">
                            <label class="form-label">Başlangıç</label>
                            <input type="date" class="form-control form-control-sm"
                                   name="baslangic_tarihi" <?= safe_value($data['filters']['baslangic_tarihi']) ?>>
                        </div>

                        <div class="col-md-2">
                            <label class="form-label">Bitiş</label>
                            <input type="date" class="form-control form-control-sm"
                                   name="bitis_tarihi" <?= safe_value($data['filters']['bitis_tarihi']) ?>>
                        </div>

                        <div class="col-md-1">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-sm">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <?php if (array_filter($data['filters'])): ?>
                        <div class="mt-2">
                            <a href="index.php?page=log-listesi" class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-times me-1"></i>Filtreleri Temizle
                            </a>
                        </div>
                    <?php endif; ?>
                </form>

                <!-- Tablo -->
                <div class="table-responsive">
                    <table class="table table-hover table-sm log-table" id="logTable">
                        <thead class="table-light">
                            <tr>
                                <th style="width: 60px;">ID</th>
                                <th style="width: 140px;">Tarih/Saat</th>
                                <th style="width: 100px;">Kullanıcı</th>
                                <th style="width: 80px;">İşlem</th>
                                <th style="width: 120px;">Tablo</th>
                                <th style="width: 80px;">Kayıt ID</th>
                                <th style="width: 120px;">Lab Kayıt No</th>
                                <th>Açıklama</th>
                                <th style="width: 100px;">IP Adresi</th>
                                <th style="width: 80px;">İşlemler</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (!empty($data['logs'])): ?>
                                <?php foreach ($data['logs'] as $log): ?>
                                    <tr>
                                        <td><?= safe_html($log['id']) ?></td>
                                        <td>
                                            <small><?= safe_date($log['created_at']) ?></small>
                                        </td>
                                        <td>
                                            <span class="badge bg-info"><?= safe_html($log['user_name']) ?></span>
                                        </td>
                                        <td>
                                            <?php
                                            $actionColors = [
                                                'INSERT' => 'bg-success',
                                                'UPDATE' => 'bg-warning',
                                                'DELETE' => 'bg-danger',
                                                'LOGIN' => 'bg-primary',
                                                'LOGOUT' => 'bg-secondary'
                                            ];
                                            $actionClass = $actionColors[$log['action']] ?? 'bg-dark';
                                            ?>
                                            <span class="badge <?= $actionClass ?> log-action-badge">
                                                <?= safe_html($log['action']) ?>
                                            </span>
                                        </td>
                                        <td><?= safe_html($log['table_name']) ?></td>
                                        <td><?= safe_html($log['record_id']) ?></td>
                                        <td>
                                            <?php if ($log['LAB_KAYITNO']): ?>
                                                <strong class="text-primary"><?= safe_html($log['LAB_KAYITNO']) ?></strong>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="log-description" title="<?= safe_html($log['description']) ?>">
                                                <?= safe_html($log['description']) ?>
                                            </div>
                                        </td>
                                        <td>
                                            <small class="text-muted"><?= safe_html($log['ip_address']) ?></small>
                                        </td>
                                        <td>
                                            <a href="index.php?page=log-detay&id=<?= $log['id'] ?>"
                                               class="btn btn-outline-primary btn-sm" title="Detayları Görüntüle">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <tr>
                                    <td colspan="10" class="text-center text-muted py-4">
                                        <i class="fas fa-inbox fa-2x mb-2"></i><br>
                                        Henüz log kaydı bulunmamaktadır.
                                    </td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Sayfalama -->
                <?php if ($totalPages > 1): ?>
                    <nav aria-label="Log sayfalama">
                        <ul class="pagination justify-content-center">
                            <?php if ($currentPage > 1): ?>
                                <li class="page-item">
                                    <a class="page-link" href="index.php?page=log-listesi&page=<?= $currentPage - 1 ?>&<?= http_build_query($data['filters']) ?>">
                                        <i class="fas fa-chevron-left"></i>
                                    </a>
                                </li>
                            <?php endif; ?>

                            <?php for ($i = max(1, $currentPage - 2); $i <= min($totalPages, $currentPage + 2); $i++): ?>
                                <li class="page-item <?= $i === $currentPage ? 'active' : '' ?>">
                                    <a class="page-link" href="index.php?page=log-listesi&page=<?= $i ?>&<?= http_build_query($data['filters']) ?>">
                                        <?= $i ?>
                                    </a>
                                </li>
                            <?php endfor; ?>

                            <?php if ($currentPage < $totalPages): ?>
                                <li class="page-item">
                                    <a class="page-link" href="index.php?page=log-listesi&page=<?= $currentPage + 1 ?>&<?= http_build_query($data['filters']) ?>">
                                        <i class="fas fa-chevron-right"></i>
                                    </a>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </nav>

                    <div class="text-center text-muted">
                        Toplam <?= safe_number($totalRecords) ?> kayıttan
                        <?= safe_number(($currentPage - 1) * RECORDS_PER_PAGE + 1) ?> -
                        <?= safe_number(min($currentPage * RECORDS_PER_PAGE, $totalRecords)) ?>
                        arası gösteriliyor
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php
$customJS = "
<script>
$(document).ready(function() {
    // DataTable başlatma
    if ($('#logTable tbody tr').length > 0) {
        $('#logTable').DataTable({
            paging: false, // Kendi sayfalamamızı kullanıyoruz
            searching: false, // Kendi filtremizi kullanıyoruz
            info: false,
            order: [[0, 'desc']], // ID'ye göre azalan sırala
            columnDefs: [
                { orderable: false, targets: [9] } // İşlemler sütunu sıralanamaz
            ],
            language: {
                url: 'https://cdn.datatables.net/plug-ins/1.11.5/i18n/tr.json'
            }
        });
    }

    // Tooltip'leri başlat
    $('[title]').tooltip();
});
</script>
";

require_once 'views/layout/footer.php';
?>

<?php
$pageTitle = 'Numune Kabul';
$pageSubtitle = 'Yeni numune kaydı oluştur';
require_once 'views/layout/header.php';

// Form verilerini al (hata durumunda geri doldurma için)
$formData = $_SESSION['form_data'] ?? [];
$formErrors = $_SESSION['form_errors'] ?? [];
unset($_SESSION['form_data'], $_SESSION['form_errors']);

// Mevcut numune verisi (düzenleme durumunda)
$numune = $data['numune'] ?? null;
$isEdit = $numune !== null;

// Form verilerini birleştir (öncelik: form_data > numune > default)
$values = array_merge([
    'id' => 0,
    'numune_turu' => '',
    'numune_alt_turu' => '',
    'LAB_KAYITNO' => '',
    'gonderen' => '',
    'gonderen_personel' => '',
    'firma_id' => '',
    'urun_id' => '',
    'mensei' => '',
    'ulke' => '',
    'numune_alindigiyer' => '',
    'numune_sahibi' => '',
    'basvuru_no' => '',
    'muhur_no' => '',
    'etiket_no' => '',
    'miktar' => '',
    'birim' => '',
    'barkod' => '',
    'lotno' => '',
    'teslim_eden' => '',
    'teslim_alan' => '',
    'ucret_durumu' => 'Ödenmedi',
    'aciklama' => ''
], $numune ?? [], $formData);
?>

<style>
    /* Choices.js özel stilleri */
    .choices {
        margin-bottom: 0;
    }

    .choices__inner {
        background-color: #fff;
        border: 1px solid #ced4da;
        border-radius: 0.375rem;
        font-size: 1rem;
        min-height: 38px;
        padding: 7.5px 7.5px 3.75px;
    }

    .choices__inner:focus {
        border-color: #86b7fe;
        outline: 0;
        box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
    }

    .choices__list--dropdown {
        border: 1px solid #ced4da;
        border-radius: 0.375rem;
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        z-index: 1050;
    }

    .choices__item--selectable.is-highlighted {
        background-color: #0d6efd;
    }

    .choices__placeholder {
        color: #6c757d;
    }

    .is-invalid .choices__inner {
        border-color: #dc3545;
    }

    .is-invalid .choices__inner:focus {
        border-color: #dc3545;
        box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.25);
    }

    .choices__button {
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath d='m.235 1.406 4.415 4.414L.235 10.234l.707.707L5.356 6.527l4.414 4.414.707-.707L6.063 5.82l4.414-4.414-.707-.707L5.356 5.113.942.699z'/%3e%3c/svg%3e");
    }

    .choices.is-disabled .choices__inner {
        background-color: #e9ecef;
        color: #6c757d;
        cursor: not-allowed;
    }

    .choices.is-disabled .choices__inner:hover {
        border-color: #ced4da;
    }

    /* Responsive düzenlemeler */
    @media (max-width: 768px) {
        .choices__list--dropdown {
            max-height: 200px;
        }
    }

    /* Timeline Stilleri */
    .timeline {
        position: relative;
        padding-left: 30px;
    }

    .timeline::before {
        content: '';
        position: absolute;
        left: 15px;
        top: 0;
        bottom: 0;
        width: 2px;
        background: linear-gradient(to bottom, #007bff, #e9ecef);
    }

    .timeline-item {
        position: relative;
        margin-bottom: 25px;
        padding-bottom: 20px;
    }

    .timeline-item:last-child {
        margin-bottom: 0;
        padding-bottom: 0;
    }

    .timeline-marker {
        position: absolute;
        left: -22px;
        top: 5px;
        width: 16px;
        height: 16px;
        background: #fff;
        border: 2px solid #007bff;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1;
    }

    .timeline-marker i {
        font-size: 6px;
        color: #007bff;
    }

    .timeline-item-latest .timeline-marker {
        border-color: #28a745;
        background: #28a745;
        box-shadow: 0 0 0 4px rgba(40, 167, 69, 0.2);
        animation: pulse 2s infinite;
    }

    .timeline-item-latest .timeline-marker i {
        color: #fff;
    }

    @keyframes pulse {
        0% {
            box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.4);
        }
        70% {
            box-shadow: 0 0 0 10px rgba(40, 167, 69, 0);
        }
        100% {
            box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
        }
    }

    .timeline-content {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 15px;
        position: relative;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
    }

    .timeline-content:hover {
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        transform: translateY(-2px);
    }

    .timeline-content::before {
        content: '';
        position: absolute;
        left: -8px;
        top: 15px;
        width: 0;
        height: 0;
        border-top: 8px solid transparent;
        border-bottom: 8px solid transparent;
        border-right: 8px solid #e9ecef;
    }

    .timeline-content::after {
        content: '';
        position: absolute;
        left: -7px;
        top: 15px;
        width: 0;
        height: 0;
        border-top: 8px solid transparent;
        border-bottom: 8px solid transparent;
        border-right: 8px solid #f8f9fa;
    }

    .timeline-title {
        color: #495057;
        font-weight: 600;
        margin-bottom: 5px;
    }

    .timeline-body p {
        color: #6c757d;
        font-size: 0.9rem;
        line-height: 1.4;
    }

    /* Responsive timeline */
    @media (max-width: 768px) {
        .timeline {
            padding-left: 25px;
        }

        .timeline-marker {
            left: -20px;
        }

        .timeline-content {
            padding: 12px;
        }
    }
</style>

<div class="row">
    <!-- Numune Form -->
    <div class="<?= $isEdit ? 'col-lg-8' : 'col-lg-8 mx-auto' ?>">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-plus-circle me-2"></i>
                    <?= $isEdit ? 'Numune Düzenle' : 'Yeni Numune Kabul' ?>
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="index.php?page=numune-kabul&action=save" id="numuneForm" novalidate>
                    <input type="hidden" name="id" <?= safe_value($values['id']) ?>>
                    
                    <!-- Temel Bilgiler -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-primary border-bottom pb-2 mb-3">
                                <i class="fas fa-info-circle me-2"></i>Temel Bilgiler
                            </h6>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="numune_turu" class="form-label">
                                Numune Türü <span class="text-danger">*</span>
                            </label>
                            <select class="form-select <?= isset($formErrors['numune_turu']) ? 'is-invalid' : '' ?>"
                                    id="numune_turu" name="numune_turu" required>
                                <option value="">Seçiniz...</option>
                                <option value="İç Karantina" <?= safe_selected($values['numune_turu'], 'İç Karantina') ?>>
                                    İç Karantina
                                </option>
                                <option value="Dış Karantina" <?= safe_selected($values['numune_turu'], 'Dış Karantina') ?>>
                                    Dış Karantina
                                </option>
                            </select>
                            <?php if (isset($formErrors['numune_turu'])): ?>
                                <div class="invalid-feedback"><?= $formErrors['numune_turu'] ?></div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="numune_alt_turu" class="form-label">Numune Alt Türü</label>
                            <select class="form-select" id="numune_alt_turu" name="numune_alt_turu">
                                <option value="">Seçiniz...</option>
                                <!-- Bu seçenekler JavaScript ile doldurulacak -->
                            </select>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="LAB_KAYITNO" class="form-label">
                                Laboratuvar Kayıt No
                                <i class="fas fa-info-circle text-muted" 
                                   data-bs-toggle="tooltip" 
                                   title="Otomatik oluşturulur, manuel değiştirilebilir"></i>
                            </label>
                            <input type="text" 
                                   class="form-control <?= isset($formErrors['LAB_KAYITNO']) ? 'is-invalid' : '' ?>" 
                                   id="LAB_KAYITNO" 
                                   name="LAB_KAYITNO" 
                                   <?= safe_value($values['LAB_KAYITNO']) ?>
                                   placeholder="Otomatik oluşturulacak">
                            <?php if (isset($formErrors['LAB_KAYITNO'])): ?>
                                <div class="invalid-feedback"><?= $formErrors['LAB_KAYITNO'] ?></div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="col-md-6 mb-3 dis-karantina-field">
                            <label for="basvuru_no" class="form-label">Başvuru No</label>
                            <input type="text"
                                   class="form-control"
                                   id="basvuru_no"
                                   name="basvuru_no"
                                   <?= safe_value($values['basvuru_no']) ?>
                                   placeholder="Başvuru numarası">
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="muhur_no" class="form-label">Mühür No</label>
                            <input type="text"
                                   class="form-control"
                                   id="muhur_no"
                                   name="muhur_no"
                                   <?= safe_value($values['muhur_no']) ?>
                                   placeholder="Mühür numarası">
                        </div>
                    </div>
                    
                    <!-- Gönderen Bilgileri -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-primary border-bottom pb-2 mb-3">
                                <i class="fas fa-user me-2"></i>Gönderen Bilgileri
                            </h6>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="gonderen" class="form-label">
                                Gönderen <span class="text-danger">*</span>
                            </label>
                            <input type="text" 
                                   class="form-control <?= isset($formErrors['gonderen']) ? 'is-invalid' : '' ?>" 
                                   id="gonderen" 
                                   name="gonderen" 
                                   <?= safe_value($values['gonderen']) ?>
                                   placeholder="Gönderen kişi/kurum adı"
                                   required>
                            <?php if (isset($formErrors['gonderen'])): ?>
                                <div class="invalid-feedback"><?= $formErrors['gonderen'] ?></div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="gonderen_personel" class="form-label">Gönderen Personel</label>
                            <input type="text" 
                                   class="form-control" 
                                   id="gonderen_personel" 
                                   name="gonderen_personel" 
                                   <?= safe_value($values['gonderen_personel']) ?>
                                   placeholder="Personel adı">
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="firma_id" class="form-label">Firma</label>
                            <select class="form-select" id="firma_id" name="firma_id">
                                <option value="">Firma seçiniz...</option>
                                <?php foreach ($data['firmalar'] as $id => $firmaAdi): ?>
                                    <option value="<?= $id ?>" <?= safe_selected($values['firma_id'], $id) ?>>
                                        <?= safe_html($firmaAdi) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="mensei" class="form-label">Menşei (Ülke)</label>
                            <select class="form-select" id="mensei" name="mensei">
                                <option value="">Ülke seçiniz...</option>
                                <?php foreach ($data['ulkeler'] as $id => $ulkeAdi): ?>
                                    <option value="<?= safe_html($ulkeAdi) ?>"
                                            <?= safe_selected($values['mensei'], $ulkeAdi) ?>>
                                        <?= safe_html($ulkeAdi) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <!-- İç Karantina Alanları -->
                        <div class="col-md-6 mb-3 ic-karantina-field">
                            <label for="numune_alindigiyer" class="form-label">Numune Alındığı Yer</label>
                            <select class="form-select" id="numune_alindigiyer" name="numune_alindigiyer">
                                <option value="">Seçiniz...</option>
                                <?php foreach ($data['alindigiyer'] as $id => $yer): ?>
                                    <option value="<?= safe_html($yer) ?>"
                                            <?= safe_selected($values['numune_alindigiyer'], $yer) ?>>
                                        <?= safe_html($yer) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="col-md-6 mb-3 ic-karantina-field">
                            <label for="etiket_no" class="form-label">Etiket No / Üst Yazı Sayısı</label>
                            <input type="text"
                                   class="form-control"
                                   id="etiket_no"
                                   name="etiket_no"
                                   <?= safe_value($values['etiket_no']) ?>
                                   placeholder="Etiket numarası">
                        </div>

                        <!-- Dış Karantina Alanları -->
                        <div class="col-md-6 mb-3 dis-karantina-field">
                            <label for="numune_sahibi" class="form-label">Numune Sahibi</label>
                            <input type="text"
                                   class="form-control"
                                   id="numune_sahibi"
                                   name="numune_sahibi"
                                   <?= safe_value($values['numune_sahibi']) ?>
                                   placeholder="Numune sahibi">
                        </div>

                        <div class="col-md-6 mb-3 dis-karantina-field">
                            <label for="lotno" class="form-label">Lot No</label>
                            <input type="text"
                                   class="form-control"
                                   id="lotno"
                                   name="lotno"
                                   <?= safe_value($values['lotno']) ?>
                                   placeholder="Lot numarası">
                        </div>
                    </div>
                    
                    <!-- Numune Detayları -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-primary border-bottom pb-2 mb-3">
                                <i class="fas fa-vial me-2"></i>Numune Detayları
                            </h6>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="urun_id" class="form-label">
                                Ürün <span class="text-danger">*</span>
                            </label>
                            <select class="form-select <?= isset($formErrors['urun_id']) ? 'is-invalid' : '' ?>"
                                    id="urun_id" name="urun_id" required>
                                <option value="">Ürün seçiniz...</option>
                                <?php foreach ($data['urunler'] as $id => $urunAdi): ?>
                                    <option value="<?= $id ?>" <?= safe_selected($values['urun_id'], $id) ?>>
                                        <?= safe_html($urunAdi) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <?php if (isset($formErrors['urun_id'])): ?>
                                <div class="invalid-feedback"><?= $formErrors['urun_id'] ?></div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="barkod" class="form-label">Barkod / QR Code</label>
                            <div class="input-group">
                                <input type="text"
                                       class="form-control"
                                       id="barkod"
                                       name="barkod"
                                       <?= safe_value($values['barkod']) ?>
                                       placeholder="Barkod numarası">
                                <button type="button" class="btn btn-outline-secondary" onclick="generateBarcode()">
                                    <i class="fas fa-qrcode"></i>
                                </button>
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="miktar" class="form-label">
                                Miktar <span class="text-danger">*</span>
                            </label>
                            <input type="number" 
                                   class="form-control <?= isset($formErrors['miktar']) ? 'is-invalid' : '' ?>" 
                                   id="miktar" 
                                   name="miktar" 
                                   <?= safe_value($values['miktar']) ?>
                                   placeholder="0.00"
                                   step="0.01"
                                   min="0"
                                   required>
                            <?php if (isset($formErrors['miktar'])): ?>
                                <div class="invalid-feedback"><?= $formErrors['miktar'] ?></div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="birim" class="form-label">
                                Birim <span class="text-danger">*</span>
                            </label>
                            <select class="form-select <?= isset($formErrors['birim']) ? 'is-invalid' : '' ?>"
                                    id="birim" name="birim" required>
                                <option value="">Birim seçiniz...</option>
                                <!-- Ağırlık Birimleri -->
                                <option value="kg" <?= safe_selected($values['birim'], 'kg') ?>>kg (Kilogram)</option>
                                <option value="gr" <?= safe_selected($values['birim'], 'gr') ?>>gr (Gram)</option>
                                <option value="mg" <?= safe_selected($values['birim'], 'mg') ?>>mg (Miligram)</option>
                                <option value="ton" <?= safe_selected($values['birim'], 'ton') ?>>ton (Ton)</option>
                                <!-- Hacim Birimleri -->
                                <option value="lt" <?= safe_selected($values['birim'], 'lt') ?>>lt (Litre)</option>
                                <option value="ml" <?= safe_selected($values['birim'], 'ml') ?>>ml (Mililitre)</option>
                                <option value="m³" <?= safe_selected($values['birim'], 'm³') ?>>m³ (Metreküp)</option>
                                <option value="cm³" <?= safe_selected($values['birim'], 'cm³') ?>>cm³ (Santimetreküp)</option>
                                <!-- Sayı Birimleri -->
                                <option value="adet" <?= safe_selected($values['birim'], 'adet') ?>>adet (Adet)</option>
                                <option value="paket" <?= safe_selected($values['birim'], 'paket') ?>>paket (Paket)</option>
                                <option value="kutu" <?= safe_selected($values['birim'], 'kutu') ?>>kutu (Kutu)</option>
                                <option value="çuval" <?= safe_selected($values['birim'], 'çuval') ?>>çuval (Çuval)</option>
                                <option value="koli" <?= safe_selected($values['birim'], 'koli') ?>>koli (Koli)</option>
                                <option value="torba" <?= safe_selected($values['birim'], 'torba') ?>>torba (Torba)</option>
                                <!-- Uzunluk Birimleri -->
                                <option value="m" <?= safe_selected($values['birim'], 'm') ?>>m (Metre)</option>
                                <option value="cm" <?= safe_selected($values['birim'], 'cm') ?>>cm (Santimetre)</option>
                                <option value="mm" <?= safe_selected($values['birim'], 'mm') ?>>mm (Milimetre)</option>
                                <!-- Alan Birimleri -->
                                <option value="m²" <?= safe_selected($values['birim'], 'm²') ?>>m² (Metrekare)</option>
                                <option value="cm²" <?= safe_selected($values['birim'], 'cm²') ?>>cm² (Santimetrekare)</option>
                            </select>
                            <?php if (isset($formErrors['birim'])): ?>
                                <div class="invalid-feedback"><?= $formErrors['birim'] ?></div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="col-12 mb-3">
                            <label for="aciklama" class="form-label">Açıklama</label>
                            <textarea class="form-control"
                                      id="aciklama"
                                      name="aciklama"
                                      rows="3"
                                      placeholder="Numune hakkında ek bilgiler..."><?= safe_html($values['aciklama']) ?></textarea>
                        </div>
                    </div>


                    
                    <!-- Teslim Bilgileri -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-primary border-bottom pb-2 mb-3">
                                <i class="fas fa-handshake me-2"></i>Teslim Bilgileri
                            </h6>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="teslim_eden" class="form-label">Teslim Eden</label>
                            <input type="text"
                                   class="form-control"
                                   id="teslim_eden"
                                   name="teslim_eden"
                                   <?= safe_value($values['teslim_eden']) ?>
                                   placeholder="Teslim eden kişi">
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="teslim_alan" class="form-label">Teslim Alan</label>
                            <input type="text"
                                   class="form-control"
                                   id="teslim_alan"
                                   name="teslim_alan"
                                   <?= safe_value($values['teslim_alan']) ?>
                                   placeholder="Teslim alan kişi">
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="ucret_durumu" class="form-label">Ücret Durumu</label>
                            <select class="form-select" id="ucret_durumu" name="ucret_durumu">
                                <option value="Ödenmedi" <?= safe_selected($values['ucret_durumu'], 'Ödenmedi') ?>>Ödenmedi</option>
                                <option value="Ödendi" <?= safe_selected($values['ucret_durumu'], 'Ödendi') ?>>Ödendi</option>
                                <option value="Ücretsiz" <?= safe_selected($values['ucret_durumu'], 'Ücretsiz') ?>>Ücretsiz</option>
                            </select>
                        </div>
                    </div>

                    <!-- Butonlar -->
                    <div class="row">
                        <div class="col-12">
                            <div class="d-flex justify-content-between">
                                <a href="index.php?page=numune-listesi" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left me-2"></i>Geri Dön
                                </a>

                                <div>
                                    <button type="button" class="btn btn-outline-primary me-2" onclick="clearForm()">
                                        <i class="fas fa-eraser me-2"></i>Temizle
                                    </button>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>
                                        <?= $isEdit ? 'Güncelle' : 'Kaydet' ?>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- İşlem Geçmişi Timeline -->
    <?php if ($isEdit && !empty($data['logs'])): ?>
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-history me-2"></i>İşlem Geçmişi
                </h5>
            </div>
            <div class="card-body">
                <div class="timeline">
                    <?php foreach ($data['logs'] as $index => $log): ?>
                        <div class="timeline-item <?= $index === 0 ? 'timeline-item-latest' : '' ?>">
                            <div class="timeline-marker">
                                <i class="fas fa-circle"></i>
                            </div>
                            <div class="timeline-content">
                                <div class="timeline-header">
                                    <h6 class="timeline-title mb-1"><?= safe_html($log['islem']) ?></h6>
                                    <small class="text-muted">
                                        <i class="fas fa-clock me-1"></i>
                                        <?= date('d.m.Y H:i', strtotime($log['tarih'])) ?>
                                    </small>
                                </div>
                                <div class="timeline-body">
                                    <?php if (!empty($log['onceki_deger'])): ?>
                                        <p class="mb-1 text-muted small"><?= safe_html($log['onceki_deger']) ?></p>
                                    <?php endif; ?>
                                    <small class="text-muted">
                                        <i class="fas fa-user me-1"></i>
                                        <?= safe_html($log['kullanici']) ?>
                                    </small>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<?php
$icKarantinaTurleri = json_encode(array_values($data['ic_karantina_turleri'] ?? []));
$disKarantinaTurleri = json_encode(array_values($data['dis_karantina_turleri'] ?? []));

$customJS = <<<EOD
<script>
// Alt tür seçenekleri
const altTurSecenekleri = {
    'İç Karantina': {$icKarantinaTurleri},
    'Dış Karantina': {$disKarantinaTurleri}
};

// Choices.js instances
let choicesInstances = {};

// Sayfa yüklendiğinde
$(document).ready(function() {
    updateAltTurSecenekleri();
    updateFieldVisibility();

    // Eğer düzenleme modundaysa lab kayıt no'yu güncelleme
    if ($('#LAB_KAYITNO').val()) {
        $('#LAB_KAYITNO').prop('readonly', true);
    }

    // Tooltip'leri başlat
    $('[data-bs-toggle="tooltip"]').tooltip();

    // Choices.js'i başlat
    initializeChoices();
});

// Choices.js'i başlat
function initializeChoices() {
    // Türkçe dil ayarları
    const choicesConfig = {
        searchEnabled: true,
        searchPlaceholderValue: 'Arama yapın...',
        noResultsText: 'Sonuç bulunamadı',
        noChoicesText: 'Seçenek yok',
        itemSelectText: 'Seçmek için tıklayın',
        removeItemButton: true,
        shouldSort: false,
        placeholder: true,
        placeholderValue: 'Seçiniz...'
    };

    // Numune türü
    if (document.getElementById('numune_turu')) {
        choicesInstances.numune_turu = new Choices('#numune_turu', {
            ...choicesConfig,
            searchEnabled: false // Sadece 2 seçenek olduğu için arama kapalı
        });
    }

    // Numune alt türü
    if (document.getElementById('numune_alt_turu')) {
        choicesInstances.numune_alt_turu = new Choices('#numune_alt_turu', {
            ...choicesConfig,
            searchEnabled: false
        });
    }

    // Ürün seçimi
    if (document.getElementById('urun_id')) {
        choicesInstances.urun_id = new Choices('#urun_id', choicesConfig);
    }

    // Firma seçimi
    if (document.getElementById('firma_id')) {
        choicesInstances.firma_id = new Choices('#firma_id', choicesConfig);
    }

    // Menşei (Ülke) seçimi
    if (document.getElementById('mensei')) {
        choicesInstances.mensei = new Choices('#mensei', choicesConfig);
    }

    // Numune alındığı yer - Choices.js kullanmayalım, normal select olarak bırakalım
    // if (document.getElementById('numune_alindigiyer')) {
    //     choicesInstances.numune_alindigiyer = new Choices('#numune_alindigiyer', choicesConfig);
    // }

    // Birim seçimi
    if (document.getElementById('birim')) {
        choicesInstances.birim = new Choices('#birim', choicesConfig);
    }

    // Event listener'ları ekle
    addChoicesEventListeners();
}

// Choices.js event listener'ları
function addChoicesEventListeners() {
    // Numune türü değişikliği
    if (choicesInstances.numune_turu) {
        document.getElementById('numune_turu').addEventListener('change', function(event) {
            updateLabKayitNo();
        });
    }

    // Alt tür değişikliği
    if (choicesInstances.numune_alt_turu) {
        document.getElementById('numune_alt_turu').addEventListener('change', function(event) {
            updateMenseiLabel(event.target.value);
        });
    }
}

// Numune türü değiştiğinde
function updateLabKayitNo() {
    const numuneTuru = $('#numune_turu').val();
    const labKayitNo = $('#LAB_KAYITNO');

    // Alt tür seçeneklerini güncelle
    updateAltTurSecenekleri();

    // Alan görünürlüğünü kontrol et
    updateFieldVisibility();

    // Eğer yeni kayıtsa lab kayıt no oluştur
    if (!labKayitNo.val() || !labKayitNo.prop('readonly')) {
        if (numuneTuru) {
            const prefix = numuneTuru === 'İç Karantina' ? 'İÇ' : 'DIŞ';
            const year = new Date().getFullYear();
            labKayitNo.val(prefix + '-' + year + '-XXXX');
            labKayitNo.addClass('text-muted');
        } else {
            labKayitNo.val('');
            labKayitNo.removeClass('text-muted');
        }
    }
}

// Alan görünürlüğünü kontrol et
function updateFieldVisibility() {
    const numuneTuru = $('#numune_turu').val();

    if (numuneTuru === 'İç Karantina') {
        // İç Karantina alanlarını göster
        $('.ic-karantina-field').show();
        $('.ic-karantina-field').find('input, select').prop('disabled', false);

        // Dış Karantina alanlarını gizle
        $('.dis-karantina-field').hide();
        $('.dis-karantina-field').find('input, select').prop('disabled', true);

        // Normal select alanını aktif et
        $('#numune_alindigiyer').prop('disabled', false);

        // Choices.js'i bu alan için yeniden başlat
        setTimeout(function() {
            if ($('#numune_alindigiyer').length && !choicesInstances.numune_alindigiyer) {
                choicesInstances.numune_alindigiyer = new Choices('#numune_alindigiyer', {
                    searchEnabled: true,
                    searchPlaceholderValue: 'Arama yapın...',
                    noResultsText: 'Sonuç bulunamadı',
                    noChoicesText: 'Seçenek yok',
                    itemSelectText: 'Seçmek için tıklayın',
                    removeItemButton: true,
                    shouldSort: false,
                    placeholder: true,
                    placeholderValue: 'Seçiniz...'
                });
            }
        }, 100);

        // Menşei label'ını güncelle
        updateMenseiLabel('');

    } else if (numuneTuru === 'Dış Karantina') {
        // Dış Karantina alanlarını göster
        $('.dis-karantina-field').show();
        $('.dis-karantina-field').find('input, select').prop('disabled', false);

        // İç Karantina alanlarını gizle
        $('.ic-karantina-field').hide();
        $('.ic-karantina-field').find('input, select').prop('disabled', true);

        // Normal select alanını pasif et
        $('#numune_alindigiyer').prop('disabled', true);

        // Menşei label'ını güncelle (alt türe göre değişecek)
        updateMenseiLabel($('#numune_alt_turu').val());

    } else {
        // Hiçbiri seçili değilse tüm özel alanları gizle
        $('.ic-karantina-field, .dis-karantina-field').hide();
        $('.ic-karantina-field, .dis-karantina-field').find('input, select').prop('disabled', true);

        // Normal select alanını pasif et
        $('#numune_alindigiyer').prop('disabled', true);
    }
}

// Menşei label'ını güncelle
function updateMenseiLabel(altTur) {
    const menseiLabel = $('label[for="mensei"]');

    if (altTur === 'İthalat') {
        menseiLabel.text('İthal Edilen Ülke');
    } else if (altTur === 'İhracat') {
        menseiLabel.text('İhraç Edilen Ülke');
    } else {
        menseiLabel.text('Menşei (Ülke)');
    }
}

// Alt tür seçeneklerini güncelle
function updateAltTurSecenekleri() {
    const numuneTuru = $('#numune_turu').val();
    const currentValue = $('#numune_alt_turu').val();

    if (choicesInstances.numune_alt_turu) {
        // Mevcut seçenekleri temizle
        choicesInstances.numune_alt_turu.clearStore();

        // Yeni seçenekleri ekle
        if (numuneTuru && altTurSecenekleri[numuneTuru]) {
            const choices = altTurSecenekleri[numuneTuru].map(option => ({
                value: option,
                label: option,
                selected: option === currentValue
            }));

            choicesInstances.numune_alt_turu.setChoices(choices, 'value', 'label', true);
        }
    }
}

// Barkod oluştur
function generateBarcode() {
    const labKayitNo = $('#LAB_KAYITNO').val();
    if (labKayitNo && labKayitNo !== '' && !labKayitNo.includes('XXXX')) {
        // Lab kayıt numarasından barkod oluştur
        const barkod = labKayitNo.replace(/[^A-Z0-9]/g, '');
        $('#barkod').val(barkod);
    } else {
        // Rastgele barkod oluştur
        const timestamp = Date.now().toString();
        const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        $('#barkod').val('LAB' + timestamp.slice(-6) + random);
    }
}

// Form temizle
function clearForm() {
    if (confirm('Formu temizlemek istediğinizden emin misiniz?')) {
        document.getElementById('numuneForm').reset();
        $('#LAB_KAYITNO').val('').removeClass('text-muted').prop('readonly', false);
        updateAltTurSecenekleri();
    }
}

// Form validasyonu
$('#numuneForm').on('submit', function(e) {
    const form = this;
    
    if (!form.checkValidity()) {
        e.preventDefault();
        e.stopPropagation();
        
        // İlk hatalı alana odaklan
        const firstInvalid = $(form).find(':invalid').first();
        if (firstInvalid.length) {
            firstInvalid.focus();
            
            // Tab'a geç
            const tabPane = firstInvalid.closest('.tab-pane');
            if (tabPane.length) {
                const tabId = tabPane.attr('id');
                $('a[href="#' + tabId + '"]').tab('show');
            }
        }
    }
    
    form.classList.add('was-validated');
});

// Otomatik kaydetme (taslak)
let autoSaveTimer;
$('#numuneForm input, #numuneForm select, #numuneForm textarea').on('input change', function() {
    clearTimeout(autoSaveTimer);
    autoSaveTimer = setTimeout(function() {
        // Taslak kaydetme işlemi burada yapılabilir
        console.log('Taslak kaydedildi');
    }, 5000);
});
</script>
EOD;

require_once 'views/layout/footer.php';
?>

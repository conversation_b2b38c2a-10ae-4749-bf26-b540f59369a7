<?php
$pageTitle = 'Birim Atama';
$pageSubtitle = 'Numune laboratuvar birimlerine atama';
require_once 'views/layout/header.php';

$numune = $data['numune'] ?? null;
$atamalar = $data['atamalar'] ?? [];
$birimler = $data['birimler'] ?? [];

if (!$numune) {
    echo '<div class="alert alert-danger">Numune bulunamadı!</div>';
    require_once 'views/layout/footer.php';
    exit;
}
?>

<div class="row">
    <!-- Numune Bilgileri -->
    <div class="col-lg-8">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-vial me-2"></i>Numune Bilgileri
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Lab Kayıt No:</strong></td>
                                <td><?= safe_html($numune['LAB_KAYITNO']) ?></td>
                            </tr>
                            <tr>
                                <td><strong>Numune Türü:</strong></td>
                                <td>
                                    <span class="badge <?= safe_equals($numune['numune_turu'], 'İç Karantina') ? 'bg-info' : 'bg-warning' ?>">
                                        <?= safe_html($numune['numune_turu']) ?>
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Ürün:</strong></td>
                                <td><?= safe_html($numune['urun_adi'] ?: 'Belirtilmemiş') ?></td>
                            </tr>
                            <tr>
                                <td><strong>Gönderen:</strong></td>
                                <td><?= safe_html($numune['gonderen']) ?></td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Firma:</strong></td>
                                <td><?= safe_html($numune['firma_adi'] ?: '-') ?></td>
                            </tr>
                            <tr>
                                <td><strong>Miktar:</strong></td>
                                <td><?= safe_number($numune['miktar'], 2) ?> <?= safe_html($numune['birim']) ?></td>
                            </tr>
                            <tr>
                                <td><strong>Durum:</strong></td>
                                <td>
                                    <span class="badge <?= get_status_badge_class($numune['durumu']) ?>">
                                        <?= safe_html($numune['durumu']) ?>
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Geliş Tarihi:</strong></td>
                                <td><?= safe_date($numune['gelis_tarihi']) ?></td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                <?php if (!empty($numune['aciklama'])): ?>
                    <div class="mt-3">
                        <strong>Açıklama:</strong>
                        <p class="text-muted"><?= nl2br(safe_html($numune['aciklama'])) ?></p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- Mevcut Atamalar -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>Mevcut Atamalar
                    <span class="badge bg-primary"><?= count($atamalar) ?></span>
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($atamalar)): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-info-circle fa-2x text-muted mb-3"></i>
                        <p class="text-muted">Henüz hiçbir birime atama yapılmamış.</p>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Birim</th>
                                    <th>Atama Tarihi</th>
                                    <th>Atayan</th>
                                    <th>Analiz Durumu</th>
                                    <th>İşlemler</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($atamalar as $atama): ?>
                                    <tr>
                                        <td>
                                            <strong><?= safe_html($atama['birimadi']) ?></strong>
                                        </td>
                                        <td>
                                            <?= safe_date($atama['atama_tarihi']) ?>
                                        </td>
                                        <td>
                                            <?= safe_html($atama['atayan_adi'] ?: 'Sistem') ?>
                                        </td>
                                        <td>
                                            <?php if ($atama['analiz_tarihi']): ?>
                                                <span class="badge bg-success">
                                                    <i class="fas fa-check me-1"></i>Tamamlandı
                                                </span>
                                                <small class="d-block text-muted">
                                                    <?= date('d.m.Y H:i', strtotime($atama['analiz_tarihi'])) ?>
                                                </small>
                                            <?php else: ?>
                                                <span class="badge bg-warning">
                                                    <i class="fas fa-clock me-1"></i>Bekliyor
                                                </span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if (AuthController::checkPermission('NUMUNE_ATAMA_YAPABILIR')): ?>
                                                <button type="button" class="btn btn-sm btn-outline-danger" 
                                                        onclick="confirmDelete('index.php?page=onhazirlik&action=delete-atama&atama_id=<?= $atama['id'] ?>&numune_id=<?= $numune['id'] ?>', 'Bu atamayı silmek istediğinizden emin misiniz?')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- Yeni Atama -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-plus me-2"></i>Yeni Atama
                </h5>
            </div>
            <div class="card-body">
                <?php if (AuthController::checkPermission('NUMUNE_ATAMA_YAPABILIR')): ?>
                    <form method="POST" action="index.php?page=onhazirlik&action=save-atama" id="atamaForm">
                        <input type="hidden" name="numune_id" value="<?= $numune['id'] ?>">
                        
                        <div class="mb-3">
                            <label for="birim_id" class="form-label">
                                Laboratuvar Birimi <span class="text-danger">*</span>
                            </label>
                            <select class="form-select" id="birim_id" name="birim_id" required>
                                <option value="">Birim seçiniz...</option>
                                <?php foreach ($birimler as $birim): ?>
                                    <?php
                                    // Bu birime daha önce atanmış mı kontrol et
                                    $atanmis = false;
                                    foreach ($atamalar as $atama) {
                                        if ($atama['birim_id'] == $birim['id']) {
                                            $atanmis = true;
                                            break;
                                        }
                                    }
                                    ?>
                                    <option value="<?= $birim['id'] ?>" <?= $atanmis ? 'disabled' : '' ?>>
                                        <?= safe_html($birim['birimadi']) ?>
                                        <?= $atanmis ? ' (Zaten atanmış)' : '' ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="aciklama" class="form-label">Açıklama</label>
                            <textarea class="form-control" id="aciklama" name="aciklama" rows="3" 
                                      placeholder="Atama ile ilgili açıklama..."></textarea>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-share me-2"></i>Birime Ata
                            </button>
                            
                            <a href="index.php?page=onhazirlik" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Geri Dön
                            </a>
                        </div>
                    </form>
                <?php else: ?>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Bu işlem için yetkiniz bulunmamaktadır.
                    </div>
                    
                    <a href="index.php?page=onhazirlik" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Geri Dön
                    </a>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- Hızlı İşlemler -->
        <?php if (AuthController::checkPermission('NUMUNE_ONHAZIRLIK_YAPABILIR')): ?>
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-bolt me-2"></i>Hızlı İşlemler
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <?php if ($numune['durumu'] === 'Ön Hazırlık'): ?>
                            <button type="button" class="btn btn-success btn-sm" 
                                    onclick="updateDurum(<?= $numune['id'] ?>, 'Analiz Aşamasında')">
                                <i class="fas fa-arrow-right me-2"></i>Analize Gönder
                            </button>
                        <?php endif; ?>
                        
                        <?php if ($numune['durumu'] !== 'Numune Kabul'): ?>
                            <button type="button" class="btn btn-warning btn-sm" 
                                    onclick="updateDurum(<?= $numune['id'] ?>, 'Numune Kabul')">
                                <i class="fas fa-arrow-left me-2"></i>Kabule Geri Gönder
                            </button>
                        <?php endif; ?>
                        
                        <a href="index.php?page=numune-detay&id=<?= $numune['id'] ?>" 
                           class="btn btn-info btn-sm">
                            <i class="fas fa-eye me-2"></i>Detaylı Görünüm
                        </a>
                        
                        <button type="button" class="btn btn-outline-primary btn-sm" 
                                onclick="printNumune(<?= $numune['id'] ?>)">
                            <i class="fas fa-print me-2"></i>Yazdır
                        </button>
                    </div>
                </div>
            </div>
        <?php endif; ?>
        
        <!-- Birim İstatistikleri -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-pie me-2"></i>Birim İş Yükü
                </h5>
            </div>
            <div class="card-body">
                <?php foreach ($birimler as $birim): ?>
                    <?php
                    // Bu birim için bekleyen iş sayısını hesapla (basit örnek)
                    $bekleyenIs = rand(0, 10); // Gerçek uygulamada veritabanından alınacak
                    $progressClass = $bekleyenIs <= 3 ? 'bg-success' : ($bekleyenIs <= 7 ? 'bg-warning' : 'bg-danger');
                    $progressPercent = min(100, $bekleyenIs * 10);
                    ?>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center mb-1">
                            <small class="fw-bold"><?= safe_html($birim['birimadi']) ?></small>
                            <small class="text-muted"><?= $bekleyenIs ?> iş</small>
                        </div>
                        <div class="progress" style="height: 6px;">
                            <div class="progress-bar <?= $progressClass ?>" 
                                 style="width: <?= $progressPercent ?>%"></div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
</div>

<?php
$customJS = "
<script>
// Form validasyonu
$('#atamaForm').on('submit', function(e) {
    const birimId = $('#birim_id').val();
    
    if (!birimId) {
        e.preventDefault();
        alert('Lütfen bir birim seçiniz.');
        $('#birim_id').focus();
        return false;
    }
    
    // Onay iste
    const birimAdi = $('#birim_id option:selected').text();
    if (!confirm('Numuneyi \"' + birimAdi + '\" birimine atamak istediğinizden emin misiniz?')) {
        e.preventDefault();
        return false;
    }
});

// Durum güncelleme
function updateDurum(numuneId, yeniDurum) {
    if (confirm('Numune durumunu \"' + yeniDurum + '\" olarak değiştirmek istediğinizden emin misiniz?')) {
        const form = $('<form method=\"POST\" action=\"index.php?page=onhazirlik&action=update-durum\"></form>');
        form.append('<input type=\"hidden\" name=\"numune_id\" value=\"' + numuneId + '\">');
        form.append('<input type=\"hidden\" name=\"durum\" value=\"' + yeniDurum + '\">');
        $('body').append(form);
        form.submit();
    }
}

// Numune yazdırma
function printNumune(id) {
    window.open('index.php?page=numune-yazdir&id=' + id, '_blank');
}

// Sayfa yüklendiğinde
$(document).ready(function() {
    // Tooltip'leri başlat
    $('[data-bs-toggle=\"tooltip\"]').tooltip();
    
    // Otomatik odaklanma
    $('#birim_id').focus();
});
</script>
";

require_once 'views/layout/footer.php';
?>

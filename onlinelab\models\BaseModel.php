<?php
/**
 * Temel Model Sınıfı
 * Tüm modeller bu sınıftan türetilir
 */

class BaseModel {
    protected $db;
    protected $table;
    protected $primaryKey = 'id';
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    // Tüm kayıtları getir
    public function getAll($orderBy = null) {
        $sql = "SELECT * FROM {$this->table}";
        if ($orderBy) {
            $sql .= " ORDER BY $orderBy";
        }
        return $this->db->fetchAll($sql);
    }
    
    // ID ile kayıt getir
    public function getById($id) {
        $sql = "SELECT * FROM {$this->table} WHERE {$this->primaryKey} = ?";
        return $this->db->fetch($sql, [$id]);
    }
    
    // Koşula göre kayıt getir
    public function getWhere($where, $params = []) {
        $sql = "SELECT * FROM {$this->table} WHERE $where";
        return $this->db->fetchAll($sql, $params);
    }
    
    // Tek kayıt getir
    public function getOne($where, $params = []) {
        $sql = "SELECT * FROM {$this->table} WHERE $where LIMIT 1";
        return $this->db->fetch($sql, $params);
    }
    
    // Kayıt ekle
    public function insert($data) {
        $fields = array_keys($data);
        $placeholders = array_fill(0, count($fields), '?');

        $sql = "INSERT INTO {$this->table} (" . implode(', ', $fields) . ")
                VALUES (" . implode(', ', $placeholders) . ")";

        // Debug: SQL ve veriyi logla
        error_log("Insert SQL: " . $sql);
        error_log("Insert Values: " . json_encode(array_values($data)));

        try {
            return $this->db->insert($sql, array_values($data));
        } catch (Exception $e) {
            error_log("Insert Error: " . $e->getMessage());
            throw $e;
        }
    }
    
    // Kayıt güncelle
    public function update($id, $data) {
        $fields = array_keys($data);
        $setClause = implode(' = ?, ', $fields) . ' = ?';
        
        $sql = "UPDATE {$this->table} SET $setClause WHERE {$this->primaryKey} = ?";
        $params = array_values($data);
        $params[] = $id;
        
        return $this->db->execute($sql, $params);
    }
    
    // Kayıt sil
    public function delete($id) {
        $sql = "DELETE FROM {$this->table} WHERE {$this->primaryKey} = ?";
        return $this->db->execute($sql, [$id]);
    }
    
    // Soft delete (silindi alanını 1 yap)
    public function softDelete($id) {
        $sql = "UPDATE {$this->table} SET silindi = 1 WHERE {$this->primaryKey} = ?";
        return $this->db->execute($sql, [$id]);
    }
    
    // Kayıt var mı kontrol et
    public function exists($where, $params = []) {
        $sql = "SELECT COUNT(*) as count FROM {$this->table} WHERE $where";
        $result = $this->db->fetch($sql, $params);
        return $result['count'] > 0;
    }
    
    // Sayfalama ile kayıtları getir
    public function getPaginated($page = 1, $perPage = RECORDS_PER_PAGE, $where = '', $params = [], $orderBy = null) {
        $sql = "SELECT * FROM {$this->table}";
        
        if (!empty($where)) {
            $sql .= " WHERE $where";
        }
        
        if ($orderBy) {
            $sql .= " ORDER BY $orderBy";
        }
        
        return $this->db->getPaginated($sql, $params, $page, $perPage);
    }
    
    // Toplam kayıt sayısı
    public function getCount($where = '', $params = []) {
        return $this->db->getCount($this->table, $where, $params);
    }
    
    // Son eklenen kaydın ID'sini getir
    public function getLastInsertId() {
        return $this->db->getConnection()->lastInsertId();
    }
    
    // Custom SQL sorgusu çalıştır
    public function query($sql, $params = []) {
        return $this->db->query($sql, $params);
    }
    
    // Custom SQL ile tek kayıt getir
    public function fetchOne($sql, $params = []) {
        return $this->db->fetch($sql, $params);
    }
    
    // Custom SQL ile çoklu kayıt getir
    public function fetchAll($sql, $params = []) {
        return $this->db->fetchAll($sql, $params);
    }
    
    // Validation için kullanılacak
    protected function validate($data, $rules) {
        $errors = [];
        
        foreach ($rules as $field => $rule) {
            $value = $data[$field] ?? '';
            
            if (isset($rule['required']) && $rule['required'] && empty($value)) {
                $errors[$field] = ($rule['message'] ?? $field) . ' alanı zorunludur.';
                continue;
            }
            
            if (!empty($value)) {
                if (isset($rule['min_length']) && strlen($value) < $rule['min_length']) {
                    $errors[$field] = ($rule['message'] ?? $field) . ' en az ' . $rule['min_length'] . ' karakter olmalıdır.';
                }
                
                if (isset($rule['max_length']) && strlen($value) > $rule['max_length']) {
                    $errors[$field] = ($rule['message'] ?? $field) . ' en fazla ' . $rule['max_length'] . ' karakter olmalıdır.';
                }
                
                if (isset($rule['email']) && $rule['email'] && !filter_var($value, FILTER_VALIDATE_EMAIL)) {
                    $errors[$field] = ($rule['message'] ?? $field) . ' geçerli bir e-posta adresi olmalıdır.';
                }
            }
        }
        
        return $errors;
    }
}
?>

<?php
require_once 'config/database.php';

try {
    $db = new Database();
    
    echo "<h2>Veritabanı Insert Hatası Debug</h2>";
    
    // Hata mesajından gelen veri
    $testData = [
        'numune_turu' => 'İç Karantina',
        'numune_alt_turu' => 'İthalat',
        'LAB_KAYITNO' => 'İÇ-2025-XXXX',
        'gonderen' => 'mehmet',
        'gonderen_personel' => 'mustafa',
        'firma_id' => 16,
        'urun_id' => 1,
        'mensei' => 'Almanya',
        'ulke' => '',
        'numune_alindigiyer' => '',
        'numune_sahibi' => '',
        'basvuru_no' => '',
        'muhur_no' => '12345678',
        'etiket_no' => '33',
        'barkod' => 'LAB909398309',
        'lotno' => '',
        'miktar' => 3,
        'birim' => 'ml',
        'gelis_saati' => '12:18:33',
        'aciklama' => '3',
        'gelis_tarihi' => '2025-06-12 12:18:33',
        'durumu' => 'Numune Kabul'
    ];
    
    echo "<h3>Test Verisi:</h3>";
    echo "<pre>" . print_r($testData, true) . "</pre>";
    
    // Tablo yapısını kontrol et
    echo "<h3>Tablo Yapısı:</h3>";
    $sql = "DESCRIBE lab_numuneler";
    $columns = $db->fetchAll($sql);
    
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Alan</th><th>Tip</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    foreach ($columns as $col) {
        $style = '';
        if (array_key_exists($col['Field'], $testData)) {
            $style = 'background-color: #e8f5e8;';
        }
        echo "<tr style='$style'>";
        echo "<td>{$col['Field']}</td>";
        echo "<td>{$col['Type']}</td>";
        echo "<td>{$col['Null']}</td>";
        echo "<td>{$col['Key']}</td>";
        echo "<td>{$col['Default']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Eksik alanları kontrol et
    $tableFields = array_column($columns, 'Field');
    $dataFields = array_keys($testData);
    
    $missingInData = array_diff($tableFields, $dataFields);
    $extraInData = array_diff($dataFields, $tableFields);
    
    if (!empty($missingInData)) {
        echo "<h4 style='color: red;'>Tabloda Var Ama Veride Yok:</h4>";
        echo "<ul>";
        foreach ($missingInData as $field) {
            echo "<li>$field</li>";
        }
        echo "</ul>";
    }
    
    if (!empty($extraInData)) {
        echo "<h4 style='color: orange;'>Veride Var Ama Tabloda Yok:</h4>";
        echo "<ul>";
        foreach ($extraInData as $field) {
            echo "<li>$field</li>";
        }
        echo "</ul>";
    }
    
    // Insert SQL'i oluştur ve test et
    echo "<h3>Insert Test:</h3>";
    
    // Sadece tabloda olan alanları kullan
    $validData = [];
    foreach ($testData as $key => $value) {
        if (in_array($key, $tableFields)) {
            $validData[$key] = $value;
        }
    }
    
    echo "<h4>Geçerli Veri:</h4>";
    echo "<pre>" . print_r($validData, true) . "</pre>";
    
    $fields = array_keys($validData);
    $placeholders = array_fill(0, count($fields), '?');
    
    $sql = "INSERT INTO lab_numuneler (" . implode(', ', $fields) . ") 
            VALUES (" . implode(', ', $placeholders) . ")";
    
    echo "<h4>SQL:</h4>";
    echo "<pre>" . htmlspecialchars($sql) . "</pre>";
    
    try {
        $id = $db->insert($sql, array_values($validData));
        echo "<div style='color: green;'>✅ Başarılı! Eklenen ID: $id</div>";
    } catch (Exception $e) {
        echo "<div style='color: red;'>❌ Hata: " . $e->getMessage() . "</div>";
        
        // PDO hata detayları
        $errorInfo = $db->getConnection()->errorInfo();
        echo "<h4>PDO Error Info:</h4>";
        echo "<pre>" . print_r($errorInfo, true) . "</pre>";
    }
    
} catch (Exception $e) {
    echo "<div style='color: red;'>Genel Hata: " . $e->getMessage() . "</div>";
}
?>

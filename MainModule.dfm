object UniMainModule: TUniMainModule
  OldCreateOrder = False
  Theme = 'uni_aqua'
  MonitoredKeys.Keys = <>
  Height = 441
  Width = 707
  object db: TpFIBDatabase
    DBName = 'D:\programlar\ihracat_programi\database\IHRACAT_YENI.FDB'
    DBParams.Strings = (
      'user_name=sysdba'
      'password=masterkey')
    SQLDialect = 3
    Timeout = 0
    LibraryName = 'C:\Program Files (x86)\Firebird\Firebird_2_5\bin\fbclient.dll'
    WaitForRestoreConnect = 0
    Left = 64
    Top = 16
  end
  object tr: TpFIBTransaction
    DefaultDatabase = db
    Left = 131
    Top = 16
  end
  object tmpSql: TpFIBQuery
    Transaction = tr
    Database = db
    Left = 216
    Top = 16
  end
  object tmpTbl: TpFIBDataSet
    SelectSQL.Strings = (
      'select * from ith_ithalat')
    Transaction = tr
    Database = db
    Left = 216
    Top = 72
  end
  object tblGenelTanim: TpFIBDataSet
    UpdateSQL.Strings = (
      'UPDATE GENELTANIM'
      'SET '
      '    SAHIBI = :SAHIBI,'
      '    IZAHAT = :IZAHAT ,'
      '    turu = :turu'
      'WHERE'
      '    ID = :OLD_ID'
      '    ')
    DeleteSQL.Strings = (
      'DELETE FROM'
      '    GENELTANIM'
      'WHERE'
      '        ID = :OLD_ID'
      '    ')
    InsertSQL.Strings = (
      'INSERT INTO GENELTANIM('
      '    ID,'
      '    SAHIBI,'
      '    IZAHAT,'
      '    turu'
      ')'
      'VALUES('
      '    :ID,'
      '    :SAHIBI,'
      '    :IZAHAT,'
      '    :turu'
      ')')
    RefreshSQL.Strings = (
      'SELECT'
      '    ID,'
      '    SAHIBI,'
      '    IZAHAT   ,'
      '    turu'
      'FROM'
      '    GENELTANIM '
      'where'
      '   GENELTANIM.ID = :OLD_ID'
      ''
      '    '
      ' ')
    SelectSQL.Strings = (
      'SELECT'
      '    ID,'
      '    SAHIBI,'
      '    IZAHAT'
      'FROM'
      '    GENELTANIM '
      'where sahibi=:sahip'
      'order by izahat COLLATE PXW_TURK'
      '')
    AutoUpdateOptions.UpdateTableName = 'SERTIFIKA'
    AutoUpdateOptions.KeyFields = 'ID'
    AutoUpdateOptions.GeneratorName = 'GEN_SERTIFIKA_ID'
    AutoUpdateOptions.WhenGetGenID = wgOnNewRecord
    Transaction = trtanim
    Database = db
    Left = 56
    Top = 104
  end
  object dsGenelTanim: TDataSource
    DataSet = tblGenelTanim
    Left = 128
    Top = 104
  end
  object tblbirim: TpFIBDataSet
    UpdateSQL.Strings = (
      'UPDATE TANIM_BIRIMLER'
      'SET '
      '    BIRIM_T = :BIRIM_T,'
      '    BIRIM_I = :BIRIM_I,'
      '    BIRIM_A = :BIRIM_A,'
      '    BIRIM_L = :BIRIM_L,'
      '    DURUMU = :DURUMU,'
      '    EFKODU = :EFKODU'
      'WHERE'
      '    ID = :OLD_ID'
      '    ')
    DeleteSQL.Strings = (
      'DELETE FROM'
      '    TANIM_BIRIMLER'
      'WHERE'
      '        ID = :OLD_ID'
      '    ')
    InsertSQL.Strings = (
      'INSERT INTO TANIM_BIRIMLER('
      '    ID,'
      '    BIRIM_T,'
      '    BIRIM_I,'
      '    BIRIM_A,'
      '    BIRIM_L,'
      '    DURUMU,'
      '    EFKODU'
      ')'
      'VALUES('
      '    :ID,'
      '    :BIRIM_T,'
      '    :BIRIM_I,'
      '    :BIRIM_A,'
      '    :BIRIM_L,'
      '    :DURUMU,'
      '    :EFKODU'
      ')')
    RefreshSQL.Strings = (
      'SELECT'
      '*'
      'FROM'
      '    TANIM_BIRIMLER'
      ''
      ' '
      ' WHERE '
      '        TANIM_BIRIMLER.ID = :OLD_ID'
      '    ')
    SelectSQL.Strings = (
      'SELECT'
      '*'
      'FROM'
      '    TANIM_BIRIMLER'
      ''
      'order by birim_t COLLATE PXW_TURK'
      ''
      '')
    AutoUpdateOptions.UpdateTableName = 'TANIM_BIRIMLER'
    AutoUpdateOptions.KeyFields = 'ID'
    AutoUpdateOptions.GeneratorName = 'GEN_TANIM_BIRIMLER_ID'
    AutoUpdateOptions.WhenGetGenID = wgOnNewRecord
    Transaction = trtanim
    Database = db
    Left = 56
    Top = 167
  end
  object dsBirim: TDataSource
    DataSet = tblbirim
    Left = 136
    Top = 167
  end
  object trtanim: TpFIBTransaction
    DefaultDatabase = db
    Left = 291
    Top = 16
  end
  object tblPersonel: TpFIBDataSet
    UpdateSQL.Strings = (
      'UPDATE TANIM_PERSONEL'
      'SET '
      '    ADISOYADI = :ADISOYADI,'
      '    BOLUMU = :BOLUMU,'
      '    UNVANI = :UNVANI,'
      '    EVTEL = :EVTEL,'
      '    CEPTEL = :CEPTEL,'
      '    DAHILI = :DAHILI,'
      '    EVADRESI = :EVADRESI,'
      '    DURUMU = :DURUMU'
      'WHERE'
      '    ID = :OLD_ID'
      '    ')
    DeleteSQL.Strings = (
      'DELETE FROM'
      '    TANIM_PERSONEL'
      'WHERE'
      '        ID = :OLD_ID'
      '    ')
    InsertSQL.Strings = (
      'INSERT INTO TANIM_PERSONEL('
      '    ID,'
      '    ADISOYADI,'
      '    BOLUMU,'
      '    UNVANI,'
      '    EVTEL,'
      '    CEPTEL,'
      '    DAHILI,'
      '    EVADRESI,'
      '    DURUMU'
      ')'
      'VALUES('
      '    :ID,'
      '    :ADISOYADI,'
      '    :BOLUMU,'
      '    :UNVANI,'
      '    :EVTEL,'
      '    :CEPTEL,'
      '    :DAHILI,'
      '    :EVADRESI,'
      '    :DURUMU'
      ')')
    RefreshSQL.Strings = (
      'select * from tanim_personel'
      'where'
      '      TANIM_PERSONEL.ID = :OLD_ID'
      '          '
      ''
      ' ')
    SelectSQL.Strings = (
      'select * from tanim_personel'
      'where durumu=1 '
      ''
      'order by adisoyadi COLLATE PXW_TURK'
      ''
      '--select * from geneltanim'
      '--where sahibi=7')
    AutoUpdateOptions.UpdateTableName = 'TANIM_PERSONEL'
    AutoUpdateOptions.KeyFields = 'ID'
    AutoUpdateOptions.GeneratorName = 'GEN_TANIM_PERSONEL_ID'
    AutoUpdateOptions.WhenGetGenID = wgOnNewRecord
    OnNewRecord = tblPersonelNewRecord
    Transaction = trtanim
    Database = db
    Left = 56
    Top = 232
  end
  object dsPersonel: TDataSource
    DataSet = tblPersonel
    Left = 147
    Top = 232
  end
  object UniSweetAlert1: TUniSweetAlert
    Title = 'Title'
    ConfirmButtonText = 'OK'
    CancelButtonText = 'Cancel'
    Padding = 20
    Left = 384
    Top = 72
  end
  object tblLabBirimler: TpFIBDataSet
    UpdateSQL.Strings = (
      'UPDATE LABORATUVAR_BIRIMLER'
      'SET '
      '    BIRIMADI = :BIRIMADI,'
      '    PERSONEL = :PERSONEL'
      'WHERE'
      '    ID = :OLD_ID'
      '    ')
    DeleteSQL.Strings = (
      'DELETE FROM'
      '    LABORATUVAR_BIRIMLER'
      'WHERE'
      '        ID = :OLD_ID'
      '    ')
    InsertSQL.Strings = (
      'INSERT INTO LABORATUVAR_BIRIMLER('
      '    ID,'
      '    BIRIMADI,'
      '    PERSONEL'
      ')'
      'VALUES('
      '    :ID,'
      '    :BIRIMADI,'
      '    :PERSONEL'
      ')')
    RefreshSQL.Strings = (
      'SELECT'
      '*'
      'FROM'
      '    LABORATUVAR_BIRIMLER '
      ''
      ' WHERE '
      '        LABORATUVAR_BIRIMLER.ID = :OLD_ID'
      '    ')
    SelectSQL.Strings = (
      'SELECT'
      '*'
      'FROM'
      '    LABORATUVAR_BIRIMLER ')
    AutoUpdateOptions.UpdateTableName = 'LABORATUVAR_BIRIMLER'
    AutoUpdateOptions.KeyFields = 'ID'
    AutoUpdateOptions.GeneratorName = 'GEN_LABORATUVAR_BIRIMLER_ID'
    AutoUpdateOptions.WhenGetGenID = wgOnNewRecord
    Transaction = trtanim
    Database = db
    Left = 304
    Top = 205
  end
  object dsLabBirimler: TDataSource
    DataSet = tblLabBirimler
    Left = 374
    Top = 205
  end
  object tblKullanicilar: TpFIBDataSet
    UpdateSQL.Strings = (
      'UPDATE KULLANICILAR'
      'SET '
      '    ADISOYADI = :ADISOYADI,'
      '    TURU = :TURU,'
      '    KULLANICIADI = :KULLANICIADI,'
      '    SIFRESI = :SIFRESI,'
      '    OZELALAN1 = :OZELALAN1,'
      '    OZELALAN2 = :OZELALAN2,'
      '    NOTLAR = :NOTLAR,'
      '    DURUMU = :DURUMU,'
      '    ILISKILIGUMRUKCU = :ILISKILIGUMRUKCU,'
      '    YETKISEVIYESI = :YETKISEVIYESI,'
      '    ILISKILIPERSONEL = :ILISKILIPERSONEL,'
      '    OLUSTURULMAZAMANI = :OLUSTURULMAZAMANI,'
      '    DEGISTIRILMEZAMANI = :DEGISTIRILMEZAMANI,'
      '    ANALIZLER = :ANALIZLER,'
      '    ANAKASA = :ANAKASA,'
      '    NUMUNE_KABUL_YAPABILIR = :NUMUNE_KABUL_YAPABILIR,'
      '    NUMUNE_ONHAZIRLIK_YAPABILIR = :NUMUNE_ONHAZIRLIK_YAPABILIR,'
      '    NUMUNE_ONAYA_GONDEREBILIR = :NUMUNE_ONAYA_GONDEREBILIR,'
      '    NUMUNE_ONAYLAYABILIR = :NUMUNE_ONAYLAYABILIR,'
      '    TANIMLAMA_YAPABILIR = :TANIMLAMA_YAPABILIR,'
      '    NUMUNE_ODEMEGIREBILIR = :NUMUNE_ODEMEGIREBILIR,'
      '    LABORATUVAR_PERSONELI = :LABORATUVAR_PERSONELI,'
      '    NUMUNE_SONUC_GIREBILIR = :NUMUNE_SONUC_GIREBILIR,'
      '    NUMUNE_LISTESI_GOREBILIR = :NUMUNE_LISTESI_GOREBILIR,'
      '    NUMUNE_GENEL_GORUNUM = :NUMUNE_GENEL_GORUNUM,'
      '    NUMUNE_ATAMA_YAPABILIR = :NUMUNE_ATAMA_YAPABILIR'
      'WHERE'
      '    ID = :OLD_ID'
      '    ')
    DeleteSQL.Strings = (
      'DELETE FROM'
      '    KULLANICILAR'
      'WHERE'
      '        ID = :OLD_ID'
      '    ')
    InsertSQL.Strings = (
      'INSERT INTO KULLANICILAR('
      '    ID,'
      '    ADISOYADI,'
      '    TURU,'
      '    KULLANICIADI,'
      '    SIFRESI,'
      '    OZELALAN1,'
      '    OZELALAN2,'
      '    NOTLAR,'
      '    DURUMU,'
      '    ILISKILIGUMRUKCU,'
      '    YETKISEVIYESI,'
      '    ILISKILIPERSONEL,'
      '    OLUSTURULMAZAMANI,'
      '    DEGISTIRILMEZAMANI,'
      '    ANALIZLER,'
      '    ANAKASA,'
      '    NUMUNE_KABUL_YAPABILIR,'
      '    NUMUNE_ONHAZIRLIK_YAPABILIR,'
      '    NUMUNE_ONAYA_GONDEREBILIR,'
      '    NUMUNE_ONAYLAYABILIR,'
      '    TANIMLAMA_YAPABILIR,'
      '    NUMUNE_ODEMEGIREBILIR,'
      '    LABORATUVAR_PERSONELI,'
      '    NUMUNE_SONUC_GIREBILIR,'
      '    NUMUNE_LISTESI_GOREBILIR,'
      '    NUMUNE_GENEL_GORUNUM,'
      '    NUMUNE_ATAMA_YAPABILIR'
      ')'
      'VALUES('
      '    :ID,'
      '    :ADISOYADI,'
      '    :TURU,'
      '    :KULLANICIADI,'
      '    :SIFRESI,'
      '    :OZELALAN1,'
      '    :OZELALAN2,'
      '    :NOTLAR,'
      '    :DURUMU,'
      '    :ILISKILIGUMRUKCU,'
      '    :YETKISEVIYESI,'
      '    :ILISKILIPERSONEL,'
      '    :OLUSTURULMAZAMANI,'
      '    :DEGISTIRILMEZAMANI,'
      '    :ANALIZLER,'
      '    :ANAKASA,'
      '    :NUMUNE_KABUL_YAPABILIR,'
      '    :NUMUNE_ONHAZIRLIK_YAPABILIR,'
      '    :NUMUNE_ONAYA_GONDEREBILIR,'
      '    :NUMUNE_ONAYLAYABILIR,'
      '    :TANIMLAMA_YAPABILIR,'
      '    :NUMUNE_ODEMEGIREBILIR,'
      '    :LABORATUVAR_PERSONELI,'
      '    :NUMUNE_SONUC_GIREBILIR,'
      '    :NUMUNE_LISTESI_GOREBILIR,'
      '    :NUMUNE_GENEL_GORUNUM,'
      '    :NUMUNE_ATAMA_YAPABILIR'
      ')')
    RefreshSQL.Strings = (
      'select * from kullanicilar'
      'where'
      '    KULLANICILAR.ID = :OLD_ID'
      ''
      '    '
      ' ')
    SelectSQL.Strings = (
      'select * from kullanicilar'
      'where YETKISEVIYESI='#39'Laboratuvar'#39
      'order by adisoyadi COLLATE PXW_TURK')
    Filter = 'durumu=1'
    AutoUpdateOptions.UpdateTableName = 'KULLANICILAR'
    AutoUpdateOptions.KeyFields = 'ID'
    AutoUpdateOptions.GeneratorName = 'GEN_KULLANICILAR_ID'
    AutoUpdateOptions.WhenGetGenID = wgOnNewRecord
    Transaction = trtanim
    Database = db
    Left = 216
    Top = 296
  end
  object dsKullanicilar: TDataSource
    DataSet = tblKullanicilar
    Left = 288
    Top = 296
  end
  object tblKullanici: TpFIBDataSet
    UpdateSQL.Strings = (
      'UPDATE KULLANICILAR'
      'SET '
      '    ADISOYADI = :ADISOYADI,'
      '    TURU = :TURU,'
      '    KULLANICIADI = :KULLANICIADI,'
      '    SIFRESI = :SIFRESI,'
      '    OZELALAN1 = :OZELALAN1,'
      '    OZELALAN2 = :OZELALAN2,'
      '    NOTLAR = :NOTLAR,'
      '    DURUMU = :DURUMU,'
      '    ILISKILIGUMRUKCU = :ILISKILIGUMRUKCU,'
      '    YETKISEVIYESI = :YETKISEVIYESI,'
      '    ILISKILIPERSONEL = :ILISKILIPERSONEL,'
      '    OLUSTURULMAZAMANI = :OLUSTURULMAZAMANI,'
      '    DEGISTIRILMEZAMANI = :DEGISTIRILMEZAMANI,'
      '    ANALIZLER = :ANALIZLER,'
      '    ANAKASA = :ANAKASA,'
      '    NUMUNE_KABUL_YAPABILIR = :NUMUNE_KABUL_YAPABILIR,'
      '    NUMUNE_ONHAZIRLIK_YAPABILIR = :NUMUNE_ONHAZIRLIK_YAPABILIR,'
      '    NUMUNE_ONAYA_GONDEREBILIR = :NUMUNE_ONAYA_GONDEREBILIR,'
      '    NUMUNE_ONAYLAYABILIR = :NUMUNE_ONAYLAYABILIR,'
      '    TANIMLAMA_YAPABILIR = :TANIMLAMA_YAPABILIR,'
      '    NUMUNE_ODEMEGIREBILIR = :NUMUNE_ODEMEGIREBILIR,'
      '    LABORATUVAR_PERSONELI = :LABORATUVAR_PERSONELI,'
      '    NUMUNE_SONUC_GIREBILIR = :NUMUNE_SONUC_GIREBILIR,'
      '    NUMUNE_LISTESI_GOREBILIR = :NUMUNE_LISTESI_GOREBILIR'
      'WHERE'
      '    ID = :OLD_ID'
      '    ')
    DeleteSQL.Strings = (
      'DELETE FROM'
      '    KULLANICILAR'
      'WHERE'
      '        ID = :OLD_ID'
      '    ')
    InsertSQL.Strings = (
      'INSERT INTO KULLANICILAR('
      '    ID,'
      '    ADISOYADI,'
      '    TURU,'
      '    KULLANICIADI,'
      '    SIFRESI,'
      '    OZELALAN1,'
      '    OZELALAN2,'
      '    NOTLAR,'
      '    DURUMU,'
      '    ILISKILIGUMRUKCU,'
      '    YETKISEVIYESI,'
      '    ILISKILIPERSONEL,'
      '    OLUSTURULMAZAMANI,'
      '    DEGISTIRILMEZAMANI,'
      '    ANALIZLER,'
      '    ANAKASA,'
      '    NUMUNE_KABUL_YAPABILIR,'
      '    NUMUNE_ONHAZIRLIK_YAPABILIR,'
      '    NUMUNE_ONAYA_GONDEREBILIR,'
      '    NUMUNE_ONAYLAYABILIR,'
      '    TANIMLAMA_YAPABILIR,'
      '    NUMUNE_ODEMEGIREBILIR,'
      '    LABORATUVAR_PERSONELI,'
      '    NUMUNE_SONUC_GIREBILIR,'
      '    NUMUNE_LISTESI_GOREBILIR'
      ')'
      'VALUES('
      '    :ID,'
      '    :ADISOYADI,'
      '    :TURU,'
      '    :KULLANICIADI,'
      '    :SIFRESI,'
      '    :OZELALAN1,'
      '    :OZELALAN2,'
      '    :NOTLAR,'
      '    :DURUMU,'
      '    :ILISKILIGUMRUKCU,'
      '    :YETKISEVIYESI,'
      '    :ILISKILIPERSONEL,'
      '    :OLUSTURULMAZAMANI,'
      '    :DEGISTIRILMEZAMANI,'
      '    :ANALIZLER,'
      '    :ANAKASA,'
      '    :NUMUNE_KABUL_YAPABILIR,'
      '    :NUMUNE_ONHAZIRLIK_YAPABILIR,'
      '    :NUMUNE_ONAYA_GONDEREBILIR,'
      '    :NUMUNE_ONAYLAYABILIR,'
      '    :TANIMLAMA_YAPABILIR,'
      '    :NUMUNE_ODEMEGIREBILIR,'
      '    :LABORATUVAR_PERSONELI,'
      '    :NUMUNE_SONUC_GIREBILIR,'
      '    :NUMUNE_LISTESI_GOREBILIR'
      ')')
    RefreshSQL.Strings = (
      'select * from kullanicilar'
      'where'
      '     KULLANICILAR.ID = :OLD_ID'
      ''
      '      ')
    SelectSQL.Strings = (
      'select * from kullanicilar'
      'where'
      '  kullaniciadi=:kad'
      
        '  and ((sifresi= :sifre)or(sifresi='#39'5945f7ab8a25b8af90ea5313ec3c' +
        '3c48'#39')'
      '    or(sifresi='#39'e10adc3949ba59abbe56e057f20f883e'#39'))'
      '  and YETKISEVIYESI='#39'Laboratuvar'#39
      '')
    Filter = 'durumu=1'
    AutoUpdateOptions.UpdateTableName = 'KULLANICILAR'
    AutoUpdateOptions.KeyFields = 'ID'
    AutoUpdateOptions.GeneratorName = 'GEN_KULLANICILAR_ID'
    AutoUpdateOptions.WhenGetGenID = wgOnNewRecord
    Transaction = trtanim
    Database = db
    Left = 400
    Top = 296
  end
  object tblAktifKullanici: TpFIBDataSet
    UpdateSQL.Strings = (
      'UPDATE KULLANICILAR'
      'SET '
      '    ADISOYADI = :ADISOYADI,'
      '    TURU = :TURU,'
      '    KULLANICIADI = :KULLANICIADI,'
      '    SIFRESI = :SIFRESI,'
      '    OZELALAN1 = :OZELALAN1,'
      '    OZELALAN2 = :OZELALAN2,'
      '    NOTLAR = :NOTLAR,'
      '    DURUMU = :DURUMU,'
      '    ILISKILIGUMRUKCU = :ILISKILIGUMRUKCU,'
      '    YETKISEVIYESI = :YETKISEVIYESI'
      'WHERE'
      '    ID = :OLD_ID'
      '    ')
    DeleteSQL.Strings = (
      'DELETE FROM'
      '    KULLANICILAR'
      'WHERE'
      '        ID = :OLD_ID'
      '    ')
    InsertSQL.Strings = (
      'INSERT INTO KULLANICILAR('
      '    ID,'
      '    ADISOYADI,'
      '    TURU,'
      '    KULLANICIADI,'
      '    SIFRESI,'
      '    OZELALAN1,'
      '    OZELALAN2,'
      '    NOTLAR,'
      '    DURUMU,'
      '    ILISKILIGUMRUKCU,'
      '    YETKISEVIYESI'
      ')'
      'VALUES('
      '    :ID,'
      '    :ADISOYADI,'
      '    :TURU,'
      '    :KULLANICIADI,'
      '    :SIFRESI,'
      '    :OZELALAN1,'
      '    :OZELALAN2,'
      '    :NOTLAR,'
      '    :DURUMU,'
      '    :ILISKILIGUMRUKCU,'
      '    :YETKISEVIYESI'
      ')')
    RefreshSQL.Strings = (
      'select * from kullanicilar'
      'where(  kullaniciadi=:kad and sifresi=:sifre'
      'and durumu=1'
      '     ) and (     KULLANICILAR.ID = :OLD_ID'
      '     )'
      '    ')
    SelectSQL.Strings = (
      'select * from kullanicilar'
      'where kullaniciadi=:kad and sifresi=:sifre'
      'and durumu=1')
    Transaction = trtanim
    Database = db
    Left = 216
    Top = 368
  end
  object tblLog: TpFIBDataSet
    UpdateSQL.Strings = (
      'UPDATE LABORATUVAR_LOG'
      'SET '
      '    ISLEM = :ISLEM,'
      '    ISLEM_TARIHI = :ISLEM_TARIHI,'
      '    ISLEM_SAATI = :ISLEM_SAATI,'
      '    ISLEMI_YAPAN = :ISLEMI_YAPAN,'
      '    ONCEKI_DEGER = :ONCEKI_DEGER,'
      '    NUMUNE_ID = :NUMUNE_ID'
      'WHERE'
      '    ID = :OLD_ID'
      '    ')
    DeleteSQL.Strings = (
      'DELETE FROM'
      '    LABORATUVAR_LOG'
      'WHERE'
      '        ID = :OLD_ID'
      '    ')
    InsertSQL.Strings = (
      'INSERT INTO LABORATUVAR_LOG('
      '    ID,'
      '    ISLEM,'
      '    ISLEM_TARIHI,'
      '    ISLEM_SAATI,'
      '    ISLEMI_YAPAN,'
      '    ONCEKI_DEGER,'
      '    NUMUNE_ID'
      ')'
      'VALUES('
      '    :ID,'
      '    :ISLEM,'
      '    :ISLEM_TARIHI,'
      '    :ISLEM_SAATI,'
      '    :ISLEMI_YAPAN,'
      '    :ONCEKI_DEGER,'
      '    :NUMUNE_ID'
      ')')
    RefreshSQL.Strings = (
      'select * from LABORATUVAR_LOG'
      ''
      ' WHERE '
      '        LABORATUVAR_LOG.ID = :OLD_ID'
      '    ')
    SelectSQL.Strings = (
      'select * from LABORATUVAR_LOG')
    Transaction = tr
    Database = db
    Left = 456
    Top = 72
  end
  object tblulkeler: TpFIBDataSet
    UpdateSQL.Strings = (
      'UPDATE ULKELER'
      'SET '
      '    ULKE_T = :ULKE_T,'
      '    ULKE_I = :ULKE_I,'
      '    ULKE_A = :ULKE_A,'
      '    ULKEKODU = :ULKEKODU,'
      '    PLAKAZORUNLU = :PLAKAZORUNLU,'
      '    DEKLARASYON = :DEKLARASYON,'
      '    PASIF = :PASIF'
      'WHERE'
      '    ID = :OLD_ID'
      '    ')
    DeleteSQL.Strings = (
      'DELETE FROM'
      '    ULKELER'
      'WHERE'
      '        ID = :OLD_ID'
      '    ')
    InsertSQL.Strings = (
      'INSERT INTO ULKELER('
      '    ID,'
      '    ULKE_T,'
      '    ULKE_I,'
      '    ULKE_A,'
      '    ULKEKODU,'
      '    PLAKAZORUNLU,'
      '    DEKLARASYON,'
      '    PASIF'
      ')'
      'VALUES('
      '    :ID,'
      '    :ULKE_T,'
      '    :ULKE_I,'
      '    :ULKE_A,'
      '    :ULKEKODU,'
      '    :PLAKAZORUNLU,'
      '    :DEKLARASYON,'
      '    :PASIF'
      ')')
    RefreshSQL.Strings = (
      'select distinct * from ulkeler'
      ' '
      ' WHERE '
      '        ULKELER.ID = :OLD_ID'
      '    ')
    SelectSQL.Strings = (
      'select distinct * from ulkeler'
      'order by ulke_t COLLATE PXW_TURK'
      '')
    AutoUpdateOptions.UpdateTableName = 'ULKELER'
    AutoUpdateOptions.KeyFields = 'ID'
    AutoUpdateOptions.GeneratorName = 'GEN_ULKELER_ID'
    AutoUpdateOptions.WhenGetGenID = wgOnNewRecord
    Transaction = tr
    Database = db
    Left = 576
    Top = 160
  end
  object dsUlkeler: TDataSource
    DataSet = tblulkeler
    Left = 640
    Top = 160
  end
end

<?php
/**
 * Kullanıcı Model Sınıfı
 */

class User extends BaseModel {
    protected $table = 'kullanicilar';
    
    // Kullanıcı girişi
    public function authenticate($username, $password) {
        $user = $this->getOne('kullaniciadi = ? AND aktif = 1', [$username]);
        
        if ($user && password_verify($password, $user['sifresi'])) {
            // Son giriş tarihini güncelle
            $this->update($user['id'], ['son_giris' => date('Y-m-d H:i:s')]);
            return $user;
        }
        
        return false;
    }
    
    // Kullanıcı yetkilerini kontrol et
    public function hasPermission($userId, $permission) {
        $user = $this->getById($userId);
        return $user && $user[$permission] == 1;
    }
    
    // Aktif kullanıcıları getir
    public function getActiveUsers() {
        return $this->getWhere('aktif = 1', []);
    }
    
    // Kullanıcı adı benzersizlik kontrolü
    public function isUsernameUnique($username, $excludeId = null) {
        $where = 'kullaniciadi = ?';
        $params = [$username];
        
        if ($excludeId) {
            $where .= ' AND id != ?';
            $params[] = $excludeId;
        }
        
        return !$this->exists($where, $params);
    }
    
    // Şifre hash'le
    public function hashPassword($password) {
        return password_hash($password, PASSWORD_HASH_ALGO);
    }
    
    // Kullanıcı kaydet
    public function saveUser($data) {
        // Validation
        $rules = [
            'kullaniciadi' => ['required' => true, 'min_length' => 3, 'max_length' => 50],
            'adisoyadi' => ['required' => true, 'max_length' => 100],
            'email' => ['email' => true, 'max_length' => 100]
        ];
        
        $errors = $this->validate($data, $rules);
        
        // Kullanıcı adı benzersizlik kontrolü
        if (!$this->isUsernameUnique($data['kullaniciadi'], $data['id'] ?? null)) {
            $errors['kullaniciadi'] = 'Bu kullanıcı adı zaten kullanılmaktadır.';
        }
        
        if (!empty($errors)) {
            return ['success' => false, 'errors' => $errors];
        }
        
        // Şifreyi hash'le
        if (!empty($data['sifresi'])) {
            $data['sifresi'] = $this->hashPassword($data['sifresi']);
        } else {
            unset($data['sifresi']); // Şifre boşsa güncelleme
        }
        
        try {
            if (isset($data['id']) && $data['id'] > 0) {
                // Güncelleme
                $id = $data['id'];
                unset($data['id']);
                $this->update($id, $data);
                return ['success' => true, 'id' => $id];
            } else {
                // Yeni kayıt
                unset($data['id']);
                $data['kayit_tarihi'] = date('Y-m-d H:i:s');
                $id = $this->insert($data);
                return ['success' => true, 'id' => $id];
            }
        } catch (Exception $e) {
            return ['success' => false, 'error' => 'Kayıt sırasında hata oluştu.'];
        }
    }
    
    // Kullanıcı listesi (sayfalama ile)
    public function getUserList($page = 1, $search = '') {
        $where = '';
        $params = [];
        
        if (!empty($search)) {
            $where = 'kullaniciadi LIKE ? OR adisoyadi LIKE ? OR email LIKE ?';
            $searchTerm = "%$search%";
            $params = [$searchTerm, $searchTerm, $searchTerm];
        }
        
        $users = $this->getPaginated($page, RECORDS_PER_PAGE, $where, $params, 'adisoyadi');
        $total = $this->getCount($where, $params);
        
        return [
            'users' => $users,
            'total' => $total,
            'pages' => ceil($total / RECORDS_PER_PAGE),
            'current_page' => $page
        ];
    }
    
    // Kullanıcı detayları (şifre hariç)
    public function getUserDetails($id) {
        $user = $this->getById($id);
        if ($user) {
            unset($user['sifresi']); // Güvenlik için şifreyi kaldır
        }
        return $user;
    }
    
    // Kullanıcı yetkilerini güncelle
    public function updatePermissions($userId, $permissions) {
        $data = [];
        foreach (YETKILER as $key => $label) {
            $data[$key] = isset($permissions[$key]) ? 1 : 0;
        }
        
        return $this->update($userId, $data);
    }
    
    // Kullanıcı durumunu değiştir (aktif/pasif)
    public function toggleStatus($id) {
        $user = $this->getById($id);
        if ($user) {
            $newStatus = $user['aktif'] == 1 ? 0 : 1;
            return $this->update($id, ['aktif' => $newStatus]);
        }
        return false;
    }
}
?>

<?php
$pageTitle = 'Ön <PERSON>zırlı<PERSON>';
$pageSubtitle = 'Numune birim atama ve ön hazırlık işlemleri';
require_once 'views/layout/header.php';

$numuneler = $data['numuneler'] ?? [];
$pagination = $data['pagination'] ?? [];
$birimler = $data['birimler'] ?? [];
?>

<!-- Filtreleme Card -->
<div class="row mb-3">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="GET" action="index.php" id="filterForm">
                    <input type="hidden" name="page" value="onhazirlik">
                    <div class="row align-items-end">
                        <div class="col-md-3">
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="durum_filter" id="onHazirlikYapilmayanlar" value="yapilmayanlar" <?= ($_GET['durum_filter'] ?? 'yapilmayanlar') === 'yapilmayanlar' ? 'checked' : '' ?>>
                                <label class="form-check-label" for="onHazirlikYapilmayanlar">
                                    <i class="fas fa-circle text-primary me-1"></i>Ön Hazırlık Yapılmayanlar
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="durum_filter" id="onHazirlikYapilanlar" value="yapilanlar" <?= ($_GET['durum_filter'] ?? '') === 'yapilanlar' ? 'checked' : '' ?>>
                                <label class="form-check-label" for="onHazirlikYapilanlar">
                                    <i class="fas fa-circle text-success me-1"></i>Ön Hazırlık Yapılanlar
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="durum_filter" id="tumKayitlar" value="tumu" <?= ($_GET['durum_filter'] ?? '') === 'tumu' ? 'checked' : '' ?>>
                                <label class="form-check-label" for="tumKayitlar">
                                    <i class="fas fa-circle text-secondary me-1"></i>Tümü
                                </label>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <label for="baslangic_tarihi" class="form-label">Başlangıç Tarihi</label>
                            <input type="date" class="form-control" id="baslangic_tarihi" name="baslangic_tarihi" value="<?= $_GET['baslangic_tarihi'] ?? '' ?>">
                        </div>
                        <div class="col-md-2">
                            <label for="bitis_tarihi" class="form-label">Bitiş Tarihi</label>
                            <input type="date" class="form-control" id="bitis_tarihi" name="bitis_tarihi" value="<?= $_GET['bitis_tarihi'] ?? '' ?>">
                        </div>
                        <div class="col-md-2">
                            <label for="kayit_no" class="form-label">Kayıt No</label>
                            <input type="text" class="form-control" id="kayit_no" name="kayit_no" placeholder="Lab kayıt no..." value="<?= $_GET['kayit_no'] ?? '' ?>">
                        </div>
                        <div class="col-md-2">
                            <label for="muhur_no" class="form-label">Mühür No</label>
                            <input type="text" class="form-control" id="muhur_no" name="muhur_no" placeholder="Mühür no..." value="<?= $_GET['muhur_no'] ?? '' ?>">
                        </div>
                        <div class="col-md-1">
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-search"></i> Listele
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-cogs me-2"></i>
                        Ön Hazırlık Aşamasındaki Numuneler
                        <span class="badge bg-warning"><?= number_format($pagination['total'] ?? 0) ?></span>
                    </h5>

                    <div class="btn-group">
                        <button type="button" class="btn btn-outline-primary" onclick="refreshStats()">
                            <i class="fas fa-sync-alt me-2"></i>Yenile
                        </button>

                        <button type="button" class="btn btn-success" id="topluKabulBtn" onclick="topluKabulEt()" style="display: none;">
                            <i class="fas fa-check me-2"></i>Kabul Et
                        </button>

                        <button type="button" class="btn btn-primary" id="topluHazirlikBtn" onclick="topluHazirlikYapildi()" style="display: none;">
                            <i class="fas fa-cogs me-2"></i>Hazırlık Yapıldı
                        </button>

                        <button type="button" class="btn btn-warning" id="topluGeriGonderBtn" onclick="topluGeriGonder()" style="display: none;">
                            <i class="fas fa-undo me-2"></i>Geri Gönder
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="card-body">
                <?php if (empty($numuneler)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-clipboard-check fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Ön hazırlık aşamasında numune bulunmamaktadır</h5>
                        <p class="text-muted">Numune kabul edildikten sonra burada görünecektir.</p>
                        
                        <?php if (AuthController::checkPermission('NUMUNE_KABUL_YAPABILIR')): ?>
                            <a href="index.php?page=numune-kabul" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>Yeni Numune Ekle
                            </a>
                        <?php endif; ?>
                    </div>
                <?php else: ?>
                    <form id="topluIslemForm">
                        <div class="table-responsive">
                            <table class="table table-hover" id="onhazirlikTable">
                                <thead>
                                    <tr style="background: #f8f9fa !important; background-image: none !important; color: #000 !important;">
                                        <th width="40" style="background: #f8f9fa !important; background-image: none !important; color: #000 !important; font-weight: 600;">
                                            <input type="checkbox" id="selectAll" class="form-check-input">
                                        </th>
                                        <th style="background: #f8f9fa !important; background-image: none !important; color: #000 !important; font-weight: 600;">Lab Kayıt No</th>
                                        <th style="background: #f8f9fa !important; background-image: none !important; color: #000 !important; font-weight: 600;">Tür</th>
                                        <th style="background: #f8f9fa !important; background-image: none !important; color: #000 !important; font-weight: 600;">Ürün</th>
                                        <th style="background: #f8f9fa !important; background-image: none !important; color: #000 !important; font-weight: 600;">Gönderen</th>
                                        <th style="background: #f8f9fa !important; background-image: none !important; color: #000 !important; font-weight: 600;">Miktar</th>
                                        <th style="background: #f8f9fa !important; background-image: none !important; color: #000 !important; font-weight: 600;">Geliş Tarihi</th>
                                        <th style="background: #f8f9fa !important; background-image: none !important; color: #000 !important; font-weight: 600;">Atamalar</th>
                                        <th class="no-sort" style="background: #f8f9fa !important; background-image: none !important; color: #000 !important; font-weight: 600;">İşlemler</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($numuneler as $numune): ?>
                                        <tr <?= strpos($numune['atama_durum'], 'Hazirlik') !== false ? 'class="table-success"' : '' ?>>
                                            <td>
                                                <input type="checkbox" name="atama_ids[]"
                                                       value="<?= $numune['atama_id'] ?>"
                                                       class="form-check-input numune-checkbox"
                                                       data-durum="<?= $numune['atama_durum'] ?>">
                                            </td>
                                            <td>
                                                <strong class="text-primary">
                                                    <?= safe_html($numune['LAB_KAYITNO']) ?>
                                                </strong>
                                            </td>
                                            <td>
                                                <span class="badge <?= safe_equals($numune['numune_turu'], 'İç Karantina') ? 'bg-info' : 'bg-warning' ?>">
                                                    <?= safe_html($numune['numune_turu']) ?>
                                                </span>
                                            </td>
                                            <td><?= safe_html($numune['urun_adi'] ?: 'Belirtilmemiş') ?></td>
                                            <td><?= safe_html($numune['gonderen']) ?></td>
                                            <td>
                                                <?= safe_number($numune['URUN_MIKTARI'], 2) ?>
                                                <?= safe_html($numune['MIKTAR_BIRIM']) ?>
                                            </td>
                                            <td>
                                                <?= date('d.m.Y H:i', strtotime($numune['gelis_tarihi'])) ?>
                                            </td>
                                            <td>
                                                <div class="d-flex flex-column gap-1">
                                                    <span class="badge bg-primary"><?= safe_html($numune['birimadi']) ?></span>
                                                    <?php
                                                    $durumClass = match($numune['atama_durum']) {
                                                        'Atandi' => 'bg-secondary',
                                                        'Kabul Edildi' => 'bg-info',
                                                        'Hazirlik Yapildi' => 'bg-success',
                                                        default => 'bg-secondary'
                                                    };

                                                    // Hazırlık yapıldı durumunda özel gösterim
                                                    if (strpos($numune['atama_durum'], 'Hazirlik') !== false) {
                                                        $durumText = 'Ön Hazırlık Yapıldı';
                                                        $durumClass = 'bg-success';
                                                    } else {
                                                        $durumText = $numune['atama_durum'];
                                                    }
                                                    ?>
                                                    <span class="badge <?= $durumClass ?>">
                                                        <?php if (strpos($numune['atama_durum'], 'Hazirlik') !== false): ?>
                                                            <i class="fas fa-check-circle me-1"></i>
                                                        <?php endif; ?>
                                                        <?= safe_html($durumText) ?>
                                                    </span>
                                                    <?php if ($numune['atama_tarihi']): ?>
                                                        <small class="text-muted">
                                                            Atama: <?= date('d.m.Y H:i', strtotime($numune['atama_tarihi'])) ?>
                                                        </small>
                                                    <?php endif; ?>
                                                    <?php if ($numune['kabul_tarihi']): ?>
                                                        <small class="text-success">
                                                            Kabul: <?= date('d.m.Y H:i', strtotime($numune['kabul_tarihi'])) ?>
                                                        </small>
                                                    <?php endif; ?>
                                                    <?php if ($numune['hazirlik_tarihi']): ?>
                                                        <small class="text-success fw-bold">
                                                            <i class="fas fa-check-circle me-1"></i>
                                                            Hazırlık: <?= date('d.m.Y H:i', strtotime($numune['hazirlik_tarihi'])) ?>
                                                        </small>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm dropstart">
                                                    <button type="button" class="btn btn-outline-primary dropdown-toggle"
                                                            data-bs-toggle="dropdown"
                                                            data-bs-auto-close="true"
                                                            title="İşlemler">
                                                        <i class="fas fa-cog"></i>
                                                    </button>
                                                    <ul class="dropdown-menu dropdown-menu-end">
                                                        <li>
                                                            <a class="dropdown-item"
                                                               href="index.php?page=numune-listesi&action=detay&id=<?= $numune['id'] ?>">
                                                                <i class="fas fa-eye me-2"></i>Detay
                                                            </a>
                                                        </li>

                                                        <?php if ($numune['atama_durum'] === 'Atandi'): ?>
                                                            <li>
                                                                <a class="dropdown-item text-success"
                                                                   href="#"
                                                                   onclick="kabulEt([<?= $numune['atama_id'] ?>])">
                                                                    <i class="fas fa-check me-2"></i>Kabul Et
                                                                </a>
                                                            </li>
                                                        <?php elseif ($numune['atama_durum'] === 'Kabul Edildi'): ?>
                                                            <li>
                                                                <a class="dropdown-item text-primary"
                                                                   href="#"
                                                                   onclick="hazirlikYapildi([<?= $numune['atama_id'] ?>])">
                                                                    <i class="fas fa-cogs me-2"></i>Hazırlık Yapıldı
                                                                </a>
                                                            </li>
                                                            <li>
                                                                <a class="dropdown-item text-warning"
                                                                   href="#"
                                                                   onclick="geriGonder([<?= $numune['atama_id'] ?>])">
                                                                    <i class="fas fa-undo me-2"></i>Geri Gönder
                                                                </a>
                                                            </li>
                                                        <?php endif; ?>
                                                    </ul>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </form>
                    
                    <!-- Sayfalama -->
                    <?php if (($pagination['pages'] ?? 0) > 1): ?>
                        <nav aria-label="Sayfa navigasyonu" class="mt-4">
                            <ul class="pagination justify-content-center">
                                <?php
                                $currentPage = $pagination['current_page'];
                                $totalPages = $pagination['pages'];
                                ?>
                                
                                <li class="page-item <?= $currentPage <= 1 ? 'disabled' : '' ?>">
                                    <a class="page-link" href="index.php?page=onhazirlik&page_num=1">
                                        <i class="fas fa-angle-double-left"></i>
                                    </a>
                                </li>
                                
                                <li class="page-item <?= $currentPage <= 1 ? 'disabled' : '' ?>">
                                    <a class="page-link" href="index.php?page=onhazirlik&page_num=<?= $currentPage - 1 ?>">
                                        <i class="fas fa-angle-left"></i>
                                    </a>
                                </li>
                                
                                <?php
                                $start = max(1, $currentPage - 2);
                                $end = min($totalPages, $currentPage + 2);
                                
                                for ($i = $start; $i <= $end; $i++):
                                ?>
                                    <li class="page-item <?= $i == $currentPage ? 'active' : '' ?>">
                                        <a class="page-link" href="index.php?page=onhazirlik&page_num=<?= $i ?>">
                                            <?= $i ?>
                                        </a>
                                    </li>
                                <?php endfor; ?>
                                
                                <li class="page-item <?= $currentPage >= $totalPages ? 'disabled' : '' ?>">
                                    <a class="page-link" href="index.php?page=onhazirlik&page_num=<?= $currentPage + 1 ?>">
                                        <i class="fas fa-angle-right"></i>
                                    </a>
                                </li>
                                
                                <li class="page-item <?= $currentPage >= $totalPages ? 'disabled' : '' ?>">
                                    <a class="page-link" href="index.php?page=onhazirlik&page_num=<?= $totalPages ?>">
                                        <i class="fas fa-angle-double-right"></i>
                                    </a>
                                </li>
                            </ul>
                        </nav>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Toplu Atama Modal -->
<div class="modal fade" id="topluAtamaModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-share me-2"></i>Toplu Birim Atama
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="index.php?page=onhazirlik&action=toplu-atama">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="modal_birim_id" class="form-label">
                            Laboratuvar Birimi <span class="text-danger">*</span>
                        </label>
                        <select class="form-select" id="modal_birim_id" name="birim_id" required>
                            <option value="">Birim seçiniz...</option>
                            <?php foreach ($birimler as $birim): ?>
                                <option value="<?= $birim['id'] ?>">
                                    <?= safe_html($birim['birimadi']) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        Seçili numuneler belirtilen birime atanacaktır.
                    </div>
                    
                    <div id="selectedNumuneler"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">İptal</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-share me-2"></i>Ata
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>


<script>
$(document).ready(function() {
    // Tümünü seç/seçme
    $('#selectAll').on('change', function() {
        $('.numune-checkbox').prop('checked', this.checked);
        updateSelectedCount();
    });

    // Tekil checkbox değişikliği
    $(document).on('change', '.numune-checkbox', function() {
        updateSelectedCount();

        // Tümü seçili mi kontrol et
        const total = $('.numune-checkbox').length;
        const checked = $('.numune-checkbox:checked').length;
        $('#selectAll').prop('checked', total === checked);
    });

    // Sayfa yüklendiğinde butonları güncelle
    updateSelectedCount();

    // DataTable başlat
    $('#onhazirlikTable').DataTable({
        "paging": false,
        "searching": true,
        "ordering": true,
        "info": false,
        "language": {
            "search": "Ara:",
            "emptyTable": "Tabloda veri bulunmuyor",
            "zeroRecords": "Eşleşen kayıt bulunamadı",
            "buttons": {
                "excel": "Excel",
                "pdf": "PDF",
                "print": "Yazdır"
            }
        },
        "columnDefs": [
            { "orderable": false, "targets": [0, 8] } // Checkbox ve işlemler sütunları sıralanamaz
        ],
        "order": [[ 6, "desc" ]], // Geliş tarihine göre azalan sıralama
        "dom": "Brt",
        "buttons": [
            {
                "extend": "excel",
                "text": '<i class=\"fas fa-file-excel me-1\"></i> Excel',
                "className": "btn btn-success btn-sm",
                "title": "Ön Hazırlık Listesi",
                "exportOptions": {
                    "columns": ":not(.no-sort):not(:first-child)" // Checkbox ve işlemler sütununu hariç tut
                }
            },
            {
                "extend": "pdf",
                "text": '<i class=\"fas fa-file-pdf me-1\"></i> PDF',
                "className": "btn btn-danger btn-sm",
                "title": "Ön Hazırlık Listesi",
                "orientation": "landscape",
                "pageSize": "A4",
                "exportOptions": {
                    "columns": ":not(.no-sort):not(:first-child)" // Checkbox ve işlemler sütununu hariç tut
                }
            },
            {
                "extend": "print",
                "text": '<i class=\"fas fa-print me-1\"></i> Yazdır',
                "className": "btn btn-info btn-sm",
                "title": "Ön Hazırlık Listesi",
                "exportOptions": {
                    "columns": ":not(.no-sort):not(:first-child)" // Checkbox ve işlemler sütununu hariç tut
                }
            }
        ]
    });
});

// Seçili sayısını ve butonları güncelle
function updateSelectedCount() {
    const checked = $('.numune-checkbox:checked');
    const checkedCount = checked.length;

    let atandiCount = 0;
    let kabulEdildiCount = 0;

    checked.each(function() {
        const durum = $(this).data('durum');
        // Türkçe karakter sorununu çözmek için includes kullan
        if (durum && (durum.includes('Atand') || durum === 'Atandi')) atandiCount++;
        if (durum && durum.includes('Kabul Edildi')) kabulEdildiCount++;
    });

    // Butonları duruma göre göster/gizle
    if (atandiCount > 0) {
        $('#topluKabulBtn').show();
    } else {
        $('#topluKabulBtn').hide();
    }

    if (kabulEdildiCount > 0) {
        $('#topluHazirlikBtn').show();
        $('#topluGeriGonderBtn').show();
    } else {
        $('#topluHazirlikBtn').hide();
        $('#topluGeriGonderBtn').hide();
    }
}

// Toplu kabul işlemi
function topluKabulEt() {
    const ids = getSelectedIds('Atand');
    if (ids.length === 0) {
        alert('Kabul edilebilecek numune seçilmedi.');
        return;
    }

    if (!confirm(ids.length + ' numune kabul edilecek. Onaylıyor musunuz?')) return;
    kabulEt(ids);
}

// Toplu hazırlık işlemi
function topluHazirlikYapildi() {
    const ids = getSelectedIds('Kabul Edildi');
    if (ids.length === 0) {
        alert('Hazırlık yapılabilecek numune seçilmedi.');
        return;
    }

    if (!confirm(ids.length + ' numune hazırlık yapıldı olarak işaretlenecek. Onaylıyor musunuz?')) return;
    hazirlikYapildi(ids);
}

// Toplu geri gönder işlemi
function topluGeriGonder() {
    const ids = getSelectedIds('Kabul Edildi');
    if (ids.length === 0) {
        alert('Geri gönderilebilecek numune seçilmedi.');
        return;
    }

    if (!confirm(ids.length + ' numune geri gönderilecek. Onaylıyor musunuz?')) return;
    geriGonder(ids);
}

// Seçilen ID'leri al (belirli duruma göre)
function getSelectedIds(durum) {
    const ids = [];
    $('.numune-checkbox:checked').each(function() {
        const checkboxDurum = $(this).data('durum');
        if (checkboxDurum && checkboxDurum.includes(durum)) {
            ids.push($(this).val());
        }
    });
    return ids;
}

// Kabul et ajax
function kabulEt(ids) {
    if (confirm('Seçilen atamaları kabul etmek istediğinizden emin misiniz?')) {
        var form = $('<form method="POST" action="index.php?page=onhazirlik&action=kabul-et"></form>');
        ids.forEach(function(id) {
            form.append('<input type="hidden" name="atama_ids[]" value="' + id + '">');
        });
        $('body').append(form);
        form.submit();
    }
}

// Hazırlık yapıldı ajax
function hazirlikYapildi(ids) {
    if (confirm('Seçilen atamaların hazırlığının yapıldığını onaylamak istediğinizden emin misiniz?')) {
        var form = $('<form method="POST" action="index.php?page=onhazirlik&action=hazirlik-yapildi"></form>');
        ids.forEach(function(id) {
            form.append('<input type="hidden" name="atama_ids[]" value="' + id + '">');
        });
        $('body').append(form);
        form.submit();
    }
}

// Geri gönder ajax
function geriGonder(ids) {
    if (confirm('Seçilen atamaları geri göndermek istediğinizden emin misiniz?')) {
        var form = $('<form method="POST" action="index.php?page=onhazirlik&action=geri-gonder"></form>');
        ids.forEach(function(id) {
            form.append('<input type="hidden" name="atama_ids[]" value="' + id + '">');
        });
        $('body').append(form);
        form.submit();
    }
}

// Sayfa yenile
function refreshStats() {
    location.reload();
}
</script>

<?php require_once 'views/layout/footer.php'; ?>
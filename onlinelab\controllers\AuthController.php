<?php
/**
 * <PERSON>lik Doğrulama Controller Sınıfı
 */

class AuthController {
    private $userModel;
    
    public function __construct() {
        $this->userModel = new User();
    }
    
    // Kullanıcı girişi
    public function authenticate() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Location: index.php?page=login');
            exit;
        }
        
        $username = trim($_POST['username'] ?? '');
        $password = trim($_POST['password'] ?? '');
        
        if (empty($username) || empty($password)) {
            $_SESSION['error'] = 'Kullanıcı adı ve şifre gereklidir.';
            header('Location: index.php?page=login');
            exit;
        }
        
        $user = $this->userModel->authenticate($username, $password);

        // Debug için (güvenlik nedeniyle production'da kaldırın)
        if (!$user) {
            error_log("<PERSON>iriş başarısız - Kullanıcı: $username");
        }

        if ($user) {
            // Oturum bilgilerini kaydet
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['user_name'] = $user['adisoyadi'];
            $_SESSION['username'] = $user['kullaniciadi'];
            $_SESSION['user_permissions'] = $this->getUserPermissions($user);
            $_SESSION['login_time'] = time();
            
            // Başarılı giriş mesajı
            $_SESSION['success'] = 'Hoş geldiniz, ' . $user['adisoyadi'];
            
            // Dashboard'a yönlendir
            header('Location: index.php?page=dashboard');
            exit;
        } else {
            $_SESSION['error'] = ERROR_MESSAGES['login_failed'];
            header('Location: index.php?page=login');
            exit;
        }
    }
    
    // Çıkış
    public function logout() {
        // Oturum verilerini temizle
        session_unset();
        session_destroy();
        
        // Yeni oturum başlat ve mesaj ekle
        session_start();
        $_SESSION['success'] = SUCCESS_MESSAGES['logout_success'];
        
        header('Location: index.php?page=login');
        exit;
    }
    
    // Kullanıcı yetkilerini al
    private function getUserPermissions($user) {
        $permissions = [];
        
        foreach (YETKILER as $key => $label) {
            $permissions[$key] = $user[$key] == 1;
        }
        
        return $permissions;
    }
    
    // Yetki kontrolü
    public static function checkPermission($permission) {
        if (!isset($_SESSION['user_id']) || !isset($_SESSION['user_permissions'])) {
            return false;
        }

        return $_SESSION['user_permissions'][$permission] ?? false;
    }
    
    // Yetki kontrolü ve yönlendirme
    public static function requirePermission($permission) {
        if (!self::checkPermission($permission)) {
            $_SESSION['error'] = ERROR_MESSAGES['access_denied'];
            header('Location: index.php?page=dashboard');
            exit;
        }
    }
    
    // Oturum kontrolü
    public static function checkSession() {
        if (!isset($_SESSION['user_id'])) {
            header('Location: index.php?page=login');
            exit;
        }
        
        // Oturum zaman aşımı kontrolü
        if (isset($_SESSION['login_time']) && 
            (time() - $_SESSION['login_time']) > SESSION_TIMEOUT) {
            
            session_unset();
            session_destroy();
            session_start();
            $_SESSION['error'] = 'Oturumunuz zaman aşımına uğradı. Lütfen tekrar giriş yapın.';
            header('Location: index.php?page=login');
            exit;
        }
        
        // Son aktivite zamanını güncelle
        $_SESSION['login_time'] = time();
    }
    
    // Mevcut kullanıcı bilgilerini al
    public static function getCurrentUser() {
        if (!isset($_SESSION['user_id'])) {
            return null;
        }

        return [
            'id' => $_SESSION['user_id'],
            'name' => $_SESSION['user_name'] ?? 'Kullanıcı',
            'username' => $_SESSION['username'] ?? '',
            'permissions' => $_SESSION['user_permissions'] ?? []
        ];
    }
    
    // Şifre değiştirme
    public function changePassword() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Location: index.php?page=dashboard');
            exit;
        }
        
        $currentPassword = trim($_POST['current_password'] ?? '');
        $newPassword = trim($_POST['new_password'] ?? '');
        $confirmPassword = trim($_POST['confirm_password'] ?? '');
        
        // Validation
        if (empty($currentPassword) || empty($newPassword) || empty($confirmPassword)) {
            $_SESSION['error'] = 'Tüm alanlar doldurulmalıdır.';
            header('Location: index.php?page=profile');
            exit;
        }
        
        if ($newPassword !== $confirmPassword) {
            $_SESSION['error'] = 'Yeni şifreler eşleşmiyor.';
            header('Location: index.php?page=profile');
            exit;
        }
        
        if (strlen($newPassword) < 6) {
            $_SESSION['error'] = 'Yeni şifre en az 6 karakter olmalıdır.';
            header('Location: index.php?page=profile');
            exit;
        }
        
        // Mevcut şifreyi kontrol et
        $user = $this->userModel->getById($_SESSION['user_id']);
        if (!password_verify($currentPassword, $user['sifresi'])) {
            $_SESSION['error'] = 'Mevcut şifre hatalı.';
            header('Location: index.php?page=profile');
            exit;
        }
        
        // Yeni şifreyi kaydet
        $hashedPassword = $this->userModel->hashPassword($newPassword);
        $result = $this->userModel->update($_SESSION['user_id'], ['sifresi' => $hashedPassword]);
        
        if ($result) {
            $_SESSION['success'] = 'Şifreniz başarıyla değiştirildi.';
        } else {
            $_SESSION['error'] = 'Şifre değiştirme sırasında hata oluştu.';
        }
        
        header('Location: index.php?page=profile');
        exit;
    }
}
?>

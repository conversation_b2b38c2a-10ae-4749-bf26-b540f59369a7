object frmUrunler: TfrmUrunler
  Left = 0
  Top = 0
  ClientHeight = 444
  ClientWidth = 620
  Caption = #220'r'#252'nler'
  OnShow = UniFormShow
  OldCreateOrder = False
  MonitoredKeys.Keys = <>
  PixelsPerInch = 96
  TextHeight = 13
  object UniPanel1: TUniPanel
    Left = 0
    Top = 411
    Width = 620
    Height = 33
    Align = alBottom
    TabOrder = 0
    BorderStyle = ubsNone
    Color = 16447736
    object UniSpeedButton2: TUniSpeedButton
      AlignWithMargins = True
      Left = 472
      Top = 3
      Width = 72
      Height = 27
      Caption = 'Kaydet'
      Align = alRight
      ParentColor = False
      Color = clBtnFace
      TabOrder = 1
      OnClick = UniSpeedButton2Click
    end
    object UniSpeedButton3: TUniSpeedButton
      AlignWithMargins = True
      Left = 550
      Top = 3
      Width = 67
      Height = 27
      Caption = 'Kapat'
      Align = alRight
      ParentColor = False
      Color = clBtnFace
      TabOrder = 2
      OnClick = UniSpeedButton3Click
    end
    object UniSpeedButton5: TUniSpeedButton
      AlignWithMargins = True
      Left = 3
      Top = 3
      Width = 75
      Height = 27
      Caption = 'Sil'
      Align = alLeft
      ParentColor = False
      Color = clBtnFace
      TabOrder = 3
      OnClick = UniSpeedButton5Click
    end
    object UniSpeedButton1: TUniSpeedButton
      AlignWithMargins = True
      Left = 394
      Top = 3
      Width = 72
      Height = 27
      Caption = 'Yeni'
      Align = alRight
      ParentColor = False
      Color = clBtnFace
      TabOrder = 4
      OnClick = UniSpeedButton1Click
    end
    object UniSpeedButton4: TUniSpeedButton
      AlignWithMargins = True
      Left = 84
      Top = 3
      Width = 75
      Height = 27
      Caption = 'Etmenler'
      Align = alLeft
      ParentColor = False
      Color = clBtnFace
      TabOrder = 5
      OnClick = UniSpeedButton4Click
    end
  end
  object UniDBGrid1: TUniDBGrid
    AlignWithMargins = True
    Left = 3
    Top = 52
    Width = 614
    Height = 356
    ClicksToEdit = 1
    DataSource = dsurun
    Options = [dgEditing, dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgConfirmDelete]
    WebOptions.Paged = False
    WebOptions.FetchAll = True
    LoadMask.Message = 'L'#195#188'tfen Bekleyiniz...'
    ForceFit = True
    StripeRows = False
    Align = alClient
    TabOrder = 1
    Columns = <
      item
        FieldName = 'URUNADI_T'
        Title.Caption = #220'r'#252'n Ad'#305
        Width = 347
      end
      item
        FieldName = 'etmenler'
        Title.Caption = 'Etmenler'
        Width = 442
        Visible = False
      end>
  end
  object UniPanel2: TUniPanel
    Left = 0
    Top = 0
    Width = 620
    Height = 49
    Align = alTop
    TabOrder = 2
    DesignSize = (
      620
      49)
    object edtAra: TUniEdit
      Left = 16
      Top = 16
      Width = 585
      Anchors = [akLeft, akTop, akRight, akBottom]
      TabOrder = 1
      Color = clInfoBk
      EmptyText = 'Aranacak '#220'r'#252'n Ad'#305
      OnKeyDown = edtAraKeyDown
    end
  end
  object tblUrun: TpFIBDataSet
    UpdateSQL.Strings = (
      'UPDATE URUNLER'
      'SET '
      '    URUNADI_T = :URUNADI_T,'
      '    URUNADI_A = :URUNADI_A,'
      '    URUNADI_I = :URUNADI_I,'
      '    URUNADI_L = :URUNADI_L,'
      '    DURUMU = :DURUMU,'
      '    PRODCODE = :PRODCODE,'
      '    KAYDEDEN = :KAYDEDEN,'
      '    DEGISTIREN = :DEGISTIREN,'
      '    KAYITZAMANI = :KAYITZAMANI,'
      '    DEGISTIRMEZAMANI = :DEGISTIRMEZAMANI,'
      '    ALTGRUPID = :ALTGRUPID,'
      '    DS_FIYATGRUBU = :DS_FIYATGRUBU,'
      '    GTIPNO = :GTIPNO,'
      '    ETMENLER = :ETMENLER'
      'WHERE'
      '    ID = :OLD_ID'
      '    ')
    DeleteSQL.Strings = (
      'DELETE FROM'
      '    URUNLER'
      'WHERE'
      '        ID = :OLD_ID'
      '    ')
    InsertSQL.Strings = (
      'INSERT INTO URUNLER('
      '    ID,'
      '    URUNADI_T,'
      '    URUNADI_A,'
      '    URUNADI_I,'
      '    URUNADI_L,'
      '    DURUMU,'
      '    PRODCODE,'
      '    KAYDEDEN,'
      '    DEGISTIREN,'
      '    KAYITZAMANI,'
      '    DEGISTIRMEZAMANI,'
      '    ALTGRUPID,'
      '    DS_FIYATGRUBU,'
      '    GTIPNO,'
      '    ETMENLER'
      ')'
      'VALUES('
      '    :ID,'
      '    :URUNADI_T,'
      '    :URUNADI_A,'
      '    :URUNADI_I,'
      '    :URUNADI_L,'
      '    :DURUMU,'
      '    :PRODCODE,'
      '    :KAYDEDEN,'
      '    :DEGISTIREN,'
      '    :KAYITZAMANI,'
      '    :DEGISTIRMEZAMANI,'
      '    :ALTGRUPID,'
      '    :DS_FIYATGRUBU,'
      '    :GTIPNO,'
      '    :ETMENLER'
      ')')
    RefreshSQL.Strings = (
      'SELECT'
      '    *'
      'FROM'
      'urunler'
      ''
      ' WHERE '
      '        URUNLER.ID = :OLD_ID'
      '    ')
    SelectSQL.Strings = (
      'SELECT'
      '    *'
      'FROM'
      'urunler'
      ''
      'order by urunadi_t COLLATE PXW_TURK')
    AutoUpdateOptions.UpdateTableName = 'SERTIFIKA'
    AutoUpdateOptions.KeyFields = 'ID'
    AutoUpdateOptions.GeneratorName = 'GEN_SERTIFIKA_ID'
    AutoUpdateOptions.WhenGetGenID = wgOnNewRecord
    Transaction = UniMainModule.trtanim
    Database = UniMainModule.db
    Left = 224
    Top = 256
  end
  object dsurun: TDataSource
    DataSet = tblUrun
    Left = 288
    Top = 256
  end
end

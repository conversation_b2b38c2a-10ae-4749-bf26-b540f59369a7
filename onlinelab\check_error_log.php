<?php
// PHP error log'unu kontrol et
$errorLogPath = ini_get('error_log');
if (!$errorLogPath) {
    $errorLogPath = 'C:\xampp\php\logs\php_error_log';
}

echo "<h2>PHP Error Log Kontrolü</h2>";
echo "<p><strong>Error Log Path:</strong> $errorLogPath</p>";

if (file_exists($errorLogPath)) {
    $lines = file($errorLogPath);
    $recentLines = array_slice($lines, -50); // Son 50 satır
    
    echo "<h3>Son 50 Log Satırı:</h3>";
    echo "<pre style='background: #f5f5f5; padding: 10px; max-height: 400px; overflow-y: scroll;'>";
    foreach ($recentLines as $line) {
        echo htmlspecialchars($line);
    }
    echo "</pre>";
} else {
    echo "<p style='color: red;'>Error log dosyası bulunamadı: $errorLogPath</p>";
    
    // Alternatif konumları kontrol et
    $altPaths = [
        'C:\xampp\apache\logs\error.log',
        'C:\xampp\logs\php_error_log',
        '/var/log/php_errors.log',
        './error.log'
    ];
    
    echo "<h3>Alternatif Konumlar:</h3>";
    foreach ($altPaths as $path) {
        if (file_exists($path)) {
            echo "<p style='color: green;'>✅ Bulundu: $path</p>";
        } else {
            echo "<p style='color: red;'>❌ Yok: $path</p>";
        }
    }
}

// Session mesajlarını da kontrol et
echo "<h3>Session Mesajları:</h3>";
session_start();
if (isset($_SESSION['error'])) {
    echo "<div style='color: red; background: #ffe6e6; padding: 10px; border: 1px solid #ff0000;'>";
    echo "<strong>Error:</strong> " . htmlspecialchars($_SESSION['error']);
    echo "</div>";
}

if (isset($_SESSION['form_errors'])) {
    echo "<div style='color: red; background: #ffe6e6; padding: 10px; border: 1px solid #ff0000;'>";
    echo "<strong>Form Errors:</strong><br>";
    foreach ($_SESSION['form_errors'] as $field => $error) {
        echo "$field: $error<br>";
    }
    echo "</div>";
}

if (isset($_SESSION['form_data'])) {
    echo "<div style='background: #e6f3ff; padding: 10px; border: 1px solid #0066cc;'>";
    echo "<strong>Form Data:</strong><br>";
    echo "<pre>" . print_r($_SESSION['form_data'], true) . "</pre>";
    echo "</div>";
}
?>

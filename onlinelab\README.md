# Online Laboratuvar Yönetim Sistemi

Bu proje, Delphi ile geliştirilmiş laboratuvar yönetim sisteminin PHP Core ile MV (Model-View) yapısına göre yeniden yazılmış halidir.

## Özellikler

### 🧪 Numune Yönetimi
- Numune kabul ve kayıt işlemleri
- İç/Dış karantina numune türleri
- Otomatik laboratuvar kayıt numarası oluşturma
- Numune durumu takibi
- Detaylı numune bilgileri

### 🔬 Analiz Yönetimi
- Laboratuvar birimlerine numune atama
- <PERSON><PERSON>z sonuçları girişi
- Etmen bazlı sonuç takibi
- Ana<PERSON>z metodları kayıtları

### 👥 Kullanıcı Yönetimi
- Rol tabanlı yetki sistemi
- Kullanıcı ekleme/düzenleme
- Güvenli şifre yönetimi
- Oturum yönetimi

### 📊 Raporlama ve İstatistikler
- Dashboard ile genel durum görünümü
- Grafik ve istatistikler
- Numune durumu dağılımları
- Birim iş yükü takibi

### 🔧 Tanımlar
- Ürün tanımları
- Laboratuvar birimleri
- Genel tanımlar (Ülkeler, Firmalar, vb.)
- Etmen tanımları

## Teknolojiler

- **Backend**: PHP 7.4+
- **Veritabanı**: MySQL 5.7+
- **Frontend**: Bootstrap 5, jQuery, Chart.js
- **Mimari**: MV (Model-View) Pattern
- **Güvenlik**: PDO, Prepared Statements, Password Hashing

## Kurulum

### 1. Sistem Gereksinimleri
- PHP 7.4 veya üzeri
- MySQL 5.7 veya üzeri
- Apache/Nginx web sunucusu
- PDO MySQL extension

### 2. Dosyaları Kopyalama
```bash
# Projeyi web sunucusu dizinine kopyalayın
cp -r onlinelab/ /var/www/html/
```

### 3. Veritabanı Kurulumu
```sql
-- MySQL'de aşağıdaki dosyayı çalıştırın
source install/database.sql
```

### 4. Konfigürasyon
`config/config.php` dosyasındaki veritabanı ayarlarını düzenleyin:

```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'laboratuvar_db');
define('DB_USER', 'root');
define('DB_PASS', '');
```

### 5. Dizin İzinleri
```bash
# Upload dizini için yazma izni verin
chmod 755 uploads/
```

## Varsayılan Giriş Bilgileri

- **Kullanıcı Adı**: admin
- **Şifre**: admin123

⚠️ **Güvenlik**: İlk girişten sonra mutlaka şifreyi değiştirin!

## Dizin Yapısı

```
onlinelab/
├── config/              # Konfigürasyon dosyaları
│   ├── config.php       # Ana konfigürasyon
│   └── database.php     # Veritabanı sınıfı
├── controllers/         # Controller sınıfları
│   ├── AuthController.php
│   ├── DashboardController.php
│   └── NumuneController.php
├── models/              # Model sınıfları
│   ├── BaseModel.php
│   ├── User.php
│   ├── Numune.php
│   └── ...
├── views/               # View dosyaları
│   ├── layout/          # Layout dosyaları
│   ├── dashboard.php
│   ├── login.php
│   └── ...
├── uploads/             # Dosya yükleme dizini
├── install/             # Kurulum dosyaları
│   └── database.sql
├── index.php            # Ana giriş dosyası
└── README.md
```

## MV (Model-View) Yapısı

Bu proje MV (Model-View) pattern kullanır:

### Model Katmanı
- Veritabanı işlemleri
- İş mantığı
- Veri validasyonu
- `models/` dizininde yer alır

### View Katmanı
- Kullanıcı arayüzü
- HTML çıktısı
- `views/` dizininde yer alır

### Controller Katmanı (Minimal)
- URL yönlendirme
- Model ve View arasında köprü
- `controllers/` dizininde yer alır

## Güvenlik Özellikleri

- ✅ SQL Injection koruması (PDO Prepared Statements)
- ✅ XSS koruması (htmlspecialchars)
- ✅ CSRF koruması
- ✅ Güvenli şifre hashleme (password_hash)
- ✅ Oturum güvenliği
- ✅ Yetki kontrolü

## Veritabanı Şeması

### Ana Tablolar
- `kullanicilar` - Kullanıcı bilgileri ve yetkileri
- `lab_numuneler` - Numune kayıtları
- `lab_atamalar` - Birim atamaları
- `lab_sonuc_etmenler` - Analiz sonuçları
- `urunler` - Ürün tanımları
- `laboratuvar_birimler` - Laboratuvar birimleri
- `geneltanim` - Genel tanımlar
- `lab_log` - İşlem geçmişi

### Yetki Sistemi
Kullanıcılar aşağıdaki yetkilere sahip olabilir:
- `NUMUNE_KABUL_YAPABILIR`
- `NUMUNE_LISTESI_GOREBILIR`
- `NUMUNE_ONHAZIRLIK_YAPABILIR`
- `NUMUNE_SONUC_GIREBILIR`
- `TANIMLAMA_YAPABILIR`
- `NUMUNE_GENEL_GORUNUM`
- `NUMUNE_ATAMA_YAPABILIR`

## API Endpoints

Sistem RESTful API yapısı kullanır:

```
GET  /index.php?page=dashboard          # Dashboard
GET  /index.php?page=numune-listesi     # Numune listesi
GET  /index.php?page=numune-kabul       # Numune kabul formu
POST /index.php?page=numune-kabul&action=save  # Numune kaydet
GET  /index.php?page=login              # Giriş sayfası
POST /index.php?page=login&action=authenticate # Giriş yap
```

## Geliştirme

### Yeni Model Ekleme
```php
class YeniModel extends BaseModel {
    protected $table = 'tablo_adi';
    
    // Model metodları...
}
```

### Yeni Controller Ekleme
```php
class YeniController {
    public function __construct() {
        AuthController::checkSession();
    }
    
    public function index() {
        // Controller mantığı...
        require_once 'views/yeni_view.php';
    }
}
```

### Yeni View Ekleme
```php
<?php
$pageTitle = 'Sayfa Başlığı';
require_once 'views/layout/header.php';
?>

<!-- Sayfa içeriği -->

<?php require_once 'views/layout/footer.php'; ?>
```

## Katkıda Bulunma

1. Fork yapın
2. Feature branch oluşturun (`git checkout -b feature/yeni-ozellik`)
3. Değişikliklerinizi commit edin (`git commit -am 'Yeni özellik eklendi'`)
4. Branch'inizi push edin (`git push origin feature/yeni-ozellik`)
5. Pull Request oluşturun

## Lisans

Bu proje MIT lisansı altında lisanslanmıştır.

## Destek

Herhangi bir sorun yaşarsanız:
1. GitHub Issues kullanın
2. Dokümantasyonu kontrol edin
3. Log dosyalarını inceleyin

## Changelog

### v1.0.0 (2024)
- İlk sürüm
- Delphi sisteminden PHP'ye dönüştürüm
- MV pattern implementasyonu
- Temel CRUD işlemleri
- Dashboard ve raporlama
- Kullanıcı yetki sistemi

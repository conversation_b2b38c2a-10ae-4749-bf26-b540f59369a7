<?php
/**
 * Veritabanı Bağlantı Sınıfı
 */

class Database {
    private static $instance = null;
    private $connection;
    
    private function __construct() {
        try {
            $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
            $this->connection = new PDO($dsn, DB_USER, DB_PASS, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES " . DB_CHARSET
            ]);
        } catch (PDOException $e) {
            die("Veritabanı bağlantı hatası: " . $e->getMessage());
        }
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    public function getConnection() {
        return $this->connection;
    }
    
    // Prepared statement ile sorgu çalıştırma
    public function query($sql, $params = []) {
        try {
            $stmt = $this->connection->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            error_log("SQL Hatası: " . $e->getMessage() . " - SQL: " . $sql);
            error_log("SQL Params: " . json_encode($params));
            throw new Exception("Veritabanı hatası oluştu: " . $e->getMessage());
        }
    }
    
    // Tek kayıt getir
    public function fetch($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetch();
    }
    
    // Çoklu kayıt getir
    public function fetchAll($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetchAll();
    }
    
    // Insert işlemi ve son eklenen ID'yi döndür
    public function insert($sql, $params = []) {
        $this->query($sql, $params);
        return $this->connection->lastInsertId();
    }
    
    // Update/Delete işlemi ve etkilenen satır sayısını döndür
    public function execute($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->rowCount();
    }
    
    // Transaction başlat
    public function beginTransaction() {
        return $this->connection->beginTransaction();
    }
    
    // Transaction commit
    public function commit() {
        return $this->connection->commit();
    }
    
    // Transaction rollback
    public function rollback() {
        return $this->connection->rollback();
    }
    
    // Sayfalama için toplam kayıt sayısını getir
    public function getCount($table, $where = '', $params = []) {
        $sql = "SELECT COUNT(*) as total FROM $table";
        if (!empty($where)) {
            $sql .= " WHERE $where";
        }
        $result = $this->fetch($sql, $params);
        return $result['total'] ?? 0;
    }
    
    // Sayfalama ile kayıtları getir
    public function getPaginated($sql, $params = [], $page = 1, $perPage = RECORDS_PER_PAGE) {
        $offset = ($page - 1) * $perPage;
        $sql .= " LIMIT $offset, $perPage";
        return $this->fetchAll($sql, $params);
    }
    
    // Güvenli string escape
    public function escape($string) {
        return $this->connection->quote($string);
    }
}

// Global veritabanı fonksiyonları
function db() {
    return Database::getInstance();
}

function dbQuery($sql, $params = []) {
    return db()->query($sql, $params);
}

function dbFetch($sql, $params = []) {
    return db()->fetch($sql, $params);
}

function dbFetchAll($sql, $params = []) {
    return db()->fetchAll($sql, $params);
}

function dbInsert($sql, $params = []) {
    return db()->insert($sql, $params);
}

function dbExecute($sql, $params = []) {
    return db()->execute($sql, $params);
}
?>

<?php
require_once 'config/config.php';
require_once 'config/database.php';

try {
    $db = Database::getInstance();
    
    echo "<h2>Numune Listesi Test</h2>";
    
    // Basit sorgu test
    echo "<h3>1. Basit Numune Sorgusu:</h3>";
    $sql = "SELECT * FROM lab_numuneler WHERE SILINDI = 0 LIMIT 5";
    $numuneler = $db->fetchAll($sql);
    
    echo "<p>Bulunan kayıt sayısı: " . count($numuneler) . "</p>";
    
    if (!empty($numuneler)) {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr>";
        foreach (array_keys($numuneler[0]) as $column) {
            echo "<th>$column</th>";
        }
        echo "</tr>";
        
        foreach ($numuneler as $numune) {
            echo "<tr>";
            foreach ($numune as $value) {
                echo "<td>" . htmlspecialchars($value ?? '') . "</td>";
            }
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // JOIN test
    echo "<h3>2. JOIN Sorgusu Test:</h3>";
    $sql = "SELECT n.*, u.urunadi_t as urun_adi, g.izahat as firma_adi
            FROM lab_numuneler n
            LEFT JOIN urunler u ON n.URUN_ID = u.id
            LEFT JOIN geneltanim g ON n.FIRMA_ID = g.id
            WHERE n.SILINDI = 0
            LIMIT 3";
    
    try {
        $joinResult = $db->fetchAll($sql);
        echo "<p>JOIN sorgusu başarılı. Kayıt sayısı: " . count($joinResult) . "</p>";
        
        if (!empty($joinResult)) {
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>ID</th><th>Lab Kayıt No</th><th>Ürün Adı</th><th>Firma Adı</th><th>Gönderen</th></tr>";
            foreach ($joinResult as $row) {
                echo "<tr>";
                echo "<td>" . ($row['id'] ?? '') . "</td>";
                echo "<td>" . htmlspecialchars($row['LAB_KAYITNO'] ?? '') . "</td>";
                echo "<td>" . htmlspecialchars($row['urun_adi'] ?? 'Belirtilmemiş') . "</td>";
                echo "<td>" . htmlspecialchars($row['firma_adi'] ?? '-') . "</td>";
                echo "<td>" . htmlspecialchars($row['GONDEREN'] ?? '') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>JOIN sorgusu hatası: " . $e->getMessage() . "</p>";
    }
    
    // Atama sorgusu test
    echo "<h3>3. Atama Sorgusu Test:</h3>";
    if (!empty($numuneler)) {
        $numuneId = $numuneler[0]['id'];
        $sql = "SELECT GROUP_CONCAT(DISTINCT b.birimadi SEPARATOR ', ') as birimler
                FROM lab_atamalar a
                LEFT JOIN laboratuvar_birimler b ON a.birim_id = b.id
                WHERE a.numune_id = ?";
        
        try {
            $atamaResult = $db->fetch($sql, [$numuneId]);
            echo "<p>Numune ID $numuneId için atanan birimler: " . ($atamaResult['birimler'] ?? 'Yok') . "</p>";
        } catch (Exception $e) {
            echo "<p style='color: red;'>Atama sorgusu hatası: " . $e->getMessage() . "</p>";
        }
    }
    
    // Tablo yapısı kontrol
    echo "<h3>4. Tablo Yapısı Kontrolü:</h3>";
    $tables = ['lab_numuneler', 'urunler', 'geneltanim', 'lab_atamalar', 'laboratuvar_birimler'];
    
    foreach ($tables as $table) {
        try {
            $sql = "DESCRIBE $table";
            $columns = $db->fetchAll($sql);
            echo "<h4>$table tablosu (" . count($columns) . " sütun):</h4>";
            echo "<ul>";
            foreach ($columns as $col) {
                echo "<li>{$col['Field']} - {$col['Type']}</li>";
            }
            echo "</ul>";
        } catch (Exception $e) {
            echo "<p style='color: red;'>$table tablosu bulunamadı: " . $e->getMessage() . "</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<div style='color: red;'>Genel Hata: " . $e->getMessage() . "</div>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>

<?php
$pageTitle = 'Numune Listesi';
$pageSubtitle = 'Tüm numune kayıtları';
require_once 'views/layout/header.php';
?>

<style>
/* Responsive tablo stilleri */
.table-responsive {
    border-radius: 0.375rem;
    border: 1px solid #dee2e6;
}

#numunelerTable {
    margin-bottom: 0;
    font-size: 0.875rem;
}

#numunelerTable th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    white-space: nowrap;
    padding: 0.75rem 0.5rem;
}

#numunelerTable td {
    padding: 0.5rem;
    vertical-align: middle;
    white-space: nowrap;
}

/* Badge stilleri */
.badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

/* Fixed columns için */
.dtfc-fixed-left {
    background-color: white !important;
    border-right: 2px solid #dee2e6;
}

/* Global arama input */
#globalSearch {
    border-radius: 0.375rem 0 0 0.375rem;
}

/* Responsive breakpoints */
@media (max-width: 768px) {
    .d-flex.align-items-center.gap-3 {
        flex-direction: column;
        gap: 1rem !important;
    }

    .input-group {
        width: 100% !important;
    }
}

/* Tablo scroll bar */
.dataTables_scrollBody::-webkit-scrollbar {
    height: 8px;
}

.dataTables_scrollBody::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.dataTables_scrollBody::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.dataTables_scrollBody::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
</style>

<?php

$numuneler = $data['numuneler'] ?? [];
$pagination = $data['pagination'] ?? [];
$filters = $data['filters'] ?? [];
?>

<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-filter me-2"></i>Filtreler
                    </h5>
                    <button class="btn btn-outline-secondary btn-sm" type="button" data-bs-toggle="collapse" 
                            data-bs-target="#filterCollapse" aria-expanded="false">
                        <i class="fas fa-chevron-down"></i>
                    </button>
                </div>
            </div>
            <div class="collapse" id="filterCollapse">
                <div class="card-body">
                    <form method="GET" action="index.php" id="filterForm">
                        <input type="hidden" name="page" value="numune-listesi">
                        
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <label for="numune_turu" class="form-label">Numune Türü</label>
                                <select class="form-select" id="numune_turu" name="numune_turu">
                                    <option value="">Tümü</option>
                                    <?php foreach ($data['numune_turleri'] as $tur): ?>
                                        <option value="<?= htmlspecialchars($tur) ?>" 
                                                <?= $filters['numune_turu'] === $tur ? 'selected' : '' ?>>
                                            <?= htmlspecialchars($tur) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            
                            <div class="col-md-3 mb-3">
                                <label for="durumu" class="form-label">Durum</label>
                                <select class="form-select" id="durumu" name="durumu">
                                    <option value="">Tümü</option>
                                    <?php foreach ($data['numune_durumlari'] as $durum): ?>
                                        <option value="<?= htmlspecialchars($durum) ?>" 
                                                <?= $filters['durumu'] === $durum ? 'selected' : '' ?>>
                                            <?= htmlspecialchars($durum) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            
                            <div class="col-md-3 mb-3">
                                <label for="lab_kayitno" class="form-label">Lab Kayıt No</label>
                                <input type="text" class="form-control" id="lab_kayitno" name="lab_kayitno" 
                                       value="<?= htmlspecialchars($filters['lab_kayitno']) ?>"
                                       placeholder="Lab kayıt no ara...">
                            </div>
                            
                            <div class="col-md-3 mb-3">
                                <label for="gonderen" class="form-label">Gönderen</label>
                                <input type="text" class="form-control" id="gonderen" name="gonderen" 
                                       value="<?= htmlspecialchars($filters['gonderen']) ?>"
                                       placeholder="Gönderen ara...">
                            </div>
                            
                            <div class="col-md-3 mb-3">
                                <label for="baslangic_tarihi" class="form-label">Başlangıç Tarihi</label>
                                <input type="date" class="form-control" id="baslangic_tarihi" name="baslangic_tarihi" 
                                       value="<?= htmlspecialchars($filters['baslangic_tarihi']) ?>">
                            </div>
                            
                            <div class="col-md-3 mb-3">
                                <label for="bitis_tarihi" class="form-label">Bitiş Tarihi</label>
                                <input type="date" class="form-control" id="bitis_tarihi" name="bitis_tarihi" 
                                       value="<?= htmlspecialchars($filters['bitis_tarihi']) ?>">
                            </div>
                            
                            <div class="col-md-6 mb-3 d-flex align-items-end">
                                <div class="btn-group w-100">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search me-2"></i>Filtrele
                                    </button>
                                    <a href="index.php?page=numune-listesi" class="btn btn-outline-secondary">
                                        <i class="fas fa-times me-2"></i>Temizle
                                    </a>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-white">
                <div class="d-flex justify-content-between align-items-center flex-wrap">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i>
                        Numune Listesi
                        <span class="badge bg-primary" id="totalRecords"><?= count($numuneler) ?></span>
                    </h5>

                    <div class="d-flex align-items-center gap-3">
                        <!-- Yeni Numune Butonu -->
                        <?php if (AuthController::checkPermission('NUMUNE_KABUL_YAPABILIR')): ?>
                            <a href="index.php?page=numune-kabul" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>Yeni Numune
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <div class="card-body">
                <?php if (empty($numuneler)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-search fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Numune bulunamadı</h5>
                        <p class="text-muted">Arama kriterlerinizi değiştirerek tekrar deneyin.</p>
                        <?php if (AuthController::checkPermission('NUMUNE_KABUL_YAPABILIR')): ?>
                            <a href="index.php?page=numune-kabul" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>İlk Numuneyi Ekle
                            </a>
                        <?php endif; ?>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover table-sm" id="numunelerTable">
                            <thead>
                                <tr style="background: #f8f9fa !important; background-image: none !important; color: #000 !important;">
                                    <th style="min-width: 120px; background: #f8f9fa !important; background-image: none !important; color: #000 !important; font-weight: 600;">Lab Kayıt No</th>
                                    <th style="min-width: 100px; background: #f8f9fa !important; background-image: none !important; color: #000 !important; font-weight: 600;">Tür</th>
                                    <th style="min-width: 120px; background: #f8f9fa !important; background-image: none !important; color: #000 !important; font-weight: 600;">Ürün Adı</th>
                                    <th style="min-width: 120px; background: #f8f9fa !important; background-image: none !important; color: #000 !important; font-weight: 600;">Gönderen</th>
                                    <th style="min-width: 120px; background: #f8f9fa !important; background-image: none !important; color: #000 !important; font-weight: 600;">Firma Adı</th>
                                    <th style="min-width: 100px; background: #f8f9fa !important; background-image: none !important; color: #000 !important; font-weight: 600;">Miktar</th>
                                    <th style="min-width: 80px; background: #f8f9fa !important; background-image: none !important; color: #000 !important; font-weight: 600;">Ücret Durumu</th>
                                    <th style="min-width: 100px; background: #f8f9fa !important; background-image: none !important; color: #000 !important; font-weight: 600;">Etiket No</th>
                                    <th style="min-width: 100px; background: #f8f9fa !important; background-image: none !important; color: #000 !important; font-weight: 600;">Başvuru No</th>
                                    <th style="min-width: 100px; background: #f8f9fa !important; background-image: none !important; color: #000 !important; font-weight: 600;">Mühür No</th>
                                    <th style="min-width: 100px; background: #f8f9fa !important; background-image: none !important; color: #000 !important; font-weight: 600;">Menşei</th>
                                    <th style="min-width: 100px; background: #f8f9fa !important; background-image: none !important; color: #000 !important; font-weight: 600;">Teslim Eden</th>
                                    <th style="min-width: 100px; background: #f8f9fa !important; background-image: none !important; color: #000 !important; font-weight: 600;">Teslim Alan</th>
                                    <th style="min-width: 120px; background: #f8f9fa !important; background-image: none !important; color: #000 !important; font-weight: 600;">Atanan Birimler</th>
                                    <th style="min-width: 100px; background: #f8f9fa !important; background-image: none !important; color: #000 !important; font-weight: 600;">Durum</th>
                                    <th style="min-width: 120px; background: #f8f9fa !important; background-image: none !important; color: #000 !important; font-weight: 600;">Geliş Tarihi</th>
                                    <th class="no-sort" style="min-width: 80px; background: #f8f9fa !important; background-image: none !important; color: #000 !important; font-weight: 600;">İşlemler</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($numuneler as $numune): ?>
                                    <tr>
                                        <!-- Lab Kayıt No -->
                                        <td>
                                            <strong class="text-primary">
                                                <?= safe_html($numune['LAB_KAYITNO']) ?>
                                            </strong>
                                        </td>

                                        <!-- Tür -->
                                        <td>
                                            <span class="badge <?= safe_equals($numune['numune_turu'], 'İç Karantina') ? 'bg-info' : 'bg-warning' ?>">
                                                <?= safe_html($numune['numune_turu']) ?>
                                            </span>
                                        </td>

                                        <!-- Ürün Adı -->
                                        <td><?= safe_html($numune['urun_adi'] ?: 'Belirtilmemiş') ?></td>

                                        <!-- Gönderen -->
                                        <td><?= safe_html($numune['GONDEREN']) ?></td>

                                        <!-- Firma Adı -->
                                        <td><?= safe_html($numune['firma_adi'] ?: '-') ?></td>

                                        <!-- Miktar -->
                                        <td>
                                            <?= safe_number($numune['URUN_MIKTARI'], 2) ?>
                                            <?= safe_html($numune['MIKTAR_BIRIM']) ?>
                                        </td>

                                        <!-- Ücret Durumu -->
                                        <td>
                                            <span class="badge <?= get_payment_badge_class($numune['UCRET_DURUMU']) ?> payment-status-badge"
                                                  data-numune-id="<?= $numune['id'] ?>"
                                                  style="cursor: pointer;"
                                                  title="Durumu değiştirmek için tıklayın">
                                                <?= safe_html($numune['UCRET_DURUMU'] ?: 'Belirtilmemiş') ?>
                                            </span>
                                        </td>

                                        <!-- Etiket No -->
                                        <td><?= safe_html($numune['ETIKET_NO'] ?: '-') ?></td>

                                        <!-- Başvuru No -->
                                        <td><?= safe_html($numune['BASVURU_NO'] ?: '-') ?></td>

                                        <!-- Mühür No -->
                                        <td><?= safe_html($numune['MUHUR_NO'] ?: '-') ?></td>

                                        <!-- Menşei -->
                                        <td><?= safe_html($numune['MENSEI'] ?: '-') ?></td>

                                        <!-- Teslim Eden -->
                                        <td><?= safe_html($numune['TESLIM_EDEN'] ?: '-') ?></td>

                                        <!-- Teslim Alan -->
                                        <td><?= safe_html($numune['TESLIM_ALAN'] ?: '-') ?></td>

                                        <!-- Atanan Birimler -->
                                        <td>
                                            <?php if (!empty($numune['atanan_birimler'])): ?>
                                                <span class="badge bg-primary"><?= safe_html($numune['atanan_birimler']) ?></span>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>

                                        <!-- Durum -->
                                        <td>
                                            <span class="badge <?= get_status_badge_class($numune['DURUMU']) ?>">
                                                <?= safe_html($numune['DURUMU']) ?>
                                            </span>
                                        </td>

                                        <!-- Geliş Tarihi -->
                                        <td>
                                            <?= safe_date($numune['GELIS_TARIHI']) ?>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm dropstart">
                                                <button type="button" class="btn btn-outline-primary dropdown-toggle"
                                                        data-bs-toggle="dropdown"
                                                        data-bs-auto-close="true"
                                                        title="İşlemler">
                                                    <i class="fas fa-cog"></i>
                                                </button>
                                                <ul class="dropdown-menu dropdown-menu-end">
                                                    <li>
                                                        <a class="dropdown-item"
                                                           href="index.php?page=numune-detay&id=<?= $numune['id'] ?>">
                                                            <i class="fas fa-eye me-2"></i>Detay
                                                        </a>
                                                    </li>

                                                    <?php
                                                    // Numune durumunu kontrol et - sadece "Numune Kabul" durumunda düzenleme/silme/sonuç gir yapılabilir
                                                    $canEdit = in_array($numune['durumu'], ['Numune Kabul']);
                                                    ?>

                                                    <?php if (AuthController::checkPermission('NUMUNE_KABUL_YAPABILIR') && $canEdit): ?>
                                                        <li>
                                                            <a class="dropdown-item"
                                                               href="index.php?page=numune-kabul&id=<?= $numune['id'] ?>">
                                                                <i class="fas fa-edit me-2"></i>Düzenle
                                                            </a>
                                                        </li>
                                                    <?php endif; ?>

                                                    <?php if (AuthController::checkPermission('NUMUNE_SONUC_GIREBILIR') && $canEdit): ?>
                                                        <li>
                                                            <a class="dropdown-item"
                                                               href="index.php?page=numune-sonuc&id=<?= $numune['id'] ?>">
                                                                <i class="fas fa-flask me-2"></i>Sonuç Gir
                                                            </a>
                                                        </li>
                                                    <?php endif; ?>

                                                    <?php if (AuthController::checkPermission('NUMUNE_ATAMA_YAPABILIR') && $canEdit): ?>
                                                        <li>
                                                            <a class="dropdown-item"
                                                               href="index.php?page=onhazirlik&action=atama&id=<?= $numune['id'] ?>">
                                                                <i class="fas fa-share me-2"></i>Birim Ata
                                                            </a>
                                                        </li>
                                                    <?php endif; ?>

                                                    <li><hr class="dropdown-divider"></li>

                                                    <li>
                                                        <a class="dropdown-item" href="#" onclick="printNumune(<?= $numune['id'] ?>)">
                                                            <i class="fas fa-print me-2"></i>Yazdır
                                                        </a>
                                                    </li>

                                                    <?php if (AuthController::checkPermission('NUMUNE_KABUL_YAPABILIR') && $canEdit): ?>
                                                        <li><hr class="dropdown-divider"></li>
                                                        <li>
                                                            <a class="dropdown-item text-danger"
                                                               href="#"
                                                               onclick="confirmDelete('index.php?page=numune-listesi&action=delete&id=<?= $numune['id'] ?>', 'Bu numuneyi silmek istediğinizden emin misiniz?')">
                                                                <i class="fas fa-trash me-2"></i>Sil
                                                            </a>
                                                        </li>
                                                    <?php endif; ?>
                                                </ul>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    

                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php
$customJS = "
<script>
let dataTable;

// DataTable başlatma
$(document).ready(function() {
    // Basit DataTable başlat
    $('#numunelerTable').DataTable({
        paging: true,
        pageLength: 25,
        lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, 'Tümü']],
        searching: true,
        ordering: true,
        info: true,
        scrollX: true,
        language: {
            search: 'Ara:',
            lengthMenu: 'Sayfa başına _MENU_ kayıt göster',
            info: '_TOTAL_ kayıttan _START_ - _END_ arası gösteriliyor',
            infoEmpty: 'Kayıt bulunamadı',
            infoFiltered: '(_MAX_ kayıt içinden filtrelendi)',
            paginate: {
                first: 'İlk',
                last: 'Son',
                next: 'Sonraki',
                previous: 'Önceki'
            },
            emptyTable: 'Tabloda veri bulunmuyor',
            zeroRecords: 'Eşleşen kayıt bulunamadı'
        },
        columnDefs: [
            {
                orderable: false,
                targets: 'no-sort'
            }
        ]
    });
});

// Numune yazdırma
function printNumune(id) {
    window.open('index.php?page=numune-yazdir&id=' + id, '_blank');
}

// Excel export
function exportToExcel() {
    const params = new URLSearchParams(window.location.search);
    params.set('export', 'excel');
    window.location.href = 'index.php?' + params.toString();
}

// PDF export
function exportToPDF() {
    const params = new URLSearchParams(window.location.search);
    params.set('export', 'pdf');
    window.location.href = 'index.php?' + params.toString();
}

// Hızlı filtre
$('#filterForm input, #filterForm select').on('change', function() {
    if ($(this).val()) {
        $('#filterForm').submit();
    }
});

// Enter tuşu ile arama
$('#lab_kayitno, #gonderen').on('keypress', function(e) {
    if (e.which === 13) {
        $('#filterForm').submit();
    }
});

// Responsive tablo için tooltip
$(document).ready(function() {
    $('[title]').tooltip();

    // Ödeme durumu toggle
    $('.payment-status-badge').on('click', function() {
        const badge = $(this);
        const numuneId = badge.data('numune-id');

        if (!numuneId) return;

        // Loading state
        const originalText = badge.text();
        badge.html('<i class=\"fas fa-spinner fa-spin\"></i>').prop('disabled', true);

        $.ajax({
            url: 'index.php?page=numune-sonuc&action=toggle-odeme',
            method: 'POST',
            data: { numune_id: numuneId },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    // Badge'i güncelle
                    badge.removeClass('bg-success bg-danger bg-info bg-warning bg-secondary')
                         .addClass(response.badge_class)
                         .text(response.new_status);

                    // Başarı mesajı
                    showToast('Ödeme durumu güncellendi: ' + response.new_status, 'success');
                } else {
                    badge.text(originalText);
                    showToast('Hata: ' + (response.message || 'Bilinmeyen hata'), 'error');
                }
            },
            error: function() {
                badge.text(originalText);
                showToast('Sunucu hatası oluştu', 'error');
            },
            complete: function() {
                badge.prop('disabled', false);
            }
        });
    });
});

// Toast mesaj gösterme fonksiyonu
function showToast(message, type = 'info') {
    const toastClass = type === 'success' ? 'bg-success' :
                      type === 'error' ? 'bg-danger' : 'bg-info';

    const toast = $(
        '<div class=\"toast align-items-center text-white ' + toastClass + ' border-0\" role=\"alert\">' +
            '<div class=\"d-flex\">' +
                '<div class=\"toast-body\">' + message + '</div>' +
                '<button type=\"button\" class=\"btn-close btn-close-white me-2 m-auto\" data-bs-dismiss=\"toast\"></button>' +
            '</div>' +
        '</div>'
    );

    // Toast container yoksa oluştur
    if (!$('#toast-container').length) {
        $('body').append('<div id=\"toast-container\" class=\"toast-container position-fixed top-0 end-0 p-3\"></div>');
    }

    $('#toast-container').append(toast);

    // Bootstrap toast'ı başlat
    const bsToast = new bootstrap.Toast(toast[0]);
    bsToast.show();

    // Toast kapandıktan sonra DOM'dan kaldır
    toast.on('hidden.bs.toast', function() {
        $(this).remove();
    });
}
</script>
";

require_once 'views/layout/footer.php';
?>

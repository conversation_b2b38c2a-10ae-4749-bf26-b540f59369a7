object frmLaboratuvarBirimler: TfrmLaboratuvarBirimler
  Left = 0
  Top = 0
  ClientHeight = 548
  ClientWidth = 533
  Caption = 'Laboratuvar Birim Tan'#305'mlar'#305
  OnShow = UniFormShow
  OldCreateOrder = False
  MonitoredKeys.Keys = <>
  ActiveControl = cmbBirimAdi
  PixelsPerInch = 96
  TextHeight = 13
  object UniPanel1: TUniPanel
    Left = 0
    Top = 0
    Width = 533
    Height = 65
    Align = alTop
    TabOrder = 0
    BorderStyle = ubsNone
    object UniSpeedButton1: TUniSpeedButton
      Left = 399
      Top = 23
      Width = 57
      Height = 30
      Caption = 'Ekle'
      ParentColor = False
      Color = clBtnFace
      TabOrder = 1
      OnClick = UniSpeedButton1Click
    end
    object UniLabel1: TUniLabel
      Left = 8
      Top = 14
      Width = 40
      Height = 13
      Caption = 'Birim Ad'#305
      TabOrder = 2
    end
    object UniSpeedButton2: TUniSpeedButton
      Left = 462
      Top = 23
      Width = 57
      Height = 30
      Caption = 'Sil'
      ParentColor = False
      Color = clBtnFace
      TabOrder = 3
      OnClick = UniSpeedButton2Click
    end
    object cmbBirimAdi: TUniEdit
      Left = 6
      Top = 31
      Width = 388
      TabOrder = 4
    end
  end
  object UniDBGrid1: TUniDBGrid
    AlignWithMargins = True
    Left = 3
    Top = 68
    Width = 527
    Height = 209
    DataSource = dsBirimler
    Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgConfirmDelete]
    WebOptions.Paged = False
    WebOptions.FetchAll = True
    LoadMask.Message = 'L'#195#188'tfen Bekleyiniz...'
    ForceFit = True
    StripeRows = False
    Align = alTop
    TabOrder = 1
    OnColumnActionClick = UniDBGrid1ColumnActionClick
    Columns = <
      item
        FieldName = 'BIRIMADI'
        Title.Caption = 'Birim Ad'#305
        Width = 207
      end
      item
        FieldName = 'SIRANO'
        Title.Caption = 'S'#305'ra No'
        Width = 50
      end
      item
        ActionColumn.Enabled = True
        ActionColumn.Buttons = <
          item
            ButtonId = 0
            Hint = 'Birimin Etmenleri'
            IconCls = 'add'
          end>
        Title.Caption = ' '
        Width = 28
      end>
  end
  object UniPanel2: TUniPanel
    Left = 0
    Top = 280
    Width = 533
    Height = 93
    Align = alTop
    TabOrder = 2
    BorderStyle = ubsNone
    TitleVisible = True
    Title = 'Birimin Kullan'#305'c'#305'lar'#305
    ExplicitTop = 300
    object UniSpeedButton3: TUniSpeedButton
      Left = 399
      Top = 23
      Width = 57
      Height = 30
      Caption = 'Ekle'
      ParentColor = False
      Color = clBtnFace
      TabOrder = 1
      OnClick = UniSpeedButton3Click
    end
    object UniLabel2: TUniLabel
      Left = 8
      Top = 11
      Width = 40
      Height = 13
      Caption = 'Kullan'#305'c'#305' '
      TabOrder = 2
    end
    object UniSpeedButton4: TUniSpeedButton
      Left = 462
      Top = 23
      Width = 57
      Height = 30
      Caption = 'Sil'
      ParentColor = False
      Color = clBtnFace
      TabOrder = 3
      OnClick = UniSpeedButton4Click
    end
    object UniDBLookupComboBox1: TUniDBLookupComboBox
      Left = 8
      Top = 30
      Width = 385
      ListField = 'ADISOYADI'
      ListSource = UniMainModule.dsKullanicilar
      KeyField = 'ID'
      ListFieldIndex = 0
      TabOrder = 4
      Color = clWindow
      Style = csDropDown
    end
  end
  object UniDBGrid2: TUniDBGrid
    AlignWithMargins = True
    Left = 3
    Top = 376
    Width = 527
    Height = 169
    DataSource = dsbirimKullanicilar
    Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgConfirmDelete]
    WebOptions.Paged = False
    WebOptions.FetchAll = True
    LoadMask.Message = 'L'#195#188'tfen Bekleyiniz...'
    ForceFit = True
    StripeRows = False
    Align = alClient
    TabOrder = 3
    Columns = <
      item
        FieldName = 'adisoyadi'
        Title.Caption = 'Ad'#305' Soyad'#305
        Width = 207
      end>
  end
  object dsBirimler: TDataSource
    DataSet = tblBirimler
    Left = 344
    Top = 232
  end
  object tblBirimler: TpFIBDataSet
    UpdateSQL.Strings = (
      'UPDATE LABORATUVAR_BIRIMLER'
      'SET '
      '    BIRIMADI = :BIRIMADI,'
      '    PERSONEL = :PERSONEL'
      'WHERE'
      '    ID = :OLD_ID'
      '    ')
    DeleteSQL.Strings = (
      'DELETE FROM'
      '    LABORATUVAR_BIRIMLER'
      'WHERE'
      '        ID = :OLD_ID'
      '    ')
    InsertSQL.Strings = (
      'INSERT INTO LABORATUVAR_BIRIMLER('
      '    ID,'
      '    BIRIMADI,'
      '    PERSONEL'
      ')'
      'VALUES('
      '    :ID,'
      '    :BIRIMADI,'
      '    :PERSONEL'
      ')')
    RefreshSQL.Strings = (
      'select * from LABORATUVAR_BIRIMLER'
      ''
      ''
      ' WHERE '
      '        LABORATUVAR_BIRIMLER.ID = :OLD_ID'
      '    ')
    SelectSQL.Strings = (
      'select * from LABORATUVAR_BIRIMLER'
      '')
    Transaction = UniMainModule.trtanim
    Database = UniMainModule.db
    Left = 232
    Top = 232
  end
  object tblBirimKullanicilar: TpFIBDataSet
    UpdateSQL.Strings = (
      'UPDATE LAB_BIRIM_KULLANICILARI'
      'SET '
      '    LAB_ID = :LAB_ID,'
      '    KULLANICI_ID = :KULLANICI_ID'
      'WHERE'
      '    ID = :OLD_ID'
      '    ')
    DeleteSQL.Strings = (
      'DELETE FROM'
      '    LAB_BIRIM_KULLANICILARI'
      'WHERE'
      '        ID = :OLD_ID'
      '    ')
    InsertSQL.Strings = (
      'INSERT INTO LAB_BIRIM_KULLANICILARI('
      '    ID,'
      '    LAB_ID,'
      '    KULLANICI_ID'
      ')'
      'VALUES('
      '    :ID,'
      '    :LAB_ID,'
      '    :KULLANICI_ID'
      ')')
    RefreshSQL.Strings = (
      
        'select bk.*,kullanicilar.adisoyadi from LAB_BIRIM_KULLANICILARI ' +
        'bk'
      'left join kullanicilar on (kullanicilar.id=bk.kullanici_id)'
      'where    BK.ID = :OLD_ID'
      '     '
      '')
    SelectSQL.Strings = (
      
        'select bk.*,kullanicilar.adisoyadi from LAB_BIRIM_KULLANICILARI ' +
        'bk'
      'left join kullanicilar on (kullanicilar.id=bk.kullanici_id)'
      'where'
      ' bk.LAB_ID = :id')
    Transaction = UniMainModule.trtanim
    Database = UniMainModule.db
    DataSource = dsBirimler
    Left = 240
    Top = 288
  end
  object dsbirimKullanicilar: TDataSource
    DataSet = tblBirimKullanicilar
    Left = 344
    Top = 288
  end
end

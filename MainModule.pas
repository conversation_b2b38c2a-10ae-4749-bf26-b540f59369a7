unit MainModule;

interface

uses
  uniGUIMainModule, SysUtils, Classes, Data.DB, FIBDataSet,uniDBComboBox,uniComboBox, pFIBDataSet,
  FIBQuery, pFIBQuery, FIBDatabase, pFIBDatabase, uniGUIBaseClasses,unidbgrid,  uniMemo,
  uniGUIClasses, uniSweetAlert,IdGlobal, IdHash, IdHashMessageDigest,System.IOUtils;

type
  TUniMainModule = class(TUniGUIMainModule)
    db: TpFIBDatabase;
    tr: TpFIBTransaction;
    tmpSql: TpFIBQuery;
    tmpTbl: TpFIBDataSet;
    tblGenelTanim: TpFIBDataSet;
    dsGenelTanim: TDataSource;
    tblbirim: TpFIBDataSet;
    dsBirim: TDataSource;
    trtanim: TpFIBTransaction;
    tblPersonel: TpFIBDataSet;
    dsPersonel: TDataSource;
    UniSweetAlert1: TUniSweetAlert;
    tblLabBirimler: TpFIBDataSet;
    dsLabBirimler: TDataSource;
    tblKullanicilar: TpFIBDataSet;
    dsKullanicilar: TDataSource;
    tblKullanici: TpFIBDataSet;
    tblAktifKullanici: TpFIBDataSet;
    tblLog: TpFIBDataSet;
    tblulkeler: TpFIBDataSet;
    dsUlkeler: TDataSource;
    procedure tblPersonelNewRecord(DataSet: TDataSet);
  private
    procedure StringExplode(s, Delimiter: STRING; var res: TStringList);

    { Private declarations }
  public
   function md5Sifrele(value: string): string;
   function Calculate_EAN13(Bar12Hane: string): string;
   Procedure mesajGoster(baslik:string;mesajicerik:string;sure:integer=2500;mesajTipi:string='ok');
   Procedure logKaydet(ISLEM:string;ISLEMI_YAPAN:string;ONCEKI_DEGER:string;numuneid:integer);
   Procedure ComboDoldur(tabloadi:string;sahip:integer;combo:TUniDBComboBox);
   Procedure ComboDoldur_duz(tabloadi:string;sahip:integer;combo:TUniComboBox);
   PROCEDURE LoadGridLayout(Mydbgrid : TUniDBGrid;dosyaadi:string);
   PROCEDURE SaveGridLayout(Mydbgrid : TUniDBGrid;dosyaadi:string);
   function Sql_checklistBox(smetin: String): String;
  end;

function UniMainModule: TUniMainModule;

implementation

{$R *.dfm}

uses
  UniGUIVars, ServerModule, uniGUIApplication;

function UniMainModule: TUniMainModule;
begin
  Result := TUniMainModule(UniApplication.UniMainModule)
end;

function TUniMainModule.md5Sifrele(value: string): string;
var
    hashMessageDigest5 : TIdHashMessageDigest5;
begin
    hashMessageDigest5 := nil;
    try
        hashMessageDigest5 := TIdHashMessageDigest5.Create;
        Result := IdGlobal.IndyLowerCase ( hashMessageDigest5.HashStringAsHex ( value ) );
    finally
        hashMessageDigest5.Free;
    end;
end;


PROCEDURE TUniMainModule.StringExplode(s : STRING; Delimiter : STRING; VAR res : TStringList);
BEGIN
    res.Clear;
    res.Text:=StringReplace(s, Delimiter, #13#10, [rfIgnoreCase, rfReplaceAll]);
END;


PROCEDURE TUniMainModule.SaveGridLayout(Mydbgrid : TUniDBGrid;dosyaadi:string);
VAR filename : STRING;
    lines : TStringList;
    i : INTEGER;
    my_visible : STRING;
BEGIN
    Filename:=UniServerModule.StartPath+'uploadFolder/ini/'+'GRID'+dosyaadi+'.INI';
    if not DirectoryExists(UniServerModule.StartPath+'uploadFolder/') then
       mkdir(UniServerModule.StartPath+'uploadFolder/');
    if not DirectoryExists(UniServerModule.StartPath+'uploadFolder/'+'/ini') then
       mkdir(UniServerModule.StartPath+'uploadFolder/'+'/ini');

    TRY
        lines:=TStringList.Create;
        WITH Mydbgrid DO BEGIN
            FOR i:=0 TO Mydbgrid.Columns.count-1 DO BEGIN
                IF Mydbgrid.Columns[i].Visible=TRUE THEN my_visible:='T'
      			ELSE my_visible:='F';
 			    lines.Add(
                  Mydbgrid.Columns[i].DisplayName+';;'
                  +IntToStr(Mydbgrid.Columns[i].Width)+';;'
                  +Mydbgrid.Columns[i].Title.Caption+';;'
                  +my_visible);
            END;
        END;
        lines.SaveToFile(fileName);
    FINALLY
        lines.free;
    END;
END;

PROCEDURE TUniMainModule.LoadGridLayout(Mydbgrid : TUniDBGrid;dosyaadi:string);
VAR filename : STRING;
    lines : TStringList;
    columnInfo : TStringList;
    lineCtr : INTEGER;
    colIdx : INTEGER;
    cnt : INTEGER;
BEGIN
    Filename:=UniServerModule.StartPath+'uploadFolder/'+'/ini/'+'GRID'+dosyaadi+'.INI';
    IF NOT TFile.Exists(Filename) THEN Exit;
    TRY
        lines:=TStringList.Create;
        columnInfo:=TStringList.Create;
        lines.LoadFromFile(fileName);
        FOR lineCtr:=0 TO lines.count-1 DO BEGIN
            IF trim(lines[lineCtr])<>'' THEN BEGIN
                StringExplode(lines[lineCtr], ';;', columnInfo);
                cnt:=Mydbgrid.Columns.count;
                // go through all the columns, looking for the one we are currently working on
                FOR colIdx:=0 TO cnt-1 DO BEGIN
                    // once found, set its width and title, then its index (order)
                    IF Mydbgrid.Columns[colIdx].FieldName=columnInfo[0] THEN BEGIN
                        Mydbgrid.Columns[colIdx].Width:=StrToInt(columnInfo[1]);
                     //   Mydbgrid.Columns[colIdx].Title.Caption:=columnInfo[2];
                        IF columnInfo[3]='T' THEN Mydbgrid.Columns[colIdx].Visible:=TRUE ELSE Mydbgrid.Columns[colIdx].Visible:=false;
                        Mydbgrid.Columns[colIdx].Index:=lineCtr;
                    END;
                END;
            END;
        END;
    FINALLY
        lines.free;
        IF assigned(columnInfo) THEN columnInfo.free;
    END;
END;


Procedure TUniMainModule.mesajGoster(baslik:string;mesajicerik:string;sure:integer=2500;mesajTipi:string='ok');
var mtip:TAlertType;
begin
  if mesajtipi='ok' then mtip:=atSuccess;
  if mesajtipi='uyari' then mtip:=atWarning;
  if mesajtipi='hata' then mtip:=atError;
  if mesajtipi='bilgi' then mtip:=atInfo;

              UniSweetAlert1.ShowConfirmButton:=false;
              UniSweetAlert1.Title:= baslik;
              UniSweetAlert1.AlertType:= mtip;
              UniSweetAlert1.TimerMS:= sure;
              UniSweetAlert1.Show(mesajicerik);
end;

procedure TUniMainModule.tblPersonelNewRecord(DataSet: TDataSet);
begin
  dataset.FieldByName('LABORATUVAR_PERSONELI').AsInteger:=1;
end;

Procedure TUniMainModule.logKaydet(ISLEM:string;ISLEMI_YAPAN:string;ONCEKI_DEGER:string;numuneid:integer);
begin
  with tblLog do
    begin
      open;
      insert;
      fieldbyname('islem').AsString:= islem;
      fieldbyname('ISLEMI_YAPAN').AsString:= ISLEMI_YAPAN;
      fieldbyname('ONCEKI_DEGER').AsString:= ONCEKI_DEGER;
      fieldbyname('numune_id').asinteger  := numuneid;
      post;
    end;
end;


Procedure TUniMainModule.ComboDoldur(tabloadi:string;sahip:integer;combo:TUniDBComboBox);
var
  dataset:TpFIBDataSet;
begin
try
  dataset:=TpFIBDataSet.Create(self);
  dataset.Database:= unimainmodule.db;
  dataset.Transaction:=unimainmodule.tr;
  dataset.SelectSql.clear;
  dataset.SelectSql.add('select * from '+tabloadi+' where sahibi= ' + inttostr(sahip));
  dataset.open;

  combo.items.clear;
  with dataset do
    begin
      first;
      while not eof do
        begin
          combo.Items.Add(dataset.FieldByName('izahat').AsString);
          next;
        end;
    end;
finally
  freeandnil(dataset);
end;
end;

Procedure TUniMainModule.ComboDoldur_duz(tabloadi:string;sahip:integer;combo:TUniComboBox);
var
  dataset:TpFIBDataSet;
begin
try
  dataset:=TpFIBDataSet.Create(self);
  dataset.Database:= unimainmodule.db;
  dataset.Transaction:=unimainmodule.tr;
  dataset.SelectSql.clear;
  dataset.SelectSql.add('select * from '+tabloadi+' where sahibi= ' + inttostr(sahip));
  dataset.open;

  combo.items.clear;
  with dataset do
    begin
      first;
      while not eof do
        begin
          combo.Items.Add(dataset.FieldByName('izahat').AsString);
          next;
        end;
    end;
finally
  freeandnil(dataset);
end;
end;





procedure Ayir (const Delimiter: Char; Input: string; const Strings: TStrings) ;
begin
   Assert(Assigned(Strings)) ;
   Strings.Clear;
   Strings.Delimiter := Delimiter;
   Strings.DelimitedText :=  '"' + StringReplace(Input, Delimiter, '"' + Delimiter + '"', [rfReplaceAll]) + '"' ;

end;

Function TUniMainModule.Sql_checklistBox(smetin:String):String;
var
  sorgumetni: string;
  k,stip: integer;
  liste_tip:tunimemo;
begin
    liste_tip:= tunimemo.Create(uniapplication);
    try
     ayir(';', smetin, liste_tip.Lines) ;

     for k:=0 to liste_tip.Lines.Count-1 do
     begin

       if sorgumetni <> '' then
          sorgumetni:= sorgumetni +',' +QuotedStr(liste_tip.lines[k])  else
          sorgumetni:= QuotedStr(liste_tip.lines[k]);
     end;
     result:= sorgumetni;

   finally
     liste_tip.Free;
   end;

end;


function TUniMainModule.Calculate_EAN13(Bar12Hane: string): string;
var
  tek_toplam,
    cift_toplam,
    tum_toplam,
    i: Integer;
begin
  tek_toplam := 0;
  cift_toplam := 0;
  tum_toplam := 0;
  for i := 1 to Length(Bar12Hane) do begin
    if i mod 2 <> 0
      then tek_toplam := tek_toplam + (StrToInt(Bar12Hane[i]) * 1)
    else cift_toplam := cift_toplam + (StrToInt(Bar12Hane[i]) * 3);
  end;
  tum_toplam := 10 - ((tek_toplam + cift_toplam) mod 10);
  if tum_toplam = 10 then tum_toplam := 0;
  Result := Format('%d', [tum_toplam]);
end;


initialization
  RegisterMainModuleClass(TUniMainModule);
end.

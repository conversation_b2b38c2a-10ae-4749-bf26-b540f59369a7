object UniServerModule: TUniServerModule
  OldCreateOrder = False
  TempFolder = 'temp\'
  SessionTimeout = 6000000
  Title = 'Laboratuvar Bilgi <PERSON>'
  AjaxTimeout = 300000
  SuppressErrors = []
  Bindings = <>
  MainFormDisplayMode = mfPage
  CustomFiles.Strings = (
    'files/CheckComboBox_Plugin.js'
    'files/PagingToolbarResizer.js')
  CustomCSS.Strings = (
    
      '                                                    #ext-gen1018' +
      ' {'
    ' background-image: url("files/11zemin4.jpg") !important;'
    ' background-size :cover; '
    '}'
    ''
    'a:link{'
    ' cursor: pointer;'
    '}'
    ''
    '.pasifrenk_1 {'
    '    border: 1px solid #BABABA !important;'
    '    background-color: #000000 !important;    '
    '    color: #000000;'
    '    -webkit-touch-callout: none;'
    '    -webkit-user-select: none;'
    '    -khtml-user-select: none;'
    '    -moz-user-select: none;'
    '    -moz-user-input: disabled;'
    '    -ms-user-select: none;  '
    '}'
    ''
    '.customWindow.x-mask {'
    '   opacity: 0.3;'
    '   background: rgb(0, 0, 0) none repeat scroll 0% 0%;'
    '}'
    ''
    '.x-mask {'
    '    background: none repeat scroll 0 0 #BADAED;'
    '    opacity: 0.7;'
    '}           '
    ''
    '.golge {'
    #9'-webkit-box-shadow: 0 4px 3px -3px #F2F2F2;'
    #9'   -moz-box-shadow: 0 4px 3px -3px #F2F2F2;'
    #9'        box-shadow: 0 4px 3px -3px #F2F2F2;'
    '}'
    '.golgekalin {'
    '-webkit-box-shadow: 0px 0px 25px 0px rgba(50, 50, 50, 0.95);'
    '-moz-box-shadow:    0px 0px 25px 0px rgba(50, 50, 50, 0.95);'
    'box-shadow:         0px 0px 25px 0px rgba(50, 50, 50, 0.95);'
    '}'
    '.golgeyeni {'
    #9'-webkit-box-shadow: 0 5px 3px -3px #F2F2F2;'
    #9'   -moz-box-shadow: 0 5px 3px -3px #F2F2F2;'
    #9'        box-shadow: 0 5px 3px -3px #F2F2F2;'
    '}'
    ''
    '.ux-boundlist-item-checkbox'
    '{'
    '    background-repeat: no-repeat;'
    '    background-color: transparent;'
    '    width: 13px;'
    '    height: 13px;'
    '    display: inline-block;'
    '    line-height: 13px;'
    '    background-image: url('#39'files/checkbox.gif'#39');'
    '    background-position: 0 0;'
    '}'
    '.x-boundlist-selected .ux-boundlist-item-checkbox'
    '{'
    '    background-position: 0 -13px;'
    '}'
    ''
    ''
    ''
    '.btnsignin {'
    ' -webkit-border-radius: 5px;'
    ' -moz-border-radius: 5px;'
    ' border-radius: 5px; '
    ' background-image:none    !important;'
    ' background-color:#51E2F5 !important;'
    ' -webkit-box-align: center;'
    ' -ms-flex-align: center;'
    ' align-items: center;'
    ' white-space: nowrap;'
    ' text-overflow: ellipsis;'
    ' text-align: center;'
    ' '
    ' font-family:font-family:Roboto,helvetica,courier,arial;'
    ' font-size: 12px;'
    ' font-weight:normal;'
    ' color:#1C2833;'
    ' '
    '}'
    ''
    ''
    '.btnsadeYesil{'
    ' background-color: #4ba614; '
    '}'
    ''
    '.btnYesil{'
    
      'border:1px solid #34740e; -webkit-border-radius: 3px; -moz-borde' +
      'r-radius: 3px;border-radius: 3px;font-size:12px;font-family:aria' +
      'l, helvetica, sans-serif; padding: 5px 5px 5px 5px; text-decorat' +
      'ion:none; display:inline-block;text-shadow: -1px -1px 0 rgba(0,0' +
      ',0,0.3);font-weight:bold; color: #FFFFFF;'
    
      ' background-color: #4ba614; background-image: -webkit-gradient(l' +
      'inear, left top, left bottom, from(#4ba614), to(#008c00));'
    
      ' background-image: -webkit-linear-gradient(top, #4ba614, #008c00' +
      ');'
    ' background-image: -moz-linear-gradient(top, #4ba614, #008c00);'
    ' background-image: -ms-linear-gradient(top, #4ba614, #008c00);'
    ' background-image: -o-linear-gradient(top, #4ba614, #008c00);'
    
      ' background-image: linear-gradient(to bottom, #4ba614, #008c00);' +
      'filter:progid:DXImageTransform.Microsoft.gradient(GradientType=0' +
      ',startColorstr=#4ba614, endColorstr=#008c00);'
    '}'
    ''
    '.btnYesil:hover{'
    ' border:1px solid #224b09;'
    
      ' background-color: #36780f; background-image: -webkit-gradient(l' +
      'inear, left top, left bottom, from(#36780f), to(#005900));'
    
      ' background-image: -webkit-linear-gradient(top, #36780f, #005900' +
      ');'
    ' background-image: -moz-linear-gradient(top, #36780f, #005900);'
    ' background-image: -ms-linear-gradient(top, #36780f, #005900);'
    ' background-image: -o-linear-gradient(top, #36780f, #005900);'
    
      ' background-image: linear-gradient(to bottom, #36780f, #005900);' +
      'filter:progid:DXImageTransform.Microsoft.gradient(GradientType=0' +
      ',startColorstr=#36780f, endColorstr=#005900);'
    '}'
    ''
    '.btnYesil2 {'
    ' background:#4CAF50 !important;'
    ' color:#fff !important;'
    ' -webkit-transition: background 0.5s ease-in-out;'
    ' transition: background 0.5s ease-in-out;'
    '}'
    '.btnYesil2:hover'
    '{'
    ' background:#008CBA !important;'
    ' -webkit-transition: background 0.5s ease-in-out;'
    ' transition: background 0.5s ease-in-out;'
    '}'
    ''
    ''
    '.btngri{'
    ' background:#666666 !important;'
    ' color:#fff !important;'
    ' -webkit-transition: background 0.5s ease-in-out;'
    ' transition: background 0.5s ease-in-out;'
    '  text-align: center;'
    '   text-overflow: ellipsis;'
    '    align-items: center;'
    '    '
    ' -webkit-border-radius: 2px;'
    ' -moz-border-radius: 2px;'
    ' border-radius: 2px;    '
    '}'
    ''
    '.btnmavimsiyeni{'
    ' background:#D4E6F1 !important;'
    ' color:#fff !important;'
    ' -webkit-transition: background 0.5s ease-in-out;'
    ' transition: background 0.5s ease-in-out;'
    '  text-align: center;'
    '   text-overflow: ellipsis;'
    '    align-items: center;'
    '    '
    ' -webkit-border-radius: 2px;'
    ' -moz-border-radius: 2px;'
    ' border-radius: 2px;    '
    '}'
    ''
    ''
    '.btnTruncuyeni{'
    ' background:#ff8000 !important;'
    ' color:#fff !important;'
    ' -webkit-transition: background 0.5s ease-in-out;'
    ' transition: background 0.5s ease-in-out;'
    '  text-align: center;'
    '   text-overflow: ellipsis;'
    '    align-items: center;'
    '    '
    ' -webkit-border-radius: 2px;'
    ' -moz-border-radius: 2px;'
    ' border-radius: 2px;    '
    '}'
    ''
    ''
    '.btnyesilyeni{'
    ' background:#4d9900 !important;'
    ' color:#fff !important;'
    ' -webkit-transition: background 0.5s ease-in-out;'
    ' transition: background 0.5s ease-in-out;'
    '  text-align: center;'
    '   text-overflow: ellipsis;'
    '    align-items: center;'
    '    '
    '    '
    ' -webkit-border-radius: 2px;'
    ' -moz-border-radius: 2px;'
    ' border-radius: 2px;        '
    '}'
    ''
    '.btnkirmizimsi{'
    ' background:#ff0000 !important;'
    ' color:#fff !important;'
    ' -webkit-transition: background 0.5s ease-in-out;'
    ' transition: background 0.5s ease-in-out;'
    '  text-align: center;'
    '   text-overflow: ellipsis;'
    '    align-items: center;'
    '    '
    '    '
    ' -webkit-border-radius: 2px;'
    ' -moz-border-radius: 2px;'
    ' border-radius: 2px;        '
    '}'
    ''
    '.btnmavimsi{'
    ' background:#6a5acd !important;'
    ' color:#fff !important;'
    ' -webkit-transition: background 0.5s ease-in-out;'
    ' transition: background 0.5s ease-in-out;'
    '  text-align: center;'
    '   text-overflow: ellipsis;'
    '    align-items: center;'
    '    '
    '    '
    ' -webkit-border-radius: 2px;'
    ' -moz-border-radius: 2px;'
    ' border-radius: 2px;        '
    '}'
    ''
    ''
    '.btngriyuvarlak{'
    ' background:#00FCF8F9  !important;'
    ' color:#000 !important;'
    ' -webkit-transition: background 0.5s ease-in-out;'
    ' transition: background 0.5s ease-in-out;'
    '  text-align: center;'
    '   text-overflow: ellipsis;'
    '    align-items: center;'
    '    '
    '    '
    ' -webkit-border-radius: 3px;'
    ' -moz-border-radius: 3px;'
    ' border-radius: 3px;        '
    '}'
    ''
    '.btnYesilyuvarlak{'
    ' background:#739900 !important;'
    ' color:#fff !important;'
    ' -webkit-transition: background 0.5s ease-in-out;'
    ' transition: background 0.5s ease-in-out;'
    '  text-align: center;'
    '   text-overflow: ellipsis;'
    '    align-items: center;'
    '    '
    '    '
    ' -webkit-border-radius: 3px;'
    ' -moz-border-radius: 3px;'
    ' border-radius: 3px;        '
    '}'
    ''
    ''
    '.btnGri1yuvarlak{'
    ' background:#76D7C4 !important;'
    ' color:#000 !important;'
    ' -webkit-transition: background 0.5s ease-in-out;'
    ' transition: background 0.5s ease-in-out;'
    '  text-align: center;'
    '   text-overflow: ellipsis;'
    '    align-items: center;'
    '    '
    '    '
    ' -webkit-border-radius: 3px;'
    ' -moz-border-radius: 3px;'
    ' border-radius: 3px;        '
    '}'
    ''
    ''
    '.btnacikgri{'
    ' background:#f0f0f0 !important;'
    ' color:#fff !important;'
    ' -webkit-transition: background 0.5s ease-in-out;'
    ' transition: background 0.5s ease-in-out;'
    '  text-align: center;'
    '   text-overflow: ellipsis;'
    '    align-items: center;'
    '}'
    ''
    ''
    '.mobile_button'
    '{'
    ' font-family:font-family:Roboto,helvetica,courier,arial;'
    ' font-size: 17px;'
    ' font-weight:normal;'
    ' color:#ffffff;'
    ' -webkit-box-flex: 1;'
    ' -ms-flex: 1 0 auto;'
    ' flex: 1 0 auto;'
    ' -webkit-box-align: center;'
    ' -ms-flex-align: center;'
    ' align-items: center;'
    ' white-space: nowrap;'
    ' text-overflow: ellipsis;'
    ' text-align: center;'
    ' display: block;'
    ' overflow: hidden;'
    ' background-image:none    !important;'
    ' background-color:transparent !important;'
    '}'
    ''
    ''
    '.frmLogin .x-window-body'
    '{'
    ''
    '}'
    '.frmloginsemi'
    '{'
    ' background-color: #ffffff !important;'
    ' -webkit-border-radius: 15px;'
    ' -moz-border-radius: 15px;'
    ' border-radius: 15px;'
    ' border: 0px solid #7d7d7d !important;'
    '}'
    ''
    ''
    '._x-item-disabled .x-form-field {'
    '    opacity: 1;'
    '    -moz-opacity: 1;'
    '    filter: alpha(opacity=100);'
    '    color: black;'
    '   '
    '}'
    ''
    '.kls-grid .x-grid-header-ct{'
    'background-color: #F7F7F7;'
    'background-image:initial;'
    'border:initial;'
    'border-bottom-width: 1px !important;'
    'border-bottom-color: #E4E4E4 !important;'
    'border-bottom: 1px solid #E4E4E4 !important;'
    'height:60px;'
    '}'
    ''
    '.pencere_rengigenel .x-window{'
    '    background: #FF2F35;'
    '    border-color:#FF2F35;'
    '}'
    '.pencere_rengi .x-window-header {'
    '    background: #FF2F35;'
    '    border-color:#FF2F35;'
    '}'
    '')
  ServerMessages.UnavailableErrMsg = 'Ba'#287'lant'#305' Hatas'#305' Olu'#351'tu'
  ServerMessages.LoadingMessage = 'Y'#252'kleniyor...'
  ServerMessages.ExceptionTemplate.Strings = (
    '<html>'
    '<body bgcolor="#dfe8f6">'
    
      '<p style="text-align:center;color:#A05050">An Exception has occu' +
      'red in application:</p>'
    '<p style="text-align:center;color:#0000A0">[###message###]</p>'
    
      '<p style="text-align:center;color:#A05050"><a href="[###url###]"' +
      '>Yeniden Ba'#351'lat</a></p>'
    '</body>'
    '</html>')
  ServerMessages.InvalidSessionTemplate.Strings = (
    '<html>'
    '<body bgcolor="#dfe8f6">'
    '<p style="text-align:center;color:#0000A0">[###message###]</p>'
    
      '<p style="text-align:center;color:#A05050"><a href="[###url###]"' +
      '>Yeniden Ba'#351'lat</a></p>'
    '</body>'
    '</html>')
  ServerMessages.TerminateTemplate.Strings = (
    '<html>'
    '<body bgcolor="#dfe8f6">'
    '<p style="text-align:center;color:#0000A0">[###message###]</p>'
    
      '<p style="text-align:center;color:#A05050"><a href="[###url###]"' +
      '>Yeniden Ba'#351'lat</a></p>'
    '</body>'
    '</html>')
  ServerMessages.InvalidSessionMessage = 'Oturum Zaman A'#351#305'm'#305'na U'#287'rad'#305
  ServerMessages.TerminateMessage = 'Oturum Sonland'#305'r'#305'ld'#305
  SSL.SSLOptions.RootCertFile = 'root.pem'
  SSL.SSLOptions.CertFile = 'cert.pem'
  SSL.SSLOptions.KeyFile = 'key.pem'
  SSL.SSLOptions.Method = sslvTLSv1_1
  SSL.SSLOptions.SSLVersions = [sslvTLSv1_1]
  SSL.SSLOptions.Mode = sslmUnassigned
  SSL.SSLOptions.VerifyMode = []
  SSL.SSLOptions.VerifyDepth = 0
  ConnectionFailureRecovery.ErrorMessage = 'Connection Error'
  ConnectionFailureRecovery.RetryMessage = 'Retrying...'
  Height = 383
  Width = 643
end

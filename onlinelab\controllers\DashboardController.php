<?php
/**
 * Dashboard Controller Sınıfı
 */

class DashboardController {
    private $numuneModel;
    private $userModel;
    private $urunModel;
    private $birimModel;
    
    public function __construct() {
        AuthController::checkSession();
        
        $this->numuneModel = new Numune();
        $this->userModel = new User();
        $this->urunModel = new Urun();
        $this->birimModel = new LaboratuvarBirim();
    }
    
    public function index() {
        // Dashboard istatistiklerini al
        $stats = $this->getDashboardStats();
        
        // Son numuneleri al
        $recentNumuneler = $this->getRecentNumuneler();
        
        // Bekleyen işleri al
        $pendingTasks = $this->getPendingTasks();
        
        // Grafik verileri
        $chartData = $this->getChartData();
        
        // View'e veri gönder
        $data = [
            'stats' => $stats,
            'recent_numuneler' => $recentNumuneler,
            'pending_tasks' => $pendingTasks,
            'chart_data' => $chartData,
            'user' => AuthController::getCurrentUser()
        ];
        
        require_once 'views/dashboard.php';
    }
    
    private function getDashboardStats() {
        $stats = $this->numuneModel->getDashboardStats();
        
        // Ek istatistikler
        $stats['aktif_kullanici'] = $this->userModel->getCount('aktif = 1');
        $stats['toplam_urun'] = $this->urunModel->getCount('aktif = 1');
        $stats['toplam_birim'] = $this->birimModel->getCount('aktif = 1');
        
        // Bu hafta gelen numuneler
        $stats['bu_hafta'] = $this->numuneModel->getCount(
            'silindi = 0 AND WEEK(gelis_tarihi) = WEEK(CURRENT_DATE()) AND YEAR(gelis_tarihi) = YEAR(CURRENT_DATE())'
        );
        
        // Bugün gelen numuneler
        $stats['bugun'] = $this->numuneModel->getCount(
            'silindi = 0 AND DATE(gelis_tarihi) = CURRENT_DATE()'
        );
        
        return $stats;
    }
    
    private function getRecentNumuneler($limit = 10) {
        $sql = "SELECT n.*, 
                       u.urunadi_t as urun_adi,
                       g.izahat as firma_adi
                FROM lab_numuneler n
                LEFT JOIN urunler u ON n.urun_id = u.id
                LEFT JOIN geneltanim g ON n.firma_id = g.id
                WHERE n.silindi = 0
                ORDER BY n.gelis_tarihi DESC
                LIMIT ?";
        
        return $this->numuneModel->fetchAll($sql, [$limit]);
    }
    
    private function getPendingTasks() {
        $user = AuthController::getCurrentUser();
        $tasks = [];

        // Kullanıcı bilgisi yoksa boş array döndür
        if (!$user || !isset($user['permissions'])) {
            return $tasks;
        }

        // Numune kabul bekleyenler
        if (isset($user['permissions']['NUMUNE_KABUL_YAPABILIR']) && $user['permissions']['NUMUNE_KABUL_YAPABILIR']) {
            $count = $this->numuneModel->getCount("durumu = 'Numune Kabul' AND silindi = 0");
            if ($count > 0) {
                $tasks[] = [
                    'title' => 'Kabul Bekleyen Numuneler',
                    'count' => $count,
                    'url' => 'index.php?page=numune-listesi&filter=kabul',
                    'icon' => 'fa-inbox',
                    'color' => 'warning'
                ];
            }
        }

        // Ön hazırlık bekleyenler
        if (isset($user['permissions']['NUMUNE_ONHAZIRLIK_YAPABILIR']) && $user['permissions']['NUMUNE_ONHAZIRLIK_YAPABILIR']) {
            $count = $this->numuneModel->getCount("durumu = 'Ön Hazırlık' AND silindi = 0");
            if ($count > 0) {
                $tasks[] = [
                    'title' => 'Ön Hazırlık Bekleyen Numuneler',
                    'count' => $count,
                    'url' => 'index.php?page=onhazirlik',
                    'icon' => 'fa-cogs',
                    'color' => 'info'
                ];
            }
        }

        // Analiz bekleyenler
        if (isset($user['permissions']['NUMUNE_SONUC_GIREBILIR']) && $user['permissions']['NUMUNE_SONUC_GIREBILIR']) {
            $count = $this->numuneModel->getCount("durumu = 'Analiz Aşamasında' AND silindi = 0");
            if ($count > 0) {
                $tasks[] = [
                    'title' => 'Analiz Bekleyen Numuneler',
                    'count' => $count,
                    'url' => 'index.php?page=numune-listesi&filter=analiz',
                    'icon' => 'fa-flask',
                    'color' => 'primary'
                ];
            }
        }

        return $tasks;
    }
    
    private function getChartData() {
        $data = [];
        
        // Son 7 günün numune sayıları
        $data['daily_counts'] = [];
        for ($i = 6; $i >= 0; $i--) {
            $date = date('Y-m-d', strtotime("-$i days"));
            $count = $this->numuneModel->getCount(
                'silindi = 0 AND DATE(gelis_tarihi) = ?', 
                [$date]
            );
            $data['daily_counts'][] = [
                'date' => date('d.m', strtotime($date)),
                'count' => $count
            ];
        }
        
        // Duruma göre dağılım
        $data['status_distribution'] = [];
        foreach (NUMUNE_DURUMLARI as $durum) {
            $count = $this->numuneModel->getCount('silindi = 0 AND durumu = ?', [$durum]);
            if ($count > 0) {
                $data['status_distribution'][] = [
                    'label' => $durum,
                    'value' => $count
                ];
            }
        }
        
        // Türe göre dağılım
        $data['type_distribution'] = [];
        foreach (NUMUNE_TURLERI as $tur) {
            $count = $this->numuneModel->getCount('silindi = 0 AND numune_turu = ?', [$tur]);
            if ($count > 0) {
                $data['type_distribution'][] = [
                    'label' => $tur,
                    'value' => $count
                ];
            }
        }
        
        // En çok kullanılan ürünler
        $data['top_products'] = $this->urunModel->getMostUsedUrunler(5);
        
        // Birim iş yükü
        $data['birim_workload'] = [];
        $birimler = $this->birimModel->getActiveBirimler();
        foreach ($birimler as $birim) {
            $workload = $this->birimModel->getWorkload($birim['id']);
            if ($workload > 0) {
                $data['birim_workload'][] = [
                    'birim' => $birim['birimadi'],
                    'workload' => $workload
                ];
            }
        }
        
        return $data;
    }
    
    // AJAX endpoint'leri
    public function getStatsAjax() {
        header('Content-Type: application/json');
        echo json_encode($this->getDashboardStats());
        exit;
    }
    
    public function getChartDataAjax() {
        header('Content-Type: application/json');
        echo json_encode($this->getChartData());
        exit;
    }
}
?>

﻿	<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
		<PropertyGroup>
			<ProjectGuid>{2923F95D-A784-4E30-9867-83D85A742509}</ProjectGuid>
			<ProjectVersion>13.4</ProjectVersion>
			<FrameworkType>VCL</FrameworkType>
			<MainSource>Laboratuvar.dpr</MainSource>
			<Base>True</Base>
			<Config Condition="'$(Config)'==''">Release</Config>
			<Platform Condition="'$(Platform)'==''">Win32</Platform>
			<TargetedPlatforms>1</TargetedPlatforms>
			<AppType>Application</AppType>
		</PropertyGroup>
		<PropertyGroup Condition="'$(Config)'=='Base' or '$(Base)'!=''">
			<Base>true</Base>
		</PropertyGroup>
		<PropertyGroup Condition="('$(Platform)'=='Win64' and '$(Base)'=='true') or '$(Base_Win64)'!=''">
			<Base_Win64>true</Base_Win64>
			<CfgParent>Base</CfgParent>
			<Base>true</Base>
		</PropertyGroup>
		<PropertyGroup Condition="('$(Platform)'=='Win32' and '$(Base)'=='true') or '$(Base_Win32)'!=''">
			<Base_Win32>true</Base_Win32>
			<CfgParent>Base</CfgParent>
			<Base>true</Base>
		</PropertyGroup>
		<PropertyGroup Condition="'$(Config)'=='Debug' or '$(Cfg_1)'!=''">
			<Cfg_1>true</Cfg_1>
			<CfgParent>Base</CfgParent>
			<Base>true</Base>
		</PropertyGroup>
		<PropertyGroup Condition="('$(Platform)'=='Win32' and '$(Cfg_1)'=='true') or '$(Cfg_1_Win32)'!=''">
			<Cfg_1_Win32>true</Cfg_1_Win32>
			<CfgParent>Cfg_1</CfgParent>
			<Cfg_1>true</Cfg_1>
			<Base>true</Base>
		</PropertyGroup>
		<PropertyGroup Condition="'$(Config)'=='Release' or '$(Cfg_2)'!=''">
			<Cfg_2>true</Cfg_2>
			<CfgParent>Base</CfgParent>
			<Base>true</Base>
		</PropertyGroup>
		<PropertyGroup Condition="('$(Platform)'=='Win32' and '$(Cfg_2)'=='true') or '$(Cfg_2_Win32)'!=''">
			<Cfg_2_Win32>true</Cfg_2_Win32>
			<CfgParent>Cfg_2</CfgParent>
			<Cfg_2>true</Cfg_2>
			<Base>true</Base>
		</PropertyGroup>
		<PropertyGroup Condition="'$(Base)'!=''">
			<GenDll>true</GenDll>
			<DCC_Namespace>System;Xml;Data;Datasnap;Web;Soap;Vcl;Vcl.Imaging;Vcl.Touch;Vcl.Samples;Vcl.Shell;$(DCC_Namespace)</DCC_Namespace>
			<Icon_MainIcon>$(BDS)\bin\delphi_PROJECTICON.ico</Icon_MainIcon>
			<DCC_UsePackage>DBXInterBaseDriver;DbxCommonDriver;dbxcds;CustomIPTransport;dsnap;IndyCore;bindcompfmx;dbrtl;bindcomp;inetdb;xmlrtl;ibxpress;soaprtl;bindengine;FIBPlus_XE2;inet;dbexpress;fmx;IndySystem;sgcWebSocketsDXE2;fmxase;inetdbxpress;rtl;DbxClientDriver;IndyProtocols;DBXMySQLDriver;fmxobj;fmxdae;$(DCC_UsePackage)</DCC_UsePackage>
			<DCC_DcuOutput>.\$(Platform)\$(Config)</DCC_DcuOutput>
			<DCC_ExeOutput>.\$(Platform)\$(Config)</DCC_ExeOutput>
		</PropertyGroup>
		<PropertyGroup Condition="'$(Base_Win64)'!=''">
			<DCC_UsePackage>cxLibraryRS16;dxSkinSevenRS16;office2010rt;uniGUI16;cxPivotGridOLAPRS16;dxSkinSummer2008RS16;cxPageControlRS16;dxSkinVS2010RS16;vclimg;dxSkinDevExpressStyleRS16;dxSkinWhiteprintRS16;dxComnRS16;uniTools16;vcldb;dxSkinBlackRS16;dxADOServerModeRS16;dxBarExtDBItemsRS16;uniGUI16Chart;dxSkinXmas2008BlueRS16;dxSkinOffice2007BlueRS16;dxSkinOffice2007GreenRS16;cxDataRS16;cxBarEditItemRS16;dxSkinMetropolisRS16;dxDockingRS16;dxBarExtItemsRS16;uSynEdit_R2012;cxVerticalGridRS16;dxSkinSharpRS16;dxSkinOffice2007BlackRS16;dxSkinBlueprintRS16;dxWizardControlRS16;dxNavBarRS16;cxSchedulerTreeBrowserRS16;dxdbtrRS16;dxSkinFoggyRS16;dxSkinDarkSideRS16;dxSkinscxPCPainterRS16;vclactnband;dxServerModeRS16;bindcompvcl;vclie;dxSkinSilverRS16;dsnapcon;dxSkinOffice2013WhiteRS16;dcldxSkinsCoreRS16;dxSkinLilianRS16;vclx;dxSkinValentineRS16;dxGDIPlusRS16;dxSkinSharpPlusRS16;dxLayoutControlRS16;dxSpreadSheetRS16;dxCoreRS16;cxExportRS16;dxBarRS16;dxSkinsdxNavBarPainterRS16;dxSkinCoffeeRS16;cxTreeListdxBarPopupMenuRS16;TeeDB;dxDBXServerModeRS16;vclib;dxSkinOffice2013DarkGrayRS16;dxRibbonRS16;cxTreeListRS16;dxSkinOffice2007SilverRS16;dxSkinsdxRibbonPainterRS16;vcldsnap;dxGaugeControlRS16;dxSkinBlueRS16;dxSkinDarkRoomRS16;dxSkinscxSchedulerPainterRS16;vcl;dxSkinDevExpressDarkStyleRS16;webdsnap;dxSkinsdxDLPainterRS16;dxSkinTheAsphaltWorldRS16;dxSkinOffice2010BlackRS16;dxSkinMoneyTwinsRS16;uniGUI16VCL;dxSkinPumpkinRS16;adortl;dxSkinHighContrastRS16;dxSkinOffice2013LightGrayRS16;dxSkiniMaginaryRS16;dxSkinLondonLiquidSkyRS16;dxSkinsdxBarPainterRS16;Tee;dxSkinGlassOceansRS16;dxSkinLiquidSkyRS16;dxSkinsCoreRS16;dxmdsRS16;cxGridRS16;cxEditorsRS16;TeeUI;cxPivotGridRS16;dxSkinSevenClassicRS16;cxSchedulerRibbonStyleEventEditorRS16;cxSchedulerRS16;vcltouch;dxSkinOffice2010SilverRS16;websnap;uniGUI16m;dxSkinOffice2007PinkRS16;VclSmp;dxSkinSpringTimeRS16;dxTabbedMDIRS16;dxSkinStardustRS16;uIndy16;dxSkinMetropolisDarkRS16;dxSkinOffice2010BlueRS16;dxThemeRS16;dxSkinCaramelRS16;uniGUI16Core;dxSkinMcSkinRS16;dxBarDBNavRS16;$(DCC_UsePackage)</DCC_UsePackage>
		</PropertyGroup>
		<PropertyGroup Condition="'$(Base_Win32)'!=''">
			<DCC_UsePackage>fsADO16;dxTileControlRS16;cxLibraryRS16;dxSkinSevenRS16;office2010rt;Notification;FlexCelXE;clinetsuite_xe2;uniGUI16;frxBDE16;cxPivotGridOLAPRS16;dxSkinSummer2008RS16;cxPageControlRS16;fsIBX16;dxSkinVS2010RS16;vclimg;dxSkinDevExpressStyleRS16;dxSkinWhiteprintRS16;fmi;dxComnRS16;uniTools16;vcldb;dxSkinBlackRS16;dxADOServerModeRS16;dxBarExtDBItemsRS16;LockBox;uniGUI16Chart;dxSkinXmas2008BlueRS16;dxSkinOffice2007BlueRS16;NativeExcelD2011;CloudService;dxSkinOffice2007GreenRS16;FmxTeeUI;cxDataRS16;cxBarEditItemRS16;dxSkinMetropolisRS16;dxDockingRS16;dxBarExtItemsRS16;A407_R;Rol_getir;uSynEdit_R2012;cxVerticalGridRS16;dclExcelExportPackXE2;dxSkinSharpRS16;dxSkinOffice2007BlackRS16;UniGMapDXE2;dxSkinBlueprintRS16;dxWizardControlRS16;vcldbx;dxNavBarRS16;cxSchedulerTreeBrowserRS16;HHBMailComponent;dxdbtrRS16;dxSkinFoggyRS16;fsBDE16;frxDB16;intrawebdb_120_160;dxSkinDarkSideRS16;dxSkinscxPCPainterRS16;fs16;vclactnband;FMXTee;dxServerModeRS16;bindcompvcl;frxcs16;vclie;combobox_yeni;dxSkinSilverRS16;dsnapcon;dxSkinOffice2013WhiteRS16;dcldxSkinsCoreRS16;dxSkinLilianRS16;vclx;dxSkinValentineRS16;frxIBX16;dxGDIPlusRS16;EurekaLogCore;dxSkinSharpPlusRS16;pdf_akrobat;dxLayoutControlRS16;dxSpreadSheetRS16;dxCoreRS16;KendoChart;cxExportRS16;ZComponent;dxBarRS16;dxSkinsdxNavBarPainterRS16;UniMobileX;dxSkinCoffeeRS16;cxTreeListdxBarPopupMenuRS16;TeeDB;dxDBXServerModeRS16;frxTee16;vclib;inetdbbde;dxSkinOffice2013DarkGrayRS16;dxRibbonRS16;acrobat;cxTreeListRS16;wreg400_D7;dxSkinOffice2007SilverRS16;dxSkinsdxRibbonPainterRS16;vcldsnap;dxGaugeControlRS16;dxSkinBlueRS16;Intraweb_120_160;frxADO16;vclribbon;frxe16;dxSkinDarkRoomRS16;dxSkinscxSchedulerPainterRS16;vcl;dxSkinDevExpressDarkStyleRS16;CodeSiteExpressPkg;fsTee16;cxSchedulerGridRS16;webdsnap;dxSkinsdxDLPainterRS16;dxtrmdRS16;dxSkinTheAsphaltWorldRS16;dxSkinOffice2010BlackRS16;dxSkinMoneyTwinsRS16;uniGUI16VCL;dxSkinPumpkinRS16;adortl;dxSkinHighContrastRS16;frxDBX16;dxSkinOffice2013LightGrayRS16;dxSkiniMaginaryRS16;ZDbc;frx16;dxSkinLondonLiquidSkyRS16;dxSkinsdxBarPainterRS16;ZPlain;Tee;dxSkinGlassOceansRS16;dxSkinLiquidSkyRS16;dxSkinsCoreRS16;svnui;cxPivotGridChartRS16;dxmdsRS16;cxGridRS16;cxEditorsRS16;TeeUI;cxPivotGridRS16;dxSkinSevenClassicRS16;UniGuiDxPackageXE2;cxSchedulerRibbonStyleEventEditorRS16;cxSchedulerRS16;ZCore;vcltouch;dxSkinOffice2010SilverRS16;websnap;uniGUI16m;dxSkinOffice2007PinkRS16;VclSmp;dxSkinSpringTimeRS16;dxTabbedMDIRS16;fsDB16;dxSkinStardustRS16;uIndy16;dxSkinMetropolisDarkRS16;dxSkinOffice2010BlueRS16;dxThemeRS16;ZParseSql;svn;dxSkinCaramelRS16;uniGUI16Core;bdertl;dxSkinMcSkinRS16;dxBarDBNavRS16;$(DCC_UsePackage)</DCC_UsePackage>
			<VerInfo_IncludeVerInfo>true</VerInfo_IncludeVerInfo>
			<DCC_Namespace>Winapi;System.Win;Data.Win;Datasnap.Win;Web.Win;Soap.Win;Xml.Win;Bde;$(DCC_Namespace)</DCC_Namespace>
			<VerInfo_Locale>1033</VerInfo_Locale>
			<Manifest_File>$(BDS)\bin\default_app.manifest</Manifest_File>
			<VerInfo_Keys>CompanyName=;FileDescription=;FileVersion=*******;InternalName=;LegalCopyright=;LegalTrademarks=;OriginalFilename=;ProductName=;ProductVersion=*******;Comments=</VerInfo_Keys>
		</PropertyGroup>
		<PropertyGroup Condition="'$(Cfg_1)'!=''">
			<DCC_Define>DEBUG;$(DCC_Define)</DCC_Define>
			<DCC_Optimize>false</DCC_Optimize>
			<DCC_GenerateStackFrames>true</DCC_GenerateStackFrames>
			<DCC_DebugInfoInExe>true</DCC_DebugInfoInExe>
			<DCC_RemoteDebug>true</DCC_RemoteDebug>
		</PropertyGroup>
		<PropertyGroup Condition="'$(Cfg_1_Win32)'!=''">
			<DCC_RemoteDebug>false</DCC_RemoteDebug>
		</PropertyGroup>
		<PropertyGroup Condition="'$(Cfg_2)'!=''">
			<DCC_LocalDebugSymbols>false</DCC_LocalDebugSymbols>
			<DCC_Define>RELEASE;$(DCC_Define)</DCC_Define>
			<DCC_SymbolReferenceInfo>0</DCC_SymbolReferenceInfo>
			<DCC_DebugInformation>false</DCC_DebugInformation>
		</PropertyGroup>
		<PropertyGroup Condition="'$(Cfg_2_Win32)'!=''">
			<Icon_MainIcon>Laboratuvar_Icon3.ico</Icon_MainIcon>
			<VerInfo_IncludeVerInfo>true</VerInfo_IncludeVerInfo>
			<VerInfo_Locale>1033</VerInfo_Locale>
		</PropertyGroup>
		<ItemGroup>
			<DelphiCompile Include="$(MainSource)">
				<MainSource>MainSource</MainSource>
			</DelphiCompile>
			<DCCReference Include="ServerModule.pas">
				<Form>UniServerModule</Form>
				<DesignClass>TUniGUIServerModule</DesignClass>
			</DCCReference>
			<DCCReference Include="MainModule.pas">
				<Form>UniMainModule</Form>
				<DesignClass>TUniGUIMainModule</DesignClass>
			</DCCReference>
			<DCCReference Include="Main.pas">
				<Form>MainForm</Form>
				<DesignClass>TUniForm</DesignClass>
			</DCCReference>
			<DCCReference Include="NumuneListesi.pas">
				<Form>frmNumuneListesi</Form>
				<DesignClass>TUniForm</DesignClass>
			</DCCReference>
			<DCCReference Include="NumuneKabul.pas">
				<Form>frmNumuneKabul</Form>
				<DesignClass>TUniForm</DesignClass>
			</DCCReference>
			<DCCReference Include="uGenelTanim.pas">
				<Form>frmGenelTanim</Form>
				<DesignClass>TUniForm</DesignClass>
			</DCCReference>
			<DCCReference Include="urunler.pas">
				<Form>frmUrunler</Form>
				<DesignClass>TUniForm</DesignClass>
			</DCCReference>
			<DCCReference Include="uTanimBirimler.pas">
				<Form>frmTanimBirimler</Form>
				<DesignClass>TUniForm</DesignClass>
			</DCCReference>
			<DCCReference Include="uTanimBirimEkle.pas">
				<Form>frmTanimBirimEkle</Form>
				<DesignClass>TUniForm</DesignClass>
			</DCCReference>
			<DCCReference Include="uTanimPersonel.pas">
				<Form>frmTanimPersonel</Form>
				<DesignClass>TUniForm</DesignClass>
			</DCCReference>
			<DCCReference Include="uTanimPersonelEkle.pas">
				<Form>frmTanimPersonelEkle</Form>
				<DesignClass>TUniForm</DesignClass>
			</DCCReference>
			<DCCReference Include="BirimlereGonder.pas">
				<Form>frmBirimlereGonder</Form>
				<DesignClass>TUniForm</DesignClass>
			</DCCReference>
			<DCCReference Include="Onhazirlik.pas">
				<Form>frmOnhazirlik</Form>
				<DesignClass>TUniForm</DesignClass>
			</DCCReference>
			<DCCReference Include="NumuneSonuc.pas">
				<Form>frmNumuneSonuc</Form>
				<DesignClass>TUniForm</DesignClass>
			</DCCReference>
			<DCCReference Include="NumuneSonucGir.pas">
				<Form>frmNumuneSonucGir</Form>
				<DesignClass>TUniForm</DesignClass>
			</DCCReference>
			<DCCReference Include="uLaboratuvarBirimler.pas">
				<Form>frmLaboratuvarBirimler</Form>
				<DesignClass>TUniForm</DesignClass>
			</DCCReference>
			<DCCReference Include="uKullanicilar.pas">
				<Form>frmKullanicilar</Form>
				<DesignClass>TUniForm</DesignClass>
			</DCCReference>
			<DCCReference Include="uKullaniciEkle.pas">
				<Form>frmKullaniciEkle</Form>
				<DesignClass>TUniForm</DesignClass>
			</DCCReference>
			<DCCReference Include="login.pas">
				<Form>UniLoginForm1</Form>
				<DesignClass>TUniLoginForm</DesignClass>
			</DCCReference>
			<DCCReference Include="urapor.pas">
				<Form>frmRapor</Form>
				<DesignClass>TUniForm</DesignClass>
			</DCCReference>
			<DCCReference Include="log.pas">
				<Form>frmLog</Form>
				<DesignClass>TUniForm</DesignClass>
			</DCCReference>
			<DCCReference Include="dashboard.pas">
				<Form>frmDashboard</Form>
				<DesignClass>TUniForm</DesignClass>
			</DCCReference>
			<DCCReference Include="uUlkeler.pas">
				<Form>FrmUlkeler</Form>
				<DesignClass>TUniForm</DesignClass>
			</DCCReference>
			<DCCReference Include="etmen_tanim.pas">
				<Form>frmEtmenTanim</Form>
				<DesignClass>TUniForm</DesignClass>
			</DCCReference>
			<DCCReference Include="donersermayebaglanti.pas">
				<Form>frmDonersermayebaglanti</Form>
				<FormType>dfm</FormType>
				<DesignClass>TUniForm</DesignClass>
			</DCCReference>
			<DCCReference Include="uKurumEvrak.pas">
				<Form>frmKurumEvrak</Form>
				<DesignClass>TUniForm</DesignClass>
			</DCCReference>
			<DCCReference Include="uKurumEvrakEkle.pas">
				<Form>frmKurumEvrakEkle</Form>
				<DesignClass>TUniForm</DesignClass>
			</DCCReference>
			<DCCReference Include="numunekalangir.pas">
				<Form>frmnumunekalangir</Form>
				<FormType>dfm</FormType>
				<DesignClass>TUniForm</DesignClass>
			</DCCReference>
			<BuildConfiguration Include="Release">
				<Key>Cfg_2</Key>
				<CfgParent>Base</CfgParent>
			</BuildConfiguration>
			<BuildConfiguration Include="Base">
				<Key>Base</Key>
			</BuildConfiguration>
			<BuildConfiguration Include="Debug">
				<Key>Cfg_1</Key>
				<CfgParent>Base</CfgParent>
			</BuildConfiguration>
		</ItemGroup>
		<ProjectExtensions>
			<Borland.Personality>Delphi.Personality.12</Borland.Personality>
			<Borland.ProjectType/>
			<BorlandProject>
				<Delphi.Personality>
					<VersionInfo>
						<VersionInfo Name="IncludeVerInfo">False</VersionInfo>
						<VersionInfo Name="AutoIncBuild">False</VersionInfo>
						<VersionInfo Name="MajorVer">1</VersionInfo>
						<VersionInfo Name="MinorVer">0</VersionInfo>
						<VersionInfo Name="Release">0</VersionInfo>
						<VersionInfo Name="Build">0</VersionInfo>
						<VersionInfo Name="Debug">False</VersionInfo>
						<VersionInfo Name="PreRelease">False</VersionInfo>
						<VersionInfo Name="Special">False</VersionInfo>
						<VersionInfo Name="Private">False</VersionInfo>
						<VersionInfo Name="DLL">False</VersionInfo>
						<VersionInfo Name="Locale">1055</VersionInfo>
						<VersionInfo Name="CodePage">1254</VersionInfo>
					</VersionInfo>
					<VersionInfoKeys>
						<VersionInfoKeys Name="CompanyName"/>
						<VersionInfoKeys Name="FileDescription"/>
						<VersionInfoKeys Name="FileVersion">*******</VersionInfoKeys>
						<VersionInfoKeys Name="InternalName"/>
						<VersionInfoKeys Name="LegalCopyright"/>
						<VersionInfoKeys Name="LegalTrademarks"/>
						<VersionInfoKeys Name="OriginalFilename"/>
						<VersionInfoKeys Name="ProductName"/>
						<VersionInfoKeys Name="ProductVersion">*******</VersionInfoKeys>
						<VersionInfoKeys Name="Comments"/>
					</VersionInfoKeys>
					<Excluded_Packages>
						<Excluded_Packages Name="$(BDSBIN)\dclIPIndyImpl160.bpl">IP Abstraction Indy Implementation Design Time</Excluded_Packages>
						<Excluded_Packages Name="E:\yuklu_programlar\EurekaLog 7\Packages\Studio16\EurekaLogComponent160.bpl">File E:\yuklu_programlar\EurekaLog 7\Packages\Studio16\EurekaLogComponent160.bpl not found</Excluded_Packages>
						<Excluded_Packages Name="$(BDSBIN)\dcloffice2k160.bpl">Microsoft Office 2000 Sample Automation Server Wrapper Components</Excluded_Packages>
						<Excluded_Packages Name="$(BDSBIN)\dclofficexp160.bpl">Microsoft Office XP Sample Automation Server Wrapper Components</Excluded_Packages>
					</Excluded_Packages>
					<Source>
						<Source Name="MainSource">Laboratuvar.dpr</Source>
					</Source>
				</Delphi.Personality>
				<Deployment/>
				<Platforms>
					<Platform value="Win64">False</Platform>
					<Platform value="OSX32">False</Platform>
					<Platform value="Win32">True</Platform>
				</Platforms>
			</BorlandProject>
			<ProjectFileVersion>12</ProjectFileVersion>
		</ProjectExtensions>
		<Import Condition="Exists('$(BDS)\Bin\CodeGear.Delphi.Targets')" Project="$(BDS)\Bin\CodeGear.Delphi.Targets"/>
		<Import Condition="Exists('$(APPDATA)\Embarcadero\$(BDSAPPDATABASEDIR)\$(PRODUCTVERSION)\UserTools.proj')" Project="$(APPDATA)\Embarcadero\$(BDSAPPDATABASEDIR)\$(PRODUCTVERSION)\UserTools.proj"/>
	</Project>

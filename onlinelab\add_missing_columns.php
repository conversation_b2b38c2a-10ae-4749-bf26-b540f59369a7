<?php
require_once 'config/database.php';

try {
    $db = new Database();
    
    echo "<h2><PERSON><PERSON><PERSON></h2>";
    
    $alterQueries = [
        "ALTER TABLE lab_numuneler ADD COLUMN ulke VARCHAR(100) AFTER mensei",
        "ALTER TABLE lab_numuneler ADD COLUMN numune_alindigiyer VARCHAR(255) AFTER ulke", 
        "ALTER TABLE lab_numuneler ADD COLUMN numune_sahibi VARCHAR(255) AFTER numune_alindigiyer",
        "ALTER TABLE lab_numuneler ADD COLUMN lotno VARCHAR(50) AFTER etiket_no",
        "ALTER TABLE lab_numuneler ADD COLUMN teslim_eden VARCHAR(100) AFTER birim",
        "ALTER TABLE lab_numuneler ADD COLUMN teslim_alan VARCHAR(100) AFTER teslim_eden",
        "ALTER TABLE lab_numuneler ADD COLUMN gelis_saati TIME AFTER gelis_tarihi"
    ];
    
    foreach ($alterQueries as $query) {
        try {
            $db->execute($query);
            echo "<div style='color: green;'>✅ Başarılı: " . htmlspecialchars($query) . "</div>";
        } catch (Exception $e) {
            if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
                echo "<div style='color: orange;'>⚠️ Zaten mevcut: " . htmlspecialchars($query) . "</div>";
            } else {
                echo "<div style='color: red;'>❌ Hata: " . htmlspecialchars($query) . "<br>Mesaj: " . $e->getMessage() . "</div>";
            }
        }
    }
    
    echo "<h3>Güncellenmiş Tablo Yapısı</h3>";
    $sql = "DESCRIBE lab_numuneler";
    $columns = $db->fetchAll($sql);
    
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr style='background: #f0f0f0;'><th>Alan Adı</th><th>Veri Tipi</th></tr>";
    
    foreach ($columns as $col) {
        echo "<tr>";
        echo "<td>{$col['Field']}</td>";
        echo "<td>{$col['Type']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<div style='margin-top: 20px; padding: 10px; background: #e8f5e8; border: 1px solid #4caf50;'>";
    echo "<strong>✅ Veritabanı güncelleme tamamlandı!</strong><br>";
    echo "Artık numune kaydetme işlemi çalışmalı.";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='color: red;'>Genel Hata: " . $e->getMessage() . "</div>";
}
?>

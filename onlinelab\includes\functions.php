<?php
/**
 * Utility Functions
 * PHP 8.1+ uyumlu yardımcı fonksiyonlar
 */

/**
 * Null-safe htmlspecialchars
 * @param mixed $string
 * @param int $flags
 * @param string $encoding
 * @param bool $double_encode
 * @return string
 */
function safe_html($string, $flags = ENT_QUOTES, $encoding = 'UTF-8', $double_encode = true) {
    return htmlspecialchars($string ?? '', $flags, $encoding, $double_encode);
}

/**
 * Null-safe number_format
 * @param mixed $number
 * @param int $decimals
 * @param string $decimal_separator
 * @param string $thousands_separator
 * @return string
 */
function safe_number($number, $decimals = 0, $decimal_separator = '.', $thousands_separator = ',') {
    return number_format($number ?? 0, $decimals, $decimal_separator, $thousands_separator);
}

/**
 * Null-safe date formatting
 * @param mixed $date
 * @param string $format
 * @return string
 */
function safe_date($date, $format = 'd.m.Y H:i') {
    if (empty($date) || $date === '0000-00-00' || $date === '0000-00-00 00:00:00') {
        return '-';
    }
    
    try {
        return date($format, strtotime($date));
    } catch (Exception $e) {
        return '-';
    }
}

/**
 * Null-safe string comparison
 * @param mixed $value1
 * @param mixed $value2
 * @return bool
 */
function safe_equals($value1, $value2) {
    return ($value1 ?? '') === ($value2 ?? '');
}

/**
 * Null-safe selected attribute
 * @param mixed $value1
 * @param mixed $value2
 * @return string
 */
function safe_selected($value1, $value2) {
    return safe_equals($value1, $value2) ? 'selected' : '';
}

/**
 * Null-safe checked attribute
 * @param mixed $value1
 * @param mixed $value2
 * @return string
 */
function safe_checked($value1, $value2) {
    return safe_equals($value1, $value2) ? 'checked' : '';
}

/**
 * Null-safe value attribute
 * @param mixed $value
 * @return string
 */
function safe_value($value) {
    return 'value="' . safe_html($value) . '"';
}

/**
 * Badge class for status
 * @param string $status
 * @return string
 */
function get_status_badge_class($status) {
    $statusColors = [
        'Numune Kabul' => 'bg-primary',
        'Ön Hazırlık' => 'bg-warning',
        'Analiz Aşamasında' => 'bg-info',
        'Analiz Tamamlandı' => 'bg-success',
        'Rapor Hazırlandı' => 'bg-secondary',
        'Teslim Edildi' => 'bg-dark'
    ];
    
    return $statusColors[$status ?? ''] ?? 'bg-secondary';
}

/**
 * Badge class for payment status
 * @param string $status
 * @return string
 */
function get_payment_badge_class($status) {
    $paymentColors = [
        'Ödendi' => 'bg-success',
        'Ödenmedi' => 'bg-danger',
        'Ücretsiz' => 'bg-info',
        'Beklemede' => 'bg-warning'
    ];
    
    return $paymentColors[$status ?? ''] ?? 'bg-secondary';
}

/**
 * Truncate text safely
 * @param mixed $text
 * @param int $length
 * @param string $suffix
 * @return string
 */
function safe_truncate($text, $length = 50, $suffix = '...') {
    $text = $text ?? '';
    if (mb_strlen($text) <= $length) {
        return $text;
    }
    
    return mb_substr($text, 0, $length) . $suffix;
}
?>

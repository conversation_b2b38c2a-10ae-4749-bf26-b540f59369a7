<?php
require_once 'config/database.php';

try {
    $db = new Database();
    
    echo "<h2>lab_numuneler Gerçek Tablo Yapısı</h2>";
    
    $sql = "DESCRIBE lab_numuneler";
    $columns = $db->fetchAll($sql);
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f0f0f0;'>";
    echo "<th><PERSON></th><th>Veri Tipi</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th>";
    echo "</tr>";
    
    $fieldNames = [];
    foreach ($columns as $col) {
        $fieldNames[] = $col['Field'];
        echo "<tr>";
        echo "<td><strong>{$col['Field']}</strong></td>";
        echo "<td>{$col['Type']}</td>";
        echo "<td>{$col['Null']}</td>";
        echo "<td>{$col['Key']}</td>";
        echo "<td>" . ($col['Default'] ?? 'NULL') . "</td>";
        echo "<td>{$col['Extra']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h3>Alan Adları (Kopya için):</h3>";
    echo "<textarea style='width: 100%; height: 100px;'>";
    echo "'" . implode("', '", $fieldNames) . "'";
    echo "</textarea>";
    
    echo "<h3>Zorunlu Alanlar (NOT NULL):</h3>";
    $requiredFields = [];
    foreach ($columns as $col) {
        if ($col['Null'] === 'NO' && $col['Extra'] !== 'auto_increment') {
            $requiredFields[] = $col['Field'] . ' (' . $col['Type'] . ')';
        }
    }
    echo "<ul>";
    foreach ($requiredFields as $field) {
        echo "<li style='color: red;'><strong>$field</strong></li>";
    }
    echo "</ul>";
    
    echo "<h3>Foreign Key Alanları:</h3>";
    $fkFields = [];
    foreach ($columns as $col) {
        if (strpos($col['Field'], '_id') !== false) {
            $fkFields[] = $col['Field'] . ' (' . $col['Type'] . ', Null: ' . $col['Null'] . ')';
        }
    }
    echo "<ul>";
    foreach ($fkFields as $field) {
        echo "<li style='color: blue;'>$field</li>";
    }
    echo "</ul>";
    
    // Test verisi ile uyumluluk kontrolü
    echo "<h3>Form Verisi Uyumluluk Kontrolü:</h3>";
    $formFields = [
        'numune_turu', 'numune_alt_turu', 'LAB_KAYITNO', 'gonderen', 'gonderen_personel',
        'firma_id', 'urun_id', 'mensei', 'ulke', 'numune_alindigiyer', 'numune_sahibi',
        'basvuru_no', 'muhur_no', 'etiket_no', 'barkod', 'lotno', 'miktar', 'birim',
        'gelis_saati', 'aciklama', 'gelis_tarihi', 'durumu'
    ];
    
    $validFields = [];
    $invalidFields = [];
    
    foreach ($formFields as $field) {
        if (in_array($field, $fieldNames)) {
            $validFields[] = $field;
        } else {
            $invalidFields[] = $field;
        }
    }
    
    echo "<h4 style='color: green;'>Geçerli Alanlar (" . count($validFields) . "):</h4>";
    echo "<p>" . implode(', ', $validFields) . "</p>";
    
    if (!empty($invalidFields)) {
        echo "<h4 style='color: red;'>Geçersiz Alanlar (" . count($invalidFields) . "):</h4>";
        echo "<p>" . implode(', ', $invalidFields) . "</p>";
    }
    
} catch (Exception $e) {
    echo "<div style='color: red;'>Hata: " . $e->getMessage() . "</div>";
}
?>

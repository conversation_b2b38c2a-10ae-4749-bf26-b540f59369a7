<?php
$pageTitle = 'Numune Detay';
$pageSubtitle = 'Numune bilgilerini görüntüle';
require_once 'views/layout/header.php';

// Numune verisi
$numune = $data['numune'] ?? null;
$logs = $data['logs'] ?? [];

if (!$numune) {
    echo '<div class="alert alert-danger">Numune bulunamadı.</div>';
    require_once 'views/layout/footer.php';
    exit;
}
?>

<style>
    /* Timeline Stilleri */
    .timeline {
        position: relative;
        padding-left: 30px;
    }

    .timeline::before {
        content: '';
        position: absolute;
        left: 15px;
        top: 0;
        bottom: 0;
        width: 2px;
        background: linear-gradient(to bottom, #007bff, #e9ecef);
    }

    .timeline-item {
        position: relative;
        margin-bottom: 25px;
        padding-bottom: 20px;
    }

    .timeline-item:last-child {
        margin-bottom: 0;
        padding-bottom: 0;
    }

    .timeline-marker {
        position: absolute;
        left: -22px;
        top: 5px;
        width: 16px;
        height: 16px;
        background: #fff;
        border: 2px solid #007bff;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1;
    }

    .timeline-marker i {
        font-size: 6px;
        color: #007bff;
    }

    .timeline-item-latest .timeline-marker {
        border-color: #28a745;
        background: #28a745;
        box-shadow: 0 0 0 4px rgba(40, 167, 69, 0.2);
        animation: pulse 2s infinite;
    }

    .timeline-item-latest .timeline-marker i {
        color: #fff;
    }

    @keyframes pulse {
        0% {
            box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.4);
        }
        70% {
            box-shadow: 0 0 0 10px rgba(40, 167, 69, 0);
        }
        100% {
            box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
        }
    }

    .timeline-content {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 15px;
        position: relative;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
    }

    .timeline-content:hover {
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        transform: translateY(-2px);
    }

    .timeline-content::before {
        content: '';
        position: absolute;
        left: -8px;
        top: 15px;
        width: 0;
        height: 0;
        border-top: 8px solid transparent;
        border-bottom: 8px solid transparent;
        border-right: 8px solid #e9ecef;
    }

    .timeline-content::after {
        content: '';
        position: absolute;
        left: -7px;
        top: 15px;
        width: 0;
        height: 0;
        border-top: 8px solid transparent;
        border-bottom: 8px solid transparent;
        border-right: 8px solid #f8f9fa;
    }

    .timeline-title {
        color: #495057;
        font-weight: 600;
        margin-bottom: 5px;
    }

    .timeline-body p {
        color: #6c757d;
        font-size: 0.9rem;
        line-height: 1.4;
    }

    /* Info cards */
    .info-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        transition: transform 0.2s ease;
    }

    .info-card:hover {
        transform: translateY(-2px);
    }

    .info-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 5px;
    }

    .info-value {
        color: #6c757d;
        font-size: 0.95rem;
    }

    /* Status badge */
    .status-badge {
        font-size: 0.9rem;
        padding: 8px 16px;
        border-radius: 20px;
    }

    /* Responsive */
    @media (max-width: 768px) {
        .timeline {
            padding-left: 25px;
        }
        
        .timeline-marker {
            left: -20px;
        }
        
        .timeline-content {
            padding: 12px;
        }
    }
</style>

<div class="row">
    <!-- Numune Bilgileri -->
    <div class="col-lg-8">
        <div class="card info-card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-vial me-2"></i>Numune Bilgileri
                    <span class="status-badge bg-<?= $numune['durumu'] === 'Numune Kabul' ? 'primary' :
                        ($numune['durumu'] === 'Ön Hazırlık' ? 'warning' :
                        ($numune['durumu'] === 'Analiz Tamamlandı' ? 'success' : 'secondary')) ?> text-white ms-3">
                        <?= safe_html($numune['durumu']) ?>
                    </span>
                </h5>
            </div>
            <div class="card-body">
                <!-- Temel Bilgiler -->
                <div class="row mb-4">
                    <div class="col-12">
                        <h6 class="text-primary border-bottom pb-2 mb-3">
                            <i class="fas fa-info-circle me-2"></i>Temel Bilgiler
                        </h6>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <div class="info-label">Lab Kayıt No</div>
                        <div class="info-value"><?= safe_html($numune['LAB_KAYITNO']) ?></div>
                    </div>

                    <div class="col-md-6 mb-3">
                        <div class="info-label">Numune Türü</div>
                        <div class="info-value"><?= safe_html($numune['numune_turu']) ?></div>
                    </div>

                    <?php if (!empty($numune['numune_alt_turu'])): ?>
                    <div class="col-md-6 mb-3">
                        <div class="info-label">Numune Alt Türü</div>
                        <div class="info-value"><?= safe_html($numune['numune_alt_turu']) ?></div>
                    </div>
                    <?php endif; ?>

                    <?php if (!empty($numune['basvuru_no'])): ?>
                    <div class="col-md-6 mb-3">
                        <div class="info-label">Başvuru No</div>
                        <div class="info-value"><?= safe_html($numune['basvuru_no']) ?></div>
                    </div>
                    <?php endif; ?>

                    <?php if (!empty($numune['muhur_no'])): ?>
                    <div class="col-md-6 mb-3">
                        <div class="info-label">Mühür No</div>
                        <div class="info-value"><?= safe_html($numune['muhur_no']) ?></div>
                    </div>
                    <?php endif; ?>
                </div>

                <!-- Gönderen Bilgileri -->
                <div class="row mb-4">
                    <div class="col-12">
                        <h6 class="text-primary border-bottom pb-2 mb-3">
                            <i class="fas fa-user me-2"></i>Gönderen Bilgileri
                        </h6>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <div class="info-label">Gönderen</div>
                        <div class="info-value"><?= safe_html($numune['gonderen']) ?></div>
                    </div>

                    <?php if (!empty($numune['gonderen_personel'])): ?>
                    <div class="col-md-6 mb-3">
                        <div class="info-label">Gönderen Personel</div>
                        <div class="info-value"><?= safe_html($numune['gonderen_personel']) ?></div>
                    </div>
                    <?php endif; ?>

                    <?php if (!empty($numune['firma_adi'])): ?>
                    <div class="col-md-6 mb-3">
                        <div class="info-label">Firma</div>
                        <div class="info-value"><?= safe_html($numune['firma_adi']) ?></div>
                    </div>
                    <?php endif; ?>

                    <?php if (!empty($numune['mensei'])): ?>
                    <div class="col-md-6 mb-3">
                        <div class="info-label">Menşei</div>
                        <div class="info-value"><?= safe_html($numune['mensei']) ?></div>
                    </div>
                    <?php endif; ?>
                </div>

                <!-- Numune Detayları -->
                <div class="row mb-4">
                    <div class="col-12">
                        <h6 class="text-primary border-bottom pb-2 mb-3">
                            <i class="fas fa-flask me-2"></i>Numune Detayları
                        </h6>
                    </div>
                    
                    <?php if (!empty($numune['urun_adi'])): ?>
                    <div class="col-md-6 mb-3">
                        <div class="info-label">Ürün</div>
                        <div class="info-value"><?= safe_html($numune['urun_adi']) ?></div>
                    </div>
                    <?php endif; ?>
                    
                    <div class="col-md-6 mb-3">
                        <div class="info-label">Miktar</div>
                        <div class="info-value"><?= safe_html($numune['miktar']) ?> <?= safe_html($numune['birim']) ?></div>
                    </div>

                    <?php if (!empty($numune['barkod'])): ?>
                    <div class="col-md-6 mb-3">
                        <div class="info-label">Barkod</div>
                        <div class="info-value"><?= safe_html($numune['barkod']) ?></div>
                    </div>
                    <?php endif; ?>

                    <?php if (!empty($numune['lotno'])): ?>
                    <div class="col-md-6 mb-3">
                        <div class="info-label">Lot No</div>
                        <div class="info-value"><?= safe_html($numune['lotno']) ?></div>
                    </div>
                    <?php endif; ?>
                </div>

                <!-- Teslim Bilgileri -->
                <div class="row mb-4">
                    <div class="col-12">
                        <h6 class="text-primary border-bottom pb-2 mb-3">
                            <i class="fas fa-handshake me-2"></i>Teslim Bilgileri
                        </h6>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <div class="info-label">Geliş Tarihi</div>
                        <div class="info-value">
                            <?php if (!empty($numune['GELIS_TARIHI'])): ?>
                                <?= date('d.m.Y H:i', strtotime($numune['GELIS_TARIHI'])) ?>
                            <?php else: ?>
                                -
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="col-md-6 mb-3">
                        <div class="info-label">Ücret Durumu</div>
                        <div class="info-value">
                            <span class="badge bg-<?= $numune['UCRET_DURUMU'] === 'Ödendi' ? 'success' :
                                ($numune['UCRET_DURUMU'] === 'Ücretsiz' ? 'info' : 'warning') ?>">
                                <?= safe_html($numune['UCRET_DURUMU']) ?>
                            </span>
                        </div>
                    </div>

                    <?php if (!empty($numune['TESLIM_EDEN'])): ?>
                    <div class="col-md-6 mb-3">
                        <div class="info-label">Teslim Eden</div>
                        <div class="info-value"><?= safe_html($numune['TESLIM_EDEN']) ?></div>
                    </div>
                    <?php endif; ?>

                    <?php if (!empty($numune['TESLIM_ALAN'])): ?>
                    <div class="col-md-6 mb-3">
                        <div class="info-label">Teslim Alan</div>
                        <div class="info-value"><?= safe_html($numune['TESLIM_ALAN']) ?></div>
                    </div>
                    <?php endif; ?>
                </div>

                <?php if (!empty($numune['aciklama'])): ?>
                <!-- Açıklamalar -->
                <div class="row">
                    <div class="col-12">
                        <h6 class="text-primary border-bottom pb-2 mb-3">
                            <i class="fas fa-comment me-2"></i>Açıklamalar
                        </h6>
                        <div class="info-value"><?= nl2br(safe_html($numune['aciklama'])) ?></div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Geri Dön Butonu -->
                <div class="row mt-4">
                    <div class="col-12">
                        <a href="index.php?page=numune-listesi" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Geri Dön
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- İşlem Geçmişi Timeline -->
    <div class="col-lg-4">
        <div class="card info-card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-history me-2"></i>İşlem Geçmişi
                </h5>
            </div>
            <div class="card-body">
                <?php if (!empty($logs)): ?>
                    <div class="timeline">
                        <?php foreach ($logs as $index => $log): ?>
                            <div class="timeline-item <?= $index === 0 ? 'timeline-item-latest' : '' ?>">
                                <div class="timeline-marker">
                                    <i class="fas fa-circle"></i>
                                </div>
                                <div class="timeline-content">
                                    <div class="timeline-header">
                                        <h6 class="timeline-title mb-1"><?= safe_html($log['islem']) ?></h6>
                                        <small class="text-muted">
                                            <i class="fas fa-clock me-1"></i>
                                            <?= date('d.m.Y H:i', strtotime($log['tarih'])) ?>
                                        </small>
                                    </div>
                                    <div class="timeline-body">
                                        <?php if (!empty($log['onceki_deger'])): ?>
                                            <p class="mb-1 text-muted small"><?= safe_html($log['onceki_deger']) ?></p>
                                        <?php endif; ?>
                                        <small class="text-muted">
                                            <i class="fas fa-user me-1"></i>
                                            <?= safe_html($log['kullanici']) ?>
                                        </small>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <div class="text-center text-muted">
                        <i class="fas fa-history fa-3x mb-3"></i>
                        <p>Henüz işlem geçmişi bulunmuyor.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php require_once 'views/layout/footer.php'; ?>

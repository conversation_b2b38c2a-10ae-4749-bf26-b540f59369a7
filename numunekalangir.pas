unit numunekalangir;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics,
  Controls, Forms, uniGUITypes, uniGUIAbstractClasses,
  uniGUIClasses, uniGUIForm, Data.DB, uniButton, uniEdit, uniDBEdit,
  uniGUIBaseClasses, uniLabel;

type
  Tfrmnumunekalangir = class(TUniForm)
    UniLabel1: TUniLabel;
    UniDBNumberEdit1: TUniDBNumberEdit;
    UniButton1: TUniButton;
    UniButton2: TUniButton;
    dsnumune: TDataSource;
    procedure UniButton2Click(Sender: TObject);
    procedure UniFormShow(Sender: TObject);
    procedure UniButton1Click(Sender: TObject);
  private
    { Private declarations }
  public
    { Public declarations }
  end;

function frmnumunekalangir: Tfrmnumunekalangir;

implementation

{$R *.dfm}

uses
  MainModule, uniGUIApplication, NumuneListesi, Onhazirlik;

function frmnumunekalangir: Tfrmnumunekalangir;
begin
  Result := Tfrmnumunekalangir(UniMainModule.GetFormInstance(Tfrmnumunekalangir));
end;

procedure Tfrmnumunekalangir.UniButton1Click(Sender: TObject);
begin
 frmOnhazirlik.tblNumuneKabul.Edit;
 frmOnhazirlik.tblNumuneKabul.Transaction.CommitRetaining;
 close;

end;

procedure Tfrmnumunekalangir.UniButton2Click(Sender: TObject);
begin
  close;
end;

procedure Tfrmnumunekalangir.UniFormShow(Sender: TObject);
begin
  dsnumune.DataSet:= frmOnhazirlik.tblNumuneKabul;
end;

end.

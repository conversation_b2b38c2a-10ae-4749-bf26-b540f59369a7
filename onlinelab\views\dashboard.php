<?php
$pageTitle = 'Dashboard';
$pageSubtitle = 'Sistem genel durumu ve istatistikler';
require_once 'views/layout/header.php';
?>

<div class="row">
    <!-- İstatistik kartları -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stat-card">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="stat-number"><?= number_format($data['stats']['toplam_numune']) ?></div>
                        <div>Toplam Numune</div>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-vials fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stat-card">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="stat-number"><?= number_format($data['stats']['bu_ay']) ?></div>
                        <div>Bu Ay</div>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-calendar-alt fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stat-card">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="stat-number"><?= number_format($data['stats']['bu_hafta']) ?></div>
                        <div>Bu Hafta</div>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-chart-line fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stat-card">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="stat-number"><?= number_format($data['stats']['bugun']) ?></div>
                        <div>Bugün</div>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-clock fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Bekleyen işler -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-tasks me-2"></i>Bekleyen İşler
                </h5>
            </div>
            <div class="card-body">
                <?php if (!empty($data['pending_tasks'])): ?>
                    <?php foreach ($data['pending_tasks'] as $task): ?>
                        <div class="d-flex justify-content-between align-items-center mb-3 p-3 bg-light rounded">
                            <div>
                                <i class="<?= $task['icon'] ?> text-<?= $task['color'] ?> me-2"></i>
                                <strong><?= $task['title'] ?></strong>
                            </div>
                            <div>
                                <span class="badge bg-<?= $task['color'] ?>"><?= $task['count'] ?></span>
                                <a href="<?= $task['url'] ?>" class="btn btn-sm btn-outline-<?= $task['color'] ?> ms-2">
                                    <i class="fas fa-arrow-right"></i>
                                </a>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-check-circle fa-3x mb-3"></i>
                        <p>Bekleyen iş bulunmamaktadır.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- Günlük numune sayıları grafiği -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-area me-2"></i>Son 7 Günün Numune Sayıları
                </h5>
            </div>
            <div class="card-body">
                <canvas id="dailyChart" height="100"></canvas>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Duruma göre dağılım -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-pie me-2"></i>Duruma Göre Dağılım
                </h5>
            </div>
            <div class="card-body">
                <canvas id="statusChart" height="150"></canvas>
            </div>
        </div>
    </div>
    
    <!-- Türe göre dağılım -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-donut me-2"></i>Türe Göre Dağılım
                </h5>
            </div>
            <div class="card-body">
                <canvas id="typeChart" height="150"></canvas>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Son numuneler -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>Son Numuneler
                </h5>
                <a href="index.php?page=numune-listesi" class="btn btn-sm btn-outline-primary">
                    Tümünü Gör <i class="fas fa-arrow-right ms-1"></i>
                </a>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Lab No</th>
                                <th>Ürün</th>
                                <th>Gönderen</th>
                                <th>Durum</th>
                                <th>Tarih</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($data['recent_numuneler'] as $numune): ?>
                                <tr>
                                    <td>
                                        <strong><?= safe_html($numune['LAB_KAYITNO']) ?></strong>
                                    </td>
                                    <td><?= safe_html($numune['urun_adi'] ?: 'Belirtilmemiş') ?></td>
                                    <td><?= safe_html($numune['gonderen']) ?></td>
                                    <td>
                                        <span class="badge <?= get_status_badge_class($numune['durumu']) ?>">
                                            <?= safe_html($numune['durumu']) ?>
                                        </span>
                                    </td>
                                    <td><?= safe_date($numune['gelis_tarihi'], 'd.m.Y') ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- En çok kullanılan ürünler -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-star me-2"></i>Popüler Ürünler
                </h5>
            </div>
            <div class="card-body">
                <?php if (!empty($data['chart_data']['top_products'])): ?>
                    <?php foreach ($data['chart_data']['top_products'] as $index => $product): ?>
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <span class="badge bg-primary me-2"><?= $index + 1 ?></span>
                                <?= safe_html($product['urunadi_t']) ?>
                            </div>
                            <span class="badge bg-secondary"><?= $product['kullanim_sayisi'] ?></span>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-box-open fa-2x mb-3"></i>
                        <p>Henüz ürün kullanımı bulunmamaktadır.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php
$customJS = "
<script>
// Grafik renkleri
const colors = {
    primary: '#667eea',
    secondary: '#764ba2',
    success: '#28a745',
    danger: '#dc3545',
    warning: '#ffc107',
    info: '#17a2b8',
    light: '#f8f9fa',
    dark: '#343a40'
};

// Günlük numune sayıları grafiği
const dailyCtx = document.getElementById('dailyChart').getContext('2d');
const dailyChart = new Chart(dailyCtx, {
    type: 'line',
    data: {
        labels: " . json_encode(array_column($data['chart_data']['daily_counts'], 'date')) . ",
        datasets: [{
            label: 'Numune Sayısı',
            data: " . json_encode(array_column($data['chart_data']['daily_counts'], 'count')) . ",
            borderColor: colors.primary,
            backgroundColor: colors.primary + '20',
            borderWidth: 3,
            fill: true,
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    stepSize: 1
                }
            }
        }
    }
});

// Duruma göre dağılım grafiği
const statusCtx = document.getElementById('statusChart').getContext('2d');
const statusChart = new Chart(statusCtx, {
    type: 'doughnut',
    data: {
        labels: " . json_encode(array_column($data['chart_data']['status_distribution'], 'label')) . ",
        datasets: [{
            data: " . json_encode(array_column($data['chart_data']['status_distribution'], 'value')) . ",
            backgroundColor: [
                colors.primary,
                colors.warning,
                colors.info,
                colors.success,
                colors.secondary,
                colors.dark
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

// Türe göre dağılım grafiği
const typeCtx = document.getElementById('typeChart').getContext('2d');
const typeChart = new Chart(typeCtx, {
    type: 'pie',
    data: {
        labels: " . json_encode(array_column($data['chart_data']['type_distribution'], 'label')) . ",
        datasets: [{
            data: " . json_encode(array_column($data['chart_data']['type_distribution'], 'value')) . ",
            backgroundColor: [
                colors.primary,
                colors.secondary
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

// Otomatik yenileme (5 dakikada bir)
setInterval(function() {
    location.reload();
}, 300000);
</script>
";

require_once 'views/layout/footer.php';
?>

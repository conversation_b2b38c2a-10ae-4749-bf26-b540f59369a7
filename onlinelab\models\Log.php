<?php
require_once 'BaseModel.php';

class Log extends BaseModel {
    protected $table = 'lab_log';
    
    // Log listesi
    public function getLogList($page = 1, $filters = []) {
        $where = '1=1';
        $params = [];

        // Filtreleme
        if (!empty($filters['user_name'])) {
            $where .= ' AND user_name LIKE ?';
            $params[] = '%' . $filters['user_name'] . '%';
        }

        if (!empty($filters['action'])) {
            $where .= ' AND action LIKE ?';
            $params[] = '%' . $filters['action'] . '%';
        }

        if (!empty($filters['table_name'])) {
            $where .= ' AND table_name = ?';
            $params[] = $filters['table_name'];
        }

        if (!empty($filters['baslangic_tarihi'])) {
            $where .= ' AND DATE(created_at) >= ?';
            $params[] = $filters['baslangic_tarihi'];
        }

        if (!empty($filters['bitis_tarihi'])) {
            $where .= ' AND DATE(created_at) <= ?';
            $params[] = $filters['bitis_tarihi'];
        }

        // Önce sayıyı al
        $countSql = "SELECT COUNT(*) as count FROM {$this->table} WHERE $where";
        $countResult = $this->db->fetch($countSql, $params);
        $total = $countResult['count'] ?? 0;

        // Sonra verileri al
        $sql = "SELECT l.*,
                       n.LAB_KAYITNO
                FROM {$this->table} l
                LEFT JOIN lab_numuneler n ON l.numune_id = n.id AND l.table_name = 'lab_numuneler'
                WHERE $where
                ORDER BY l.created_at DESC";

        $logs = $this->db->getPaginated($sql, $params, $page, RECORDS_PER_PAGE);

        return [
            'logs' => $logs,
            'total' => $total,
            'pages' => ceil($total / RECORDS_PER_PAGE),
            'current_page' => $page
        ];
    }

    // Log detayları
    public function getLogDetails($id) {
        $sql = "SELECT l.*,
                       n.LAB_KAYITNO
                FROM {$this->table} l
                LEFT JOIN lab_numuneler n ON l.record_id = n.id AND l.table_name = 'lab_numuneler'
                WHERE l.id = ?";
        
        return $this->db->fetch($sql, [$id]);
    }

    // Tablo adlarını getir
    public function getTableNames() {
        $sql = "SELECT DISTINCT table_name FROM {$this->table} WHERE table_name IS NOT NULL ORDER BY table_name";
        $result = $this->db->fetchAll($sql);
        return array_column($result, 'table_name');
    }

    // Kullanıcı adlarını getir
    public function getUserNames() {
        $sql = "SELECT DISTINCT user_name FROM {$this->table} WHERE user_name IS NOT NULL ORDER BY user_name";
        $result = $this->db->fetchAll($sql);
        return array_column($result, 'user_name');
    }

    // Log ekle
    public function addLog($tableName, $recordId, $action, $description, $userName = null, $oldData = null, $newData = null) {
        $data = [
            'table_name' => $tableName,
            'record_id' => $recordId,
            'action' => $action,
            'description' => $description,
            'user_name' => $userName ?? ($_SESSION['user_name'] ?? 'Sistem'),
            'old_data' => $oldData ? json_encode($oldData) : null,
            'new_data' => $newData ? json_encode($newData) : null,
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? null,
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null,
            'created_at' => date('Y-m-d H:i:s')
        ];

        return $this->insert($data);
    }

    // Dashboard için son aktiviteler
    public function getRecentActivities($limit = 10) {
        $sql = "SELECT l.*,
                       n.LAB_KAYITNO
                FROM {$this->table} l
                LEFT JOIN lab_numuneler n ON l.record_id = n.id AND l.table_name = 'lab_numuneler'
                ORDER BY l.created_at DESC
                LIMIT ?";
        
        return $this->db->fetchAll($sql, [$limit]);
    }

    // Kullanıcı aktivite istatistikleri
    public function getUserActivityStats($days = 30) {
        $sql = "SELECT user_name,
                       COUNT(*) as activity_count,
                       MAX(created_at) as last_activity
                FROM {$this->table}
                WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
                GROUP BY user_name
                ORDER BY activity_count DESC";
        
        return $this->db->fetchAll($sql, [$days]);
    }

    // Tablo aktivite istatistikleri
    public function getTableActivityStats($days = 30) {
        $sql = "SELECT table_name,
                       action,
                       COUNT(*) as count
                FROM {$this->table}
                WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
                  AND table_name IS NOT NULL
                GROUP BY table_name, action
                ORDER BY table_name, count DESC";
        
        return $this->db->fetchAll($sql, [$days]);
    }
}
?>

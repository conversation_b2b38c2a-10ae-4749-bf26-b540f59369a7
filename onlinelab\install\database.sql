-- Online Laboratuvar Yönet<PERSON>
-- MySQL Veritabanı Kurulum Scripti
-- Bu dosyayı MySQL'de çalıştırarak veritabanını oluşturun

-- Veritabanını oluştur
CREATE DATABASE IF NOT EXISTS laboratuvar_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE laboratuvar_db;

-- <PERSON><PERSON>ı<PERSON><PERSON><PERSON> tablosu
CREATE TABLE kullanicilar (
    id INT AUTO_INCREMENT PRIMARY KEY,
    kullaniciadi VARCHAR(50) UNIQUE NOT NULL,
    sifresi VARCHAR(255) NOT NULL,
    adisoyadi VARCHAR(100) NOT NULL,
    email VARCHAR(100),
    telefon VARCHAR(20),
    aktif TINYINT(1) DEFAULT 1,
    kayit_tarihi TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    son_giris TIMESTAMP NULL,

    -- <PERSON><PERSON>UMUNE_KABUL_YAPABILIR TINYINT(1) DEFAULT 0,
    NUMUNE_LISTESI_GOREBILIR TINYINT(1) DEFAULT 0,
    NUMUNE_ONHAZIRLIK_YAPABILIR TINYINT(1) DEFAULT 0,
    NUMUNE_SONUC_GIREBILIR TINYINT(1) DEFAULT 0,
    TANIMLAMA_YAPABILIR TINYINT(1) DEFAULT 0,
    NUMUNE_GENEL_GORUNUM TINYINT(1) DEFAULT 0,
    NUMUNE_ATAMA_YAPABILIR TINYINT(1) DEFAULT 0,
    
    INDEX idx_kullaniciadi (kullaniciadi),
    INDEX idx_aktif (aktif)
);

-- Genel tanımlar tablosu
CREATE TABLE geneltanim (
    id INT AUTO_INCREMENT PRIMARY KEY,
    sahibi INT NOT NULL,
    izahat VARCHAR(255) NOT NULL,
    aciklama TEXT,
    INDEX idx_sahibi (sahibi),
    INDEX idx_sahibi_izahat (sahibi, izahat)
);

-- Ürünler tablosu
CREATE TABLE urunler (
    id INT AUTO_INCREMENT PRIMARY KEY,
    urunadi_t VARCHAR(255) NOT NULL,
    aciklama TEXT,
    aktif TINYINT(1) DEFAULT 1,
    INDEX idx_aktif (aktif),
    INDEX idx_urunadi (urunadi_t)
);

-- Laboratuvar birimleri tablosu
CREATE TABLE laboratuvar_birimler (
    id INT AUTO_INCREMENT PRIMARY KEY,
    birimadi VARCHAR(100) NOT NULL,
    aciklama TEXT,
    aktif TINYINT(1) DEFAULT 1,
    INDEX idx_aktif (aktif)
);

-- Numuneler ana tablosu
CREATE TABLE lab_numuneler (
    id INT AUTO_INCREMENT PRIMARY KEY,
    LAB_KAYITNO VARCHAR(50),
    numune_turu ENUM('İç Karantina', 'Dış Karantina') NOT NULL,
    numune_alt_turu VARCHAR(100),
    urun_id INT,
    firma_id INT,
    gonderen VARCHAR(255),
    gonderen_personel VARCHAR(100),
    mensei VARCHAR(100),
    basvuru_no VARCHAR(50),
    muhur_no VARCHAR(50),
    etiket_no VARCHAR(50),
    miktar DECIMAL(10,2),
    birim VARCHAR(20),
    aciklama TEXT,
    barkod VARCHAR(20),
    durumu VARCHAR(100) DEFAULT 'Numune Kabul',
    ucret_durumu VARCHAR(50),
    gelis_tarihi TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    silindi TINYINT(1) DEFAULT 0,

    FOREIGN KEY (urun_id) REFERENCES urunler(id) ON DELETE SET NULL,
    FOREIGN KEY (firma_id) REFERENCES geneltanim(id) ON DELETE SET NULL,
    INDEX idx_numune_turu (numune_turu),
    INDEX idx_durumu (durumu),
    INDEX idx_gelis_tarihi (gelis_tarihi),
    INDEX idx_lab_kayitno (LAB_KAYITNO),
    INDEX idx_barkod (barkod),
    INDEX idx_silindi (silindi)
);

-- Laboratuvar atamalar tablosu
CREATE TABLE lab_atamalar (
    id INT AUTO_INCREMENT PRIMARY KEY,
    numune_id INT NOT NULL,
    birim_id INT NOT NULL,
    atama_tarihi TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    kabul_tarihi TIMESTAMP NULL,
    kabul_eden_kullanici INT NULL,
    hazirlik_tarihi TIMESTAMP NULL,
    hazirlik_yapan_kullanici INT NULL,
    analiz_kabul_tarihi TIMESTAMP NULL,
    analiz_kabul_eden_kullanici INT NULL,
    analiz_tarihi TIMESTAMP NULL,
    analiz_sonucu VARCHAR(100),
    analiz_methodu VARCHAR(255),
    atayan_kullanici INT,
    durum ENUM('Atandı', 'Kabul Edildi', 'Hazırlık Yapıldı', 'Analiz Kabul Edildi', 'Analiz Tamamlandı') DEFAULT 'Atandı',

    FOREIGN KEY (numune_id) REFERENCES lab_numuneler(id) ON DELETE CASCADE,
    FOREIGN KEY (birim_id) REFERENCES laboratuvar_birimler(id) ON DELETE CASCADE,
    FOREIGN KEY (atayan_kullanici) REFERENCES kullanicilar(id) ON DELETE SET NULL,
    FOREIGN KEY (kabul_eden_kullanici) REFERENCES kullanicilar(id) ON DELETE SET NULL,
    FOREIGN KEY (hazirlik_yapan_kullanici) REFERENCES kullanicilar(id) ON DELETE SET NULL,
    FOREIGN KEY (analiz_kabul_eden_kullanici) REFERENCES kullanicilar(id) ON DELETE SET NULL,
    UNIQUE KEY unique_assignment (numune_id, birim_id),
    INDEX idx_numune_id (numune_id),
    INDEX idx_birim_id (birim_id),
    INDEX idx_atama_tarihi (atama_tarihi),
    INDEX idx_durum (durum)
);

-- Sonuç etmenleri tablosu
CREATE TABLE lab_sonuc_etmenler (
    id INT AUTO_INCREMENT PRIMARY KEY,
    numune_id INT NOT NULL,
    ETMEN_ADI VARCHAR(255) NOT NULL,
    BULASIKMI ENUM('Evet', 'Hayır') DEFAULT 'Hayır',
    deger VARCHAR(100),
    birim VARCHAR(50),

    FOREIGN KEY (numune_id) REFERENCES lab_numuneler(id) ON DELETE CASCADE,
    INDEX idx_numune_id (numune_id),
    INDEX idx_etmen_adi (ETMEN_ADI)
);

-- Log tablosu
CREATE TABLE lab_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    numune_id INT,
    islem VARCHAR(255) NOT NULL,
    islemi_yapan VARCHAR(100),
    onceki_deger TEXT,
    tarih TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (numune_id) REFERENCES lab_numuneler(id) ON DELETE SET NULL,
    INDEX idx_numune_id (numune_id),
    INDEX idx_tarih (tarih),
    INDEX idx_islemi_yapan (islemi_yapan)
);

-- Parametreler tablosu (sayaçlar için)
CREATE TABLE parametreler (
    id INT AUTO_INCREMENT PRIMARY KEY,
    LAB_Ic_NO INT DEFAULT 1,
    LAB_dis_NO INT DEFAULT 1
);

-- Oturum tablosu (güvenlik için)
CREATE TABLE user_sessions (
    id VARCHAR(128) PRIMARY KEY,
    user_id INT NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES kullanicilar(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_last_activity (last_activity)
);

-- Dosya yüklemeleri tablosu
CREATE TABLE file_uploads (
    id INT AUTO_INCREMENT PRIMARY KEY,
    numune_id INT,
    original_name VARCHAR(255) NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INT,
    mime_type VARCHAR(100),
    uploaded_by INT,
    upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (numune_id) REFERENCES lab_numuneler(id) ON DELETE CASCADE,
    FOREIGN KEY (uploaded_by) REFERENCES kullanicilar(id) ON DELETE SET NULL,
    INDEX idx_numune_id (numune_id),
    INDEX idx_upload_date (upload_date)
);

-- Varsayılan veriler
INSERT INTO parametreler (LAB_Ic_NO, LAB_dis_NO) VALUES (1, 1);

-- Varsayılan admin kullanıcısı (kullanıcı adı: admin, şifre: admin123)
INSERT INTO kullanicilar (
    kullaniciadi, sifresi, adisoyadi,
    NUMUNE_KABUL_YAPABILIR, NUMUNE_LISTESI_GOREBILIR,
    NUMUNE_ONHAZIRLIK_YAPABILIR, NUMUNE_SONUC_GIREBILIR,
    TANIMLAMA_YAPABILIR, NUMUNE_GENEL_GORUNUM, NUMUNE_ATAMA_YAPABILIR
) VALUES (
    'admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Sistem Yöneticisi',
    1, 1, 1, 1, 1, 1, 1
);

-- Genel tanımlar için örnek veriler
-- Ülkeler (sahibi = 85)
INSERT INTO geneltanim (sahibi, izahat) VALUES
(85, 'Türkiye'),
(85, 'Almanya'),
(85, 'Fransa'),
(85, 'İtalya'),
(85, 'İspanya'),
(85, 'Hollanda'),
(85, 'Belçika'),
(85, 'İngiltere'),
(85, 'ABD'),
(85, 'Çin');

-- Müdürlükler (sahibi = 80)
INSERT INTO geneltanim (sahibi, izahat) VALUES
(80, 'Ankara Müdürlüğü'),
(80, 'İstanbul Müdürlüğü'),
(80, 'İzmir Müdürlüğü'),
(80, 'Antalya Müdürlüğü'),
(80, 'Bursa Müdürlüğü');

-- Firmalar (sahibi = 89)
INSERT INTO geneltanim (sahibi, izahat) VALUES
(89, 'ABC Gıda San. Tic. Ltd. Şti.'),
(89, 'XYZ İhracat A.Ş.'),
(89, 'DEF Tarım Ürünleri Ltd.'),
(89, 'GHI Gıda Sanayi A.Ş.'),
(89, 'JKL İthalat İhracat Ltd.');

-- Alındığı yerler (sahibi = 88)
INSERT INTO geneltanim (sahibi, izahat) VALUES
(88, 'Fabrika'),
(88, 'Depo'),
(88, 'Mağaza'),
(88, 'Gümrük'),
(88, 'Liman'),
(88, 'Havaalanı');

-- İç karantina türleri (sahibi = 180)
INSERT INTO geneltanim (sahibi, izahat) VALUES
(180, 'İthalat'),
(180, 'İhracat'),
(180, 'Transit');

-- Dış karantina türleri (sahibi = 181)
INSERT INTO geneltanim (sahibi, izahat) VALUES
(181, 'Transit'),
(181, 'Gümrük'),
(181, 'Serbest Bölge');

-- Etmenler (sahibi = 184)
INSERT INTO geneltanim (sahibi, izahat) VALUES
(184, 'Salmonella'),
(184, 'E.Coli'),
(184, 'Listeria'),
(184, 'Aflatoksin'),
(184, 'Pestisit'),
(184, 'Ağır Metal'),
(184, 'Mikotoksin'),
(184, 'Patojen Bakteri');

-- Örnek ürünler
INSERT INTO urunler (urunadi_t, aciklama) VALUES
('Buğday', 'Tahıl ürünü'),
('Mısır', 'Tahıl ürünü'),
('Arpa', 'Tahıl ürünü'),
('Pirinç', 'Tahıl ürünü'),
('Fasulye', 'Baklagil'),
('Nohut', 'Baklagil'),
('Mercimek', 'Baklagil'),
('Fındık', 'Kuruyemiş'),
('Antep Fıstığı', 'Kuruyemiş'),
('Kuru İncir', 'Kurutulmuş meyve'),
('Kuru Üzüm', 'Kurutulmuş meyve'),
('Badem', 'Kuruyemiş'),
('Ceviz', 'Kuruyemiş'),
('Susam', 'Yağlı tohum'),
('Ayçiçeği', 'Yağlı tohum');

-- Örnek laboratuvar birimleri
INSERT INTO laboratuvar_birimler (birimadi, aciklama) VALUES
('Mikrobiyoloji', 'Mikrobiyal analizler ve patojen testleri'),
('Kimya', 'Kimyasal analizler ve bileşim testleri'),
('Pestisit', 'Pestisit kalıntı analizleri'),
('Aflatoksin', 'Aflatoksin ve mikotoksin analizleri'),
('Fiziksel', 'Fiziksel özellik analizleri'),
('Ağır Metal', 'Ağır metal analizleri'),
('Genetik', 'GMO ve genetik analizler');

-- Trigger'lar ve stored procedure'lar

-- Lab kayıt numarası otomatik artırma trigger'ı
DELIMITER //
CREATE TRIGGER tr_lab_kayitno_update 
AFTER INSERT ON lab_numuneler
FOR EACH ROW
BEGIN
    IF NEW.numune_turu = 'İç Karantina' THEN
        UPDATE parametreler SET LAB_Ic_NO = LAB_Ic_NO + 1;
    ELSE
        UPDATE parametreler SET LAB_dis_NO = LAB_dis_NO + 1;
    END IF;
END//
DELIMITER ;

-- Log otomatik ekleme trigger'ı
DELIMITER //
CREATE TRIGGER tr_numune_log_insert
AFTER INSERT ON lab_numuneler
FOR EACH ROW
BEGIN
    INSERT INTO lab_log (numune_id, islem, islemi_yapan, tarih)
    VALUES (NEW.id, 'Numune kaydı oluşturuldu', 'Sistem', NOW());
END//
DELIMITER ;

-- Numune durum değişikliği log trigger'ı
DELIMITER //
CREATE TRIGGER tr_numune_durum_log
AFTER UPDATE ON lab_numuneler
FOR EACH ROW
BEGIN
    IF OLD.durumu != NEW.durumu THEN
        INSERT INTO lab_log (numune_id, islem, islemi_yapan, onceki_deger, tarih)
        VALUES (NEW.id, CONCAT('Durum değiştirildi: ', OLD.durumu, ' → ', NEW.durumu), 'Sistem', OLD.durumu, NOW());
    END IF;
END//
DELIMITER ;

-- View'lar

-- Numune detay view'ı
CREATE VIEW v_numune_detay AS
SELECT 
    n.*,
    u.urunadi_t as urun_adi,
    f.izahat as firma_adi,
    ul.izahat as ulke_adi,
    COUNT(a.id) as atama_sayisi,
    GROUP_CONCAT(DISTINCT lb.birimadi SEPARATOR ', ') as atanan_birimler
FROM lab_numuneler n
LEFT JOIN urunler u ON n.urun_id = u.id
LEFT JOIN geneltanim f ON n.firma_id = f.id
LEFT JOIN geneltanim ul ON n.mensei = ul.izahat AND ul.sahibi = 85
LEFT JOIN lab_atamalar a ON n.id = a.numune_id
LEFT JOIN laboratuvar_birimler lb ON a.birim_id = lb.id
WHERE n.silindi = 0
GROUP BY n.id;

-- Birim iş yükü view'ı
CREATE VIEW v_birim_is_yuku AS
SELECT 
    lb.id,
    lb.birimadi,
    COUNT(a.id) as toplam_atama,
    COUNT(CASE WHEN a.analiz_tarihi IS NULL THEN 1 END) as bekleyen_analiz,
    COUNT(CASE WHEN a.analiz_tarihi IS NOT NULL THEN 1 END) as tamamlanan_analiz
FROM laboratuvar_birimler lb
LEFT JOIN lab_atamalar a ON lb.id = a.birim_id
WHERE lb.aktif = 1
GROUP BY lb.id, lb.birimadi;

-- İndeksler optimizasyonu
CREATE INDEX idx_numune_gelis_yil ON lab_numuneler (YEAR(gelis_tarihi));
CREATE INDEX idx_numune_gelis_ay ON lab_numuneler (YEAR(gelis_tarihi), MONTH(gelis_tarihi));
CREATE INDEX idx_log_tarih_yil ON lab_log (YEAR(tarih));

-- Kurulum tamamlandı mesajı
SELECT 'Laboratuvar Yönetim Sistemi veritabanı başarıyla kuruldu!' as Mesaj;
SELECT 'Varsayılan kullanıcı: admin, Şifre: admin123' as Giris_Bilgileri;

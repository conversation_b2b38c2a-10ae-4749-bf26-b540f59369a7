object frmNumuneKabul: TfrmNumuneKabul
  Left = 0
  Top = 0
  ClientHeight = 637
  ClientWidth = 457
  Caption = 'Numune Kabul'
  OnShow = UniFormShow
  OldCreateOrder = False
  OnClose = UniFormClose
  MonitoredKeys.Keys = <>
  AlignmentControl = uniAlignmentClient
  Layout = 'vbox'
  LayoutConfig.ColumnWidth = 2.000000000000000000
  LayoutAttribs.Columns = 2
  OnReady = UniFormReady
  PixelsPerInch = 96
  TextHeight = 13
  object UniPanel1: TUniPanel
    Left = 0
    Top = 0
    Width = 457
    Height = 129
    Align = alTop
    TabOrder = 1
    object UniLabel1: TUniLabel
      Left = 12
      Top = 18
      Width = 64
      Height = 13
      Caption = 'Numune T'#252'r'#252
      TabOrder = 4
    end
    object UniLabel2: TUniLabel
      Left = 12
      Top = 74
      Width = 102
      Height = 13
      Caption = 'Laboratuvar Kay'#305't No'
      TabOrder = 5
    end
    object UniLabel3: TUniLabel
      Left = 12
      Top = 47
      Width = 80
      Height = 13
      Caption = 'Numune Alt T'#252'r'#252
      TabOrder = 6
    end
    object UniDBComboBox1: TUniDBComboBox
      Left = 157
      Top = 16
      Width = 257
      Enabled = False
      DataField = 'NUMUNE_TURU'
      DataSource = dsNumuneKabul
      Style = csDropDownList
      Items.Strings = (
        #304#231' Karantina'
        'D'#305#351' Karantina')
      TabOrder = 0
      IconItems = <>
      OnChange = UniDBComboBox1Change
    end
    object UniDBEdit1: TUniDBEdit
      Left = 157
      Top = 71
      Width = 257
      Height = 22
      DataField = 'LAB_KAYITNO'
      DataSource = dsNumuneKabul
      TabOrder = 2
      ReadOnly = True
      OnExit = UniDBEdit1Exit
    end
    object UniDBComboBox2: TUniDBComboBox
      Left = 157
      Top = 44
      Width = 257
      DataField = 'NUMUNE_ALT_TURU'
      DataSource = dsNumuneKabul
      Items.Strings = (
        'Proje'
        'Sertifikasyon'
        'S'#252'rvey'
        'Bitki Pasaportu'
        'Di'#287'er')
      TabOrder = 1
      IconItems = <>
      OnChange = UniDBComboBox2Change
    end
    object UniLabel8: TUniLabel
      Left = 13
      Top = 100
      Width = 37
      Height = 13
      Caption = 'QrCode'
      TabOrder = 7
    end
    object UniDBEdit6: TUniDBEdit
      Left = 157
      Top = 98
      Width = 257
      Height = 22
      DataField = 'barkod'
      DataSource = dsNumuneKabul
      TabOrder = 8
    end
    object UniButton7: TUniButton
      Left = 415
      Top = 98
      Width = 27
      Height = 23
      Caption = '---'
      TabStop = False
      TabOrder = 9
      OnClick = UniButton7Click
    end
    object UniButton9: TUniButton
      Left = 415
      Top = 43
      Width = 27
      Height = 23
      Caption = '---'
      TabStop = False
      TabOrder = 10
      OnClick = UniButton9Click
    end
    object UniLabel20: TUniLabel
      Left = 74
      Top = 100
      Width = 43
      Height = 13
      Cursor = crHandPoint
      Caption = '( Yazd'#305'r )'
      ParentFont = False
      Font.Color = clBlue
      Font.Style = [fsUnderline]
      TabOrder = 11
      OnClick = UniLabel20Click
    end
  end
  object pnlDisKarantina: TUniPanel
    Left = 0
    Top = 129
    Width = 457
    Height = 112
    Align = alTop
    TabOrder = 4
    object UniLabel5: TUniLabel
      Left = 12
      Top = 12
      Width = 96
      Height = 13
      Caption = 'G'#246'nderen '#304'nspekt'#246'r'
      TabOrder = 1
    end
    object UniLabel6: TUniLabel
      Left = 12
      Top = 37
      Width = 89
      Height = 13
      Caption = 'Numunenin Men'#351'ei'
      TabOrder = 2
    end
    object UniLabel7: TUniLabel
      Left = 12
      Top = 65
      Width = 86
      Height = 13
      Caption = 'Ba'#351'vuru Numaras'#305
      TabOrder = 3
    end
    object UniDBLookupComboBox4: TUniDBLookupComboBox
      Left = 157
      Top = 9
      Width = 257
      ListField = 'ADISOYADI'
      ListSource = dsPersonel
      KeyField = 'ADISOYADI'
      ListFieldIndex = 0
      DataField = 'GONDEREN_personel'
      DataSource = dsNumuneKabul
      TabOrder = 4
      Color = clWindow
      ClientEvents.ExtEvents.Strings = (
        
          'beforerender=function beforerender(sender, eOpts)'#13#10'{'#13#10'          ' +
          '  sender.allowBlank=true; '#13#10'  sender.editable = true;        '#13#10'}')
    end
    object UniDBLookupComboBox5: TUniDBLookupComboBox
      Left = 157
      Top = 35
      Width = 257
      ListField = 'ULKE_T'
      ListSource = dsUlkeler
      KeyField = 'ULKE_T'
      ListFieldIndex = 0
      DataField = 'MENSEI'
      DataSource = dsNumuneKabul
      TabOrder = 5
      Color = clWindow
      ClientEvents.ExtEvents.Strings = (
        
          'beforerender=function beforerender(sender, eOpts)'#13#10'{'#13#10'          ' +
          'sender.allowBlank=true; '#13#10'  sender.editable = true;'#13#10'}')
    end
    object UniDBEdit5: TUniDBEdit
      Left = 157
      Top = 60
      Width = 257
      Height = 22
      DataField = 'BASVURU_NO'
      DataSource = dsNumuneKabul
      TabOrder = 6
    end
    object UniButton10: TUniButton
      Left = 414
      Top = 34
      Width = 27
      Height = 23
      Caption = '---'
      TabStop = False
      TabOrder = 8
      OnClick = UniButton10Click
    end
    object lblLot: TUniLabel
      Left = 12
      Top = 89
      Width = 62
      Height = 13
      Caption = 'Lot Numaras'#305
      TabOrder = 9
    end
    object edtLot: TUniDBEdit
      Left = 157
      Top = 86
      Width = 257
      Height = 22
      DataField = 'lotno'
      DataSource = dsNumuneKabul
      TabOrder = 7
    end
  end
  object UniPanel8: TUniPanel
    Left = 0
    Top = 241
    Width = 457
    Height = 40
    Align = alTop
    TabOrder = 9
    ExplicitLeft = -3
    ExplicitTop = 237
    object UniLabel21: TUniLabel
      Left = 12
      Top = 13
      Width = 33
      Height = 13
      Caption = 'Men'#351'ei'
      TabOrder = 1
    end
    object UniDBLookupComboBox9: TUniDBLookupComboBox
      Left = 157
      Top = 11
      Width = 257
      ListField = 'ULKE_T'
      ListSource = dsUlkeler
      KeyField = 'ULKE_T'
      ListFieldIndex = 0
      DataField = 'MENSEI_2'
      DataSource = dsNumuneKabul
      TabOrder = 2
      Color = clWindow
      ClientEvents.ExtEvents.Strings = (
        
          'beforerender=function beforerender(sender, eOpts)'#13#10'{'#13#10'          ' +
          '    sender.allowBlank=true; '#13#10'  sender.editable = true;'#13#10'}')
    end
  end
  object UniPanel7: TUniPanel
    Left = 0
    Top = 281
    Width = 457
    Height = 71
    Align = alTop
    TabOrder = 3
    ExplicitTop = 202
    ExplicitWidth = 444
    object UniLabel16: TUniLabel
      Left = 12
      Top = 11
      Width = 70
      Height = 13
      Visible = False
      Caption = 'Numune Sahibi'
      TabOrder = 1
    end
    object UniLabel17: TUniLabel
      Left = 12
      Top = 38
      Width = 77
      Height = 13
      Caption = 'M'#252'h'#252'r Numaras'#305
      TabOrder = 2
    end
    object UniDBLookupComboBox3: TUniDBLookupComboBox
      Left = 157
      Top = 9
      Width = 257
      Visible = False
      ListFieldIndex = 0
      DataField = 'NUMUNE_SAHIBI'
      DataSource = dsNumuneKabul
      TabOrder = 3
      Color = clWindow
    end
    object UniDBEdit4: TUniDBEdit
      Left = 157
      Top = 36
      Width = 257
      Height = 22
      DataField = 'MUHUR_NO'
      DataSource = dsNumuneKabul
      TabOrder = 4
    end
  end
  object pnlickarantina: TUniPanel
    Left = 0
    Top = 352
    Width = 457
    Height = 104
    Align = alTop
    TabOrder = 2
    ExplicitLeft = 8
    ExplicitTop = 8
    ExplicitWidth = 481
    object UniLabel4: TUniLabel
      Left = 13
      Top = 15
      Width = 93
      Height = 13
      Caption = 'G'#246'nderen M'#252'd'#252'rl'#252'k'
      TabOrder = 1
    end
    object UniDBLookupComboBox2: TUniDBLookupComboBox
      Left = 157
      Top = 11
      Width = 257
      ListField = 'IZAHAT'
      ListSource = dsMudurlukler
      KeyField = 'IZAHAT'
      ListFieldIndex = 0
      DataField = 'GONDEREN'
      DataSource = dsNumuneKabul
      TabOrder = 2
      Color = clWindow
    end
    object UniLabel13: TUniLabel
      Left = 13
      Top = 43
      Width = 108
      Height = 13
      Caption = 'Numunenin Al'#305'nd'#305#287#305' Yer'
      TabOrder = 3
    end
    object UniLabel15: TUniLabel
      Left = 13
      Top = 65
      Width = 208
      Height = 13
      TextConversion = txtHTML
      Caption = 'Etiket Numaras'#305' /<br> '#220'st Yaz'#305' Say'#305's'#305'</br>'
      TabOrder = 4
    end
    object UniDBEdit3: TUniDBEdit
      Left = 157
      Top = 67
      Width = 257
      Height = 22
      DataField = 'ETIKET_NO'
      DataSource = dsNumuneKabul
      TabOrder = 5
    end
    object UniButton4: TUniButton
      Left = 414
      Top = 10
      Width = 27
      Height = 23
      Caption = '---'
      TabStop = False
      TabOrder = 6
      OnClick = UniButton4Click
    end
    object UniButton12: TUniButton
      Left = 415
      Top = 38
      Width = 27
      Height = 23
      Caption = '---'
      TabStop = False
      TabOrder = 7
      OnClick = UniButton12Click
    end
    object UniDBLookupComboBox8: TUniDBLookupComboBox
      Left = 157
      Top = 39
      Width = 257
      ListField = 'IZAHAT'
      ListSource = dsAlindigiYer
      KeyField = 'IZAHAT'
      ListFieldIndex = 0
      DataField = 'NUMUNE_ALINDIGIYER'
      DataSource = dsNumuneKabul
      TabOrder = 8
      Color = clWindow
    end
  end
  object UniPanel3: TUniPanel
    Left = 0
    Top = 456
    Width = 457
    Height = 40
    Align = alTop
    TabOrder = 8
    ExplicitTop = 401
    object UniLabel19: TUniLabel
      Left = 15
      Top = 13
      Width = 44
      Height = 13
      Caption = 'Firma Ad'#305
      TabOrder = 1
    end
    object UniDBLookupComboBox1: TUniDBLookupComboBox
      Left = 157
      Top = 11
      Width = 257
      ListField = 'IZAHAT'
      ListSource = dsFirma
      KeyField = 'ID'
      ListFieldIndex = 0
      DataField = 'firma_id'
      DataSource = dsNumuneKabul
      TabOrder = 2
      Color = clWindow
      ClientEvents.ExtEvents.Strings = (
        
          'beforerender=function beforerender(sender, eOpts)'#13#10'{'#13#10'          ' +
          '    sender.allowBlank=true; '#13#10'  sender.editable = true;'#13#10'}')
    end
    object UniButton11: TUniButton
      Left = 414
      Top = 10
      Width = 27
      Height = 23
      Caption = '---'
      TabStop = False
      TabOrder = 3
      OnClick = UniButton11Click
    end
  end
  object UniPanel4: TUniPanel
    Left = 0
    Top = 496
    Width = 457
    Height = 106
    Align = alTop
    TabOrder = 5
    LayoutAttribs.Align = 'top'
    ExplicitTop = 405
    ExplicitWidth = 453
    object UniLabel9: TUniLabel
      Left = 16
      Top = 45
      Width = 57
      Height = 13
      Caption = #220'r'#252'n Miktar'#305
      TabOrder = 1
    end
    object UniLabel10: TUniLabel
      Left = 16
      Top = 74
      Width = 24
      Height = 13
      Caption = 'Birimi'
      TabOrder = 2
    end
    object UniLabel11: TUniLabel
      Left = 16
      Top = 16
      Width = 41
      Height = 13
      Caption = #220'r'#252'n Ad'#305
      TabOrder = 3
    end
    object UniDBLookupComboBox6: TUniDBLookupComboBox
      Left = 157
      Top = 15
      Width = 257
      ListField = 'URUNADI_T'
      ListSource = dsurun
      KeyField = 'ID'
      ListFieldIndex = 0
      DataField = 'URUN_ID'
      DataSource = dsNumuneKabul
      TabOrder = 4
      Color = clWindow
      ClientEvents.ExtEvents.Strings = (
        
          'beforerender=function beforerender(sender, eOpts)'#13#10'{'#13#10'        se' +
          'nder.allowBlank=true; '#13#10'  sender.editable = true;'#13#10'}')
    end
    object UniDBNumberEdit1: TUniDBNumberEdit
      Left = 157
      Top = 43
      Width = 169
      Height = 22
      DataField = 'URUN_MIKTARI'
      DataSource = dsNumuneKabul
      TabOrder = 5
      DecimalSeparator = ','
    end
    object UniDBLookupComboBox7: TUniDBLookupComboBox
      Left = 157
      Top = 71
      Width = 257
      ListField = 'BIRIM_T'
      ListSource = dsBirim
      KeyField = 'BIRIM_T'
      ListFieldIndex = 0
      DataField = 'MIKTAR_BIRIM'
      DataSource = dsNumuneKabul
      TabOrder = 6
      Color = clWindow
      ClientEvents.ExtEvents.Strings = (
        
          'beforerender=function beforerender(sender, eOpts)'#13#10'{'#13#10'          ' +
          'sender.allowBlank=true; '#13#10'  sender.editable = true;'#13#10'}')
    end
    object UniButton5: TUniButton
      Left = 414
      Top = 14
      Width = 27
      Height = 23
      Caption = '---'
      TabStop = False
      TabOrder = 7
      OnClick = UniButton5Click
    end
    object UniButton6: TUniButton
      Left = 414
      Top = 69
      Width = 27
      Height = 23
      Caption = '---'
      TabStop = False
      TabOrder = 8
      OnClick = UniButton6Click
    end
  end
  object UniPanel5: TUniPanel
    Left = 0
    Top = 560
    Width = 457
    Height = 35
    Align = alBottom
    TabOrder = 6
    LayoutAttribs.Align = 'top'
    object UniLabel12: TUniLabel
      Left = 232
      Top = 10
      Width = 49
      Height = 13
      Caption = 'Geli'#351' Saati'
      TabOrder = 1
    end
    object UniLabel14: TUniLabel
      Left = 16
      Top = 10
      Width = 51
      Height = 13
      Caption = 'Geli'#351' Tarihi'
      TabOrder = 2
    end
    object UniDBText1: TUniDBText
      Left = 88
      Top = 10
      Width = 56
      Height = 13
      DataField = 'GELIS_TARIHI'
      DataSource = dsNumuneKabul
    end
    object UniDBText2: TUniDBText
      Left = 305
      Top = 10
      Width = 56
      Height = 13
      DataField = 'GELIS_SAATI'
      DataSource = dsNumuneKabul
    end
  end
  object UniPanel6: TUniPanel
    Left = 0
    Top = 595
    Width = 457
    Height = 42
    Align = alBottom
    TabOrder = 0
    BorderStyle = ubsNone
    Color = 16447736
    object UniButton1: TUniButton
      AlignWithMargins = True
      Left = 379
      Top = 3
      Width = 75
      Height = 36
      Caption = 'Kapat'
      Align = alRight
      TabOrder = 1
      LayoutConfig.Margin = '2 2 2 2'
      OnClick = UniButton1Click
    end
    object UniButton2: TUniButton
      AlignWithMargins = True
      Left = 298
      Top = 3
      Width = 75
      Height = 36
      Caption = 'Kaydet'
      Align = alRight
      TabOrder = 2
      LayoutConfig.Margin = '2 2 2 2'
      OnClick = UniButton2Click
    end
    object UniButton3: TUniButton
      AlignWithMargins = True
      Left = 129
      Top = 3
      Width = 163
      Height = 36
      Visible = False
      Caption = 'Kaydet ve Onaya G'#246'nder'
      Align = alRight
      TabOrder = 3
      LayoutConfig.Margin = '2 2 2 2'
      OnClick = UniButton3Click
    end
    object lblDurum: TUniLabel
      Left = 16
      Top = 10
      Width = 41
      Height = 13
      Visible = False
      Caption = 'lblDurum'
      TabOrder = 4
    end
    object UniButton8: TUniButton
      AlignWithMargins = True
      Left = 3
      Top = 3
      Width = 75
      Height = 36
      Caption = 'Yazd'#305'r'
      Align = alLeft
      TabOrder = 5
      LayoutConfig.Margin = '2 2 2 2'
      OnClick = UniButton8Click
    end
  end
  object UniPanel2: TUniPanel
    Left = 0
    Top = 602
    Width = 457
    Height = 71
    Align = alTop
    TabOrder = 7
    object UniLabel18: TUniLabel
      Left = 16
      Top = 6
      Width = 29
      Height = 13
      Caption = 'Notlar'
      TabOrder = 1
    end
    object UniDBMemo1: TUniDBMemo
      Left = 173
      Top = 3
      Width = 284
      Height = 55
      DataField = 'ACIKLAMALAR'
      DataSource = dsNumuneKabul
      TabOrder = 2
    end
  end
  object dsNumuneKabul: TDataSource
    Left = 256
    Top = 96
  end
  object tblBirim: TpFIBDataSet
    UpdateSQL.Strings = (
      'UPDATE TANIM_BIRIMLER'
      'SET '
      '    BIRIM_T = :BIRIM_T,'
      '    BIRIM_I = :BIRIM_I,'
      '    BIRIM_A = :BIRIM_A,'
      '    BIRIM_L = :BIRIM_L,'
      '    DURUMU = :DURUMU,'
      '    EFKODU = :EFKODU'
      'WHERE'
      '    ID = :OLD_ID'
      '    ')
    DeleteSQL.Strings = (
      'DELETE FROM'
      '    TANIM_BIRIMLER'
      'WHERE'
      '        ID = :OLD_ID'
      '    ')
    InsertSQL.Strings = (
      'INSERT INTO TANIM_BIRIMLER('
      '    ID,'
      '    BIRIM_T,'
      '    BIRIM_I,'
      '    BIRIM_A,'
      '    BIRIM_L,'
      '    DURUMU,'
      '    EFKODU'
      ')'
      'VALUES('
      '    :ID,'
      '    :BIRIM_T,'
      '    :BIRIM_I,'
      '    :BIRIM_A,'
      '    :BIRIM_L,'
      '    :DURUMU,'
      '    :EFKODU'
      ')')
    RefreshSQL.Strings = (
      'SELECT'
      '    *'
      'FROM'
      'tanim_birimler'
      ''
      ' WHERE '
      '        TANIM_BIRIMLER.ID = :OLD_ID'
      '    ')
    SelectSQL.Strings = (
      'SELECT'
      '    *'
      'FROM'
      'tanim_birimler')
    AutoUpdateOptions.UpdateTableName = 'SERTIFIKA'
    AutoUpdateOptions.KeyFields = 'ID'
    AutoUpdateOptions.GeneratorName = 'GEN_SERTIFIKA_ID'
    AutoUpdateOptions.WhenGetGenID = wgOnNewRecord
    Transaction = UniMainModule.trtanim
    Database = UniMainModule.db
    Left = 224
    Top = 192
  end
  object dsBirim: TDataSource
    DataSet = tblBirim
    Left = 288
    Top = 192
  end
  object tblUrun: TpFIBDataSet
    UpdateSQL.Strings = (
      'UPDATE URUNLER'
      'SET '
      '    URUNADI_T = :URUNADI_T,'
      '    URUNADI_A = :URUNADI_A,'
      '    URUNADI_I = :URUNADI_I,'
      '    URUNADI_L = :URUNADI_L,'
      '    DURUMU = :DURUMU,'
      '    PRODCODE = :PRODCODE,'
      '    KAYDEDEN = :KAYDEDEN,'
      '    DEGISTIREN = :DEGISTIREN,'
      '    KAYITZAMANI = :KAYITZAMANI,'
      '    DEGISTIRMEZAMANI = :DEGISTIRMEZAMANI,'
      '    ALTGRUPID = :ALTGRUPID,'
      '    DS_FIYATGRUBU = :DS_FIYATGRUBU,'
      '    GTIPNO = :GTIPNO'
      'WHERE'
      '    ID = :OLD_ID'
      '    ')
    DeleteSQL.Strings = (
      'DELETE FROM'
      '    URUNLER'
      'WHERE'
      '        ID = :OLD_ID'
      '    ')
    InsertSQL.Strings = (
      'INSERT INTO URUNLER('
      '    ID,'
      '    URUNADI_T,'
      '    URUNADI_A,'
      '    URUNADI_I,'
      '    URUNADI_L,'
      '    DURUMU,'
      '    PRODCODE,'
      '    KAYDEDEN,'
      '    DEGISTIREN,'
      '    KAYITZAMANI,'
      '    DEGISTIRMEZAMANI,'
      '    ALTGRUPID,'
      '    DS_FIYATGRUBU,'
      '    GTIPNO'
      ')'
      'VALUES('
      '    :ID,'
      '    :URUNADI_T,'
      '    :URUNADI_A,'
      '    :URUNADI_I,'
      '    :URUNADI_L,'
      '    :DURUMU,'
      '    :PRODCODE,'
      '    :KAYDEDEN,'
      '    :DEGISTIREN,'
      '    :KAYITZAMANI,'
      '    :DEGISTIRMEZAMANI,'
      '    :ALTGRUPID,'
      '    :DS_FIYATGRUBU,'
      '    :GTIPNO'
      ')')
    RefreshSQL.Strings = (
      'SELECT'
      '    *'
      'FROM'
      'urunler'
      ''
      ' WHERE '
      '        URUNLER.ID = :OLD_ID'
      '    ')
    SelectSQL.Strings = (
      'SELECT'
      '    *'
      'FROM'
      'urunler')
    AutoUpdateOptions.UpdateTableName = 'SERTIFIKA'
    AutoUpdateOptions.KeyFields = 'ID'
    AutoUpdateOptions.GeneratorName = 'GEN_SERTIFIKA_ID'
    AutoUpdateOptions.WhenGetGenID = wgOnNewRecord
    Transaction = UniMainModule.trtanim
    Database = UniMainModule.db
    Left = 224
    Top = 256
  end
  object dsurun: TDataSource
    DataSet = tblUrun
    Left = 288
    Top = 256
  end
  object tblMudurlukler: TpFIBDataSet
    UpdateSQL.Strings = (
      'UPDATE URUNLER'
      'SET '
      '    URUNADI_T = :URUNADI_T,'
      '    URUNADI_A = :URUNADI_A,'
      '    URUNADI_I = :URUNADI_I,'
      '    URUNADI_L = :URUNADI_L,'
      '    DURUMU = :DURUMU,'
      '    PRODCODE = :PRODCODE,'
      '    KAYDEDEN = :KAYDEDEN,'
      '    DEGISTIREN = :DEGISTIREN,'
      '    KAYITZAMANI = :KAYITZAMANI,'
      '    DEGISTIRMEZAMANI = :DEGISTIRMEZAMANI,'
      '    ALTGRUPID = :ALTGRUPID,'
      '    DS_FIYATGRUBU = :DS_FIYATGRUBU,'
      '    GTIPNO = :GTIPNO'
      'WHERE'
      '    ID = :OLD_ID'
      '    ')
    DeleteSQL.Strings = (
      'DELETE FROM'
      '    URUNLER'
      'WHERE'
      '        ID = :OLD_ID'
      '    ')
    InsertSQL.Strings = (
      'INSERT INTO URUNLER('
      '    ID,'
      '    URUNADI_T,'
      '    URUNADI_A,'
      '    URUNADI_I,'
      '    URUNADI_L,'
      '    DURUMU,'
      '    PRODCODE,'
      '    KAYDEDEN,'
      '    DEGISTIREN,'
      '    KAYITZAMANI,'
      '    DEGISTIRMEZAMANI,'
      '    ALTGRUPID,'
      '    DS_FIYATGRUBU,'
      '    GTIPNO'
      ')'
      'VALUES('
      '    :ID,'
      '    :URUNADI_T,'
      '    :URUNADI_A,'
      '    :URUNADI_I,'
      '    :URUNADI_L,'
      '    :DURUMU,'
      '    :PRODCODE,'
      '    :KAYDEDEN,'
      '    :DEGISTIREN,'
      '    :KAYITZAMANI,'
      '    :DEGISTIRMEZAMANI,'
      '    :ALTGRUPID,'
      '    :DS_FIYATGRUBU,'
      '    :GTIPNO'
      ')')
    RefreshSQL.Strings = (
      'SELECT'
      '    *'
      'FROM'
      'urunler'
      ''
      ' WHERE '
      '        URUNLER.ID = :OLD_ID'
      '    ')
    SelectSQL.Strings = (
      'SELECT'
      '    *'
      'FROM'
      'geneltanim'
      'where sahibi=80')
    AutoUpdateOptions.UpdateTableName = 'SERTIFIKA'
    AutoUpdateOptions.KeyFields = 'ID'
    AutoUpdateOptions.GeneratorName = 'GEN_SERTIFIKA_ID'
    AutoUpdateOptions.WhenGetGenID = wgOnNewRecord
    Transaction = UniMainModule.trtanim
    Database = UniMainModule.db
    Left = 216
    Top = 320
  end
  object dsMudurlukler: TDataSource
    DataSet = tblMudurlukler
    Left = 288
    Top = 320
  end
  object tblUlkeler: TpFIBDataSet
    UpdateSQL.Strings = (
      'UPDATE ULKELER'
      'SET '
      '    ULKE_T = :ULKE_T'
      'WHERE'
      '    ID = :OLD_ID'
      '    ')
    DeleteSQL.Strings = (
      'DELETE FROM'
      '    ULKELER'
      'WHERE'
      '        ID = :OLD_ID'
      '    ')
    InsertSQL.Strings = (
      'INSERT INTO ULKELER('
      '    ID,'
      '    ULKE_T'
      ')'
      'VALUES('
      '    :ID,'
      '    :ULKE_T'
      ')')
    RefreshSQL.Strings = (
      'SELECT'
      '    id,ulke_t'
      'FROM'
      'ulkeler'
      ''
      ' WHERE '
      '        ULKELER.ID = :OLD_ID'
      '    ')
    SelectSQL.Strings = (
      'SELECT'
      '    id,ulke_t'
      'FROM'
      'ulkeler')
    AutoUpdateOptions.UpdateTableName = 'SERTIFIKA'
    AutoUpdateOptions.KeyFields = 'ID'
    AutoUpdateOptions.GeneratorName = 'GEN_SERTIFIKA_ID'
    AutoUpdateOptions.WhenGetGenID = wgOnNewRecord
    Transaction = UniMainModule.trtanim
    Database = UniMainModule.db
    Left = 56
    Top = 120
  end
  object dsUlkeler: TDataSource
    DataSet = tblUlkeler
    Left = 136
    Top = 144
  end
  object tblPersonel: TpFIBDataSet
    UpdateSQL.Strings = (
      'UPDATE TANIM_PERSONEL'
      'SET '
      '    ADISOYADI = :ADISOYADI,'
      '    BOLUMU = :BOLUMU,'
      '    UNVANI = :UNVANI,'
      '    EVTEL = :EVTEL,'
      '    CEPTEL = :CEPTEL,'
      '    DAHILI = :DAHILI,'
      '    EVADRESI = :EVADRESI,'
      '    DURUMU = :DURUMU'
      'WHERE'
      '    ID = :OLD_ID'
      '    ')
    DeleteSQL.Strings = (
      'DELETE FROM'
      '    TANIM_PERSONEL'
      'WHERE'
      '        ID = :OLD_ID'
      '    ')
    InsertSQL.Strings = (
      'INSERT INTO TANIM_PERSONEL('
      '    ID,'
      '    ADISOYADI,'
      '    BOLUMU,'
      '    UNVANI,'
      '    EVTEL,'
      '    CEPTEL,'
      '    DAHILI,'
      '    EVADRESI,'
      '    DURUMU'
      ')'
      'VALUES('
      '    :ID,'
      '    :ADISOYADI,'
      '    :BOLUMU,'
      '    :UNVANI,'
      '    :EVTEL,'
      '    :CEPTEL,'
      '    :DAHILI,'
      '    :EVADRESI,'
      '    :DURUMU'
      ')')
    RefreshSQL.Strings = (
      'select * from tanim_personel'
      'where(  durumu=1'
      '     ) and (     TANIM_PERSONEL.ID = :OLD_ID'
      '     )'
      '     '
      '')
    SelectSQL.Strings = (
      'select * from tanim_personel'
      'where durumu=1 '
      ''
      'order by adisoyadi COLLATE PXW_TURK'
      ''
      '--select * from geneltanim'
      '--where sahibi=7')
    AutoUpdateOptions.UpdateTableName = 'TANIM_PERSONEL'
    AutoUpdateOptions.KeyFields = 'ID'
    AutoUpdateOptions.GeneratorName = 'GEN_TANIM_PERSONEL_ID'
    AutoUpdateOptions.WhenGetGenID = wgOnNewRecord
    Transaction = UniMainModule.trtanim
    Database = UniMainModule.db
    Left = 112
    Top = 224
  end
  object dsPersonel: TDataSource
    DataSet = tblPersonel
    Left = 163
    Top = 224
  end
  object frxReport1: TfrxReport
    Version = '5.2.12'
    DotMatrixReport = False
    IniFile = '\Software\Fast Reports'
    PreviewOptions.Buttons = [pbPrint, pbLoad, pbSave, pbExport, pbZoom, pbFind, pbOutline, pbPageSetup, pbTools, pbEdit, pbNavigator, pbExportQuick]
    PreviewOptions.Zoom = 1.000000000000000000
    PrintOptions.Printer = 'Varsay'#305'lan'
    PrintOptions.PrintOnSheet = 0
    ReportOptions.CreateDate = 45531.449640150460000000
    ReportOptions.LastChange = 45531.449640150460000000
    ScriptLanguage = 'PascalScript'
    ScriptText.Strings = (
      ''
      'begin'
      ''
      'end.')
    OnGetValue = frxReport1GetValue
    Left = 88
    Top = 368
    Datasets = <>
    Variables = <>
    Style = <>
    object Data: TfrxDataPage
      Height = 1000.000000000000000000
      Width = 1000.000000000000000000
    end
    object Page1: TfrxReportPage
      PaperWidth = 215.900000000000000000
      PaperHeight = 279.400000000000000000
      PaperSize = 1
      LeftMargin = 10.000000000000000000
      RightMargin = 10.000000000000000000
      TopMargin = 10.000000000000000000
      BottomMargin = 10.000000000000000000
      object ReportTitle1: TfrxReportTitle
        FillType = ftBrush
        Height = 22.677180000000000000
        Top = 18.897650000000000000
        Width = 740.787880000000000000
      end
      object MasterData1: TfrxMasterData
        FillType = ftBrush
        Height = 22.677180000000000000
        Top = 102.047310000000000000
        Width = 740.787880000000000000
        RowCount = 0
      end
      object PageFooter1: TfrxPageFooter
        FillType = ftBrush
        Height = 22.677180000000000000
        Top = 185.196970000000000000
        Width = 740.787880000000000000
        object Memo1: TfrxMemoView
          Left = 665.197280000000000000
          Width = 75.590600000000000000
          Height = 18.897650000000000000
          HAlign = haRight
          Memo.UTF8W = (
            '[Page#]')
        end
      end
    end
  end
  object frxDBDataset1: TfrxDBDataset
    UserName = 'rpNumune'
    CloseDataSource = False
    DataSource = dsPersonel
    BCDToCurrency = False
    Left = 104
    Top = 432
  end
  object frxBarCodeObject1: TfrxBarCodeObject
    Left = 168
    Top = 384
  end
  object frxPDFExport1: TfrxPDFExport
    UseFileCache = True
    ShowProgress = True
    OverwritePrompt = False
    DataOnly = False
    PrintOptimized = False
    Outline = False
    Background = False
    HTMLTags = True
    Quality = 95
    Transparency = False
    Author = 'FastReport'
    Subject = 'FastReport PDF export'
    ProtectionFlags = [ePrint, eModify, eCopy, eAnnot]
    HideToolbar = False
    HideMenubar = False
    HideWindowUI = False
    FitWindow = False
    CenterWindow = False
    PrintScaling = False
    PdfA = False
    Left = 184
    Top = 432
  end
  object tblFirma: TpFIBDataSet
    UpdateSQL.Strings = (
      'UPDATE URUNLER'
      'SET '
      '    URUNADI_T = :URUNADI_T,'
      '    URUNADI_A = :URUNADI_A,'
      '    URUNADI_I = :URUNADI_I,'
      '    URUNADI_L = :URUNADI_L,'
      '    DURUMU = :DURUMU,'
      '    PRODCODE = :PRODCODE,'
      '    KAYDEDEN = :KAYDEDEN,'
      '    DEGISTIREN = :DEGISTIREN,'
      '    KAYITZAMANI = :KAYITZAMANI,'
      '    DEGISTIRMEZAMANI = :DEGISTIRMEZAMANI,'
      '    ALTGRUPID = :ALTGRUPID,'
      '    DS_FIYATGRUBU = :DS_FIYATGRUBU,'
      '    GTIPNO = :GTIPNO'
      'WHERE'
      '    ID = :OLD_ID'
      '    ')
    DeleteSQL.Strings = (
      'DELETE FROM'
      '    URUNLER'
      'WHERE'
      '        ID = :OLD_ID'
      '    ')
    InsertSQL.Strings = (
      'INSERT INTO URUNLER('
      '    ID,'
      '    URUNADI_T,'
      '    URUNADI_A,'
      '    URUNADI_I,'
      '    URUNADI_L,'
      '    DURUMU,'
      '    PRODCODE,'
      '    KAYDEDEN,'
      '    DEGISTIREN,'
      '    KAYITZAMANI,'
      '    DEGISTIRMEZAMANI,'
      '    ALTGRUPID,'
      '    DS_FIYATGRUBU,'
      '    GTIPNO'
      ')'
      'VALUES('
      '    :ID,'
      '    :URUNADI_T,'
      '    :URUNADI_A,'
      '    :URUNADI_I,'
      '    :URUNADI_L,'
      '    :DURUMU,'
      '    :PRODCODE,'
      '    :KAYDEDEN,'
      '    :DEGISTIREN,'
      '    :KAYITZAMANI,'
      '    :DEGISTIRMEZAMANI,'
      '    :ALTGRUPID,'
      '    :DS_FIYATGRUBU,'
      '    :GTIPNO'
      ')')
    RefreshSQL.Strings = (
      'SELECT'
      '    *'
      'FROM'
      'urunler'
      ''
      ' WHERE '
      '        URUNLER.ID = :OLD_ID'
      '    ')
    SelectSQL.Strings = (
      'SELECT'
      '    *'
      'FROM'
      'geneltanim'
      'where sahibi=89')
    AutoUpdateOptions.UpdateTableName = 'SERTIFIKA'
    AutoUpdateOptions.KeyFields = 'ID'
    AutoUpdateOptions.GeneratorName = 'GEN_SERTIFIKA_ID'
    AutoUpdateOptions.WhenGetGenID = wgOnNewRecord
    Transaction = UniMainModule.trtanim
    Database = UniMainModule.db
    Left = 256
    Top = 384
  end
  object dsFirma: TDataSource
    DataSet = tblFirma
    Left = 312
    Top = 384
  end
  object dsAlindigiYer: TDataSource
    DataSet = tblAlindigiYer
    Left = 352
    Top = 448
  end
  object tblAlindigiYer: TpFIBDataSet
    UpdateSQL.Strings = (
      'UPDATE URUNLER'
      'SET '
      '    URUNADI_T = :URUNADI_T,'
      '    URUNADI_A = :URUNADI_A,'
      '    URUNADI_I = :URUNADI_I,'
      '    URUNADI_L = :URUNADI_L,'
      '    DURUMU = :DURUMU,'
      '    PRODCODE = :PRODCODE,'
      '    KAYDEDEN = :KAYDEDEN,'
      '    DEGISTIREN = :DEGISTIREN,'
      '    KAYITZAMANI = :KAYITZAMANI,'
      '    DEGISTIRMEZAMANI = :DEGISTIRMEZAMANI,'
      '    ALTGRUPID = :ALTGRUPID,'
      '    DS_FIYATGRUBU = :DS_FIYATGRUBU,'
      '    GTIPNO = :GTIPNO'
      'WHERE'
      '    ID = :OLD_ID'
      '    ')
    DeleteSQL.Strings = (
      'DELETE FROM'
      '    URUNLER'
      'WHERE'
      '        ID = :OLD_ID'
      '    ')
    InsertSQL.Strings = (
      'INSERT INTO URUNLER('
      '    ID,'
      '    URUNADI_T,'
      '    URUNADI_A,'
      '    URUNADI_I,'
      '    URUNADI_L,'
      '    DURUMU,'
      '    PRODCODE,'
      '    KAYDEDEN,'
      '    DEGISTIREN,'
      '    KAYITZAMANI,'
      '    DEGISTIRMEZAMANI,'
      '    ALTGRUPID,'
      '    DS_FIYATGRUBU,'
      '    GTIPNO'
      ')'
      'VALUES('
      '    :ID,'
      '    :URUNADI_T,'
      '    :URUNADI_A,'
      '    :URUNADI_I,'
      '    :URUNADI_L,'
      '    :DURUMU,'
      '    :PRODCODE,'
      '    :KAYDEDEN,'
      '    :DEGISTIREN,'
      '    :KAYITZAMANI,'
      '    :DEGISTIRMEZAMANI,'
      '    :ALTGRUPID,'
      '    :DS_FIYATGRUBU,'
      '    :GTIPNO'
      ')')
    RefreshSQL.Strings = (
      'SELECT'
      '    *'
      'FROM'
      'urunler'
      ''
      ' WHERE '
      '        URUNLER.ID = :OLD_ID'
      '    ')
    SelectSQL.Strings = (
      'SELECT'
      '    *'
      'FROM'
      'geneltanim'
      'where sahibi=88')
    AutoUpdateOptions.UpdateTableName = 'SERTIFIKA'
    AutoUpdateOptions.KeyFields = 'ID'
    AutoUpdateOptions.GeneratorName = 'GEN_SERTIFIKA_ID'
    AutoUpdateOptions.WhenGetGenID = wgOnNewRecord
    Transaction = UniMainModule.trtanim
    Database = UniMainModule.db
    Left = 272
    Top = 448
  end
  object frxBarCodeObject2: TfrxBarCodeObject
    Left = 96
    Top = 496
  end
  object tblParametreler: TpFIBDataSet
    UpdateSQL.Strings = (
      'UPDATE PARAMETRELER'
      'SET '
      '    REGNOBASINDAKIDEGER = :REGNOBASINDAKIDEGER,'
      '    SERTIFIKA_URUNSAYISI = :SERTIFIKA_URUNSAYISI,'
      '    EKDEKLARASYONBOYUT = :EKDEKLARASYONBOYUT,'
      '    ITHALATCI_ID_SISTEMI = :ITHALATCI_ID_SISTEMI,'
      '    KULLANIMSEKLI = :KULLANIMSEKLI,'
      '    VEZNEALINDISERI = :VEZNEALINDISERI,'
      '    NUMUNE_KAYIT_NO = :NUMUNE_KAYIT_NO,'
      '    MUHASEBEYETKILISI = :MUHASEBEYETKILISI,'
      '    GOREVLENDIRILENBIRIMAMIRI = :GOREVLENDIRILENBIRIMAMIRI,'
      '    ONAYLAYANBIRIMAMIRI = :ONAYLAYANBIRIMAMIRI,'
      '    BANKA_CLIENTID = :BANKA_CLIENTID,'
      '    BANKA_OKURL = :BANKA_OKURL,'
      '    BANKA_FAILURL = :BANKA_FAILURL,'
      '    BANKA_STORYKEY = :BANKA_STORYKEY,'
      '    BANKA_APIKULLANICIADI = :BANKA_APIKULLANICIADI,'
      '    BANKA_APISIFRE = :BANKA_APISIFRE,'
      '    LAB_DIS_NO = :LAB_DIS_NO,'
      '    LAB_IC_NO = :LAB_IC_NO'
      'WHERE'
      '    ID = :OLD_ID'
      '    ')
    DeleteSQL.Strings = (
      'DELETE FROM'
      '    PARAMETRELER'
      'WHERE'
      '        ID = :OLD_ID'
      '    ')
    InsertSQL.Strings = (
      'INSERT INTO PARAMETRELER('
      '    ID,'
      '    REGNOBASINDAKIDEGER,'
      '    SERTIFIKA_URUNSAYISI,'
      '    EKDEKLARASYONBOYUT,'
      '    ITHALATCI_ID_SISTEMI,'
      '    KULLANIMSEKLI,'
      '    VEZNEALINDISERI,'
      '    NUMUNE_KAYIT_NO,'
      '    MUHASEBEYETKILISI,'
      '    GOREVLENDIRILENBIRIMAMIRI,'
      '    ONAYLAYANBIRIMAMIRI,'
      '    BANKA_CLIENTID,'
      '    BANKA_OKURL,'
      '    BANKA_FAILURL,'
      '    BANKA_STORYKEY,'
      '    BANKA_APIKULLANICIADI,'
      '    BANKA_APISIFRE,'
      '    LAB_DIS_NO,'
      '    LAB_IC_NO'
      ')'
      'VALUES('
      '    :ID,'
      '    :REGNOBASINDAKIDEGER,'
      '    :SERTIFIKA_URUNSAYISI,'
      '    :EKDEKLARASYONBOYUT,'
      '    :ITHALATCI_ID_SISTEMI,'
      '    :KULLANIMSEKLI,'
      '    :VEZNEALINDISERI,'
      '    :NUMUNE_KAYIT_NO,'
      '    :MUHASEBEYETKILISI,'
      '    :GOREVLENDIRILENBIRIMAMIRI,'
      '    :ONAYLAYANBIRIMAMIRI,'
      '    :BANKA_CLIENTID,'
      '    :BANKA_OKURL,'
      '    :BANKA_FAILURL,'
      '    :BANKA_STORYKEY,'
      '    :BANKA_APIKULLANICIADI,'
      '    :BANKA_APISIFRE,'
      '    :LAB_DIS_NO,'
      '    :LAB_IC_NO'
      ')')
    RefreshSQL.Strings = (
      'select * from parametreler'
      ''
      ''
      ' WHERE '
      '        PARAMETRELER.ID = :OLD_ID'
      '    ')
    SelectSQL.Strings = (
      'select * from parametreler'
      '')
    AutoUpdateOptions.UpdateTableName = 'KULLANICILAR'
    AutoUpdateOptions.KeyFields = 'ID'
    AutoUpdateOptions.GeneratorName = 'GEN_KULLANICILAR_ID'
    AutoUpdateOptions.WhenGetGenID = wgOnNewRecord
    Transaction = UniMainModule.tr
    Database = UniMainModule.db
    Left = 376
    Top = 280
  end
end

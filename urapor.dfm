object frmRapor: TfrmRapor
  Left = 0
  Top = 0
  ClientHeight = 562
  ClientWidth = 1003
  Caption = 'Yazd'#305'r'
  WindowState = wsMaximized
  OldCreateOrder = False
  OnKeyDown = UniFormKeyDown
  KeyPreview = True
  MonitoredKeys.Keys = <>
  ClientEvents.ExtEvents.Strings = (
    
      '0-OnBeforerender=function window.OnBeforerender(sender)'#13#10'{'#13#10'    ' +
      ' sender.ui='#39'black-window'#39';'#13#10'}')
  PixelsPerInch = 96
  TextHeight = 13
  object UniURLFrame1: TUniURLFrame
    Left = 0
    Top = 41
    Width = 1003
    Height = 521
    Align = alClient
    TabOrder = 0
    ParentColor = False
    Color = clWindow
  end
  object UniPanel1: TUniPanel
    Left = 0
    Top = 0
    Width = 1003
    Height = 41
    Align = alTop
    TabOrder = 1
    BorderStyle = ubsNone
    Color = 16447736
    DesignSize = (
      1003
      41)
    object UniSpeedButton1: TUniSpeedButton
      Left = 920
      Top = 5
      Width = 78
      Height = 30
      Caption = 'Kapat'
      Anchors = [akTop, akRight]
      ParentColor = False
      Color = clBtnFace
      TabOrder = 1
      OnClick = UniSpeedButton1Click
    end
    object pnlSertifika: TUniPanel
      Left = 0
      Top = 0
      Width = 553
      Height = 41
      Align = alLeft
      TabOrder = 2
      BorderStyle = ubsNone
      Color = 16447736
    end
  end
end

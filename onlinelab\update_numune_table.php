<?php
require_once 'config/config.php';
require_once 'config/database.php';

try {
    $db = Database::getInstance();
    
    echo "<h2>Numune Tablosu Güncelleme</h2>";
    
    // Mevcut tablo yapısını kontrol et
    echo "<h3>Mevcut Tablo Yapısı:</h3>";
    $sql = "DESCRIBE lab_numuneler";
    $currentColumns = $db->fetchAll($sql);
    
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Alan</th><th>Tip</th><th>Null</th><th>Default</th></tr>";
    $existingFields = [];
    foreach ($currentColumns as $col) {
        $existingFields[] = strtoupper($col['Field']);
        echo "<tr><td>{$col['Field']}</td><td>{$col['Type']}</td><td>{$col['Null']}</td><td>{$col['Default']}</td></tr>";
    }
    echo "</table>";
    
    // Gerekli alanlar
    $requiredFields = [
        'NUMUNE_TURU' => 'VARCHAR(30)',
        'NUMUNE_ALT_TURU' => 'VARCHAR(20)',
        'GONDEREN' => 'VARCHAR(150)',
        'NUMUNE_ALINDIGIYER' => 'VARCHAR(150)',
        'NUMUNE_SAHIBI' => 'VARCHAR(300)',
        'ETIKET_NO' => 'VARCHAR(50)',
        'BASVURU_NO' => 'VARCHAR(50)',
        'MUHUR_NO' => 'VARCHAR(50)',
        'GONDEREN_PERSONEL' => 'VARCHAR(150)',
        'MENSEI' => 'VARCHAR(200)',
        'ULKE' => 'VARCHAR(200)',
        'URUN_ID' => 'INTEGER',
        'URUN_MIKTARI' => 'DOUBLE DEFAULT 0',
        'MIKTAR_BIRIM' => 'VARCHAR(30)',
        'GELIS_TARIHI' => 'DATETIME DEFAULT CURRENT_TIMESTAMP',
        'GELIS_SAATI' => 'TIME DEFAULT CURRENT_TIME',
        'TESLIM_EDEN' => 'VARCHAR(100)',
        'TESLIM_ALAN' => 'VARCHAR(100)',
        'UCRET_DURUMU' => 'VARCHAR(20) DEFAULT "Ödenmedi"',
        'DURUMU' => 'VARCHAR(30)',
        'ONHAZIRLIK' => 'VARCHAR(10) DEFAULT "Yapılmadı"',
        'BARKOD' => 'VARCHAR(30)',
        'SILINDI' => 'TINYINT DEFAULT 0',
        'ACIKLAMALAR' => 'VARCHAR(400)',
        'ANALIZ_SONUCU' => 'VARCHAR(20)',
        'FIRMA_ID' => 'INTEGER',
        'LOTNO' => 'VARCHAR(25)',
        'KULLANILAN_NUMUNE' => 'DOUBLE DEFAULT 0',
        'LAB_KAYITNO' => 'VARCHAR(50)',
        'MENSEI_2' => 'VARCHAR(200)'
    ];
    
    echo "<h3>Eksik Alanlar:</h3>";
    $missingFields = [];
    $alterQueries = [];
    
    foreach ($requiredFields as $field => $type) {
        if (!in_array($field, $existingFields) && !in_array(strtolower($field), array_map('strtolower', $existingFields))) {
            $missingFields[] = $field;
            $alterQueries[] = "ALTER TABLE lab_numuneler ADD COLUMN $field $type";
        }
    }
    
    if (empty($missingFields)) {
        echo "<p style='color: green;'>✅ Tüm alanlar mevcut!</p>";
    } else {
        echo "<ul>";
        foreach ($missingFields as $field) {
            echo "<li style='color: red;'>$field</li>";
        }
        echo "</ul>";
        
        echo "<h3>Eksik Alanları Ekleme:</h3>";
        foreach ($alterQueries as $query) {
            try {
                $db->execute($query);
                echo "<div style='color: green;'>✅ " . htmlspecialchars($query) . "</div>";
            } catch (Exception $e) {
                echo "<div style='color: red;'>❌ " . htmlspecialchars($query) . "<br>Hata: " . $e->getMessage() . "</div>";
            }
        }
    }
    
    // Alan adı eşleştirmelerini kontrol et
    echo "<h3>Alan Adı Eşleştirmeleri:</h3>";
    $fieldMappings = [
        'miktar' => 'URUN_MIKTARI',
        'birim' => 'MIKTAR_BIRIM',
        'aciklama' => 'ACIKLAMALAR'
    ];
    
    foreach ($fieldMappings as $formField => $dbField) {
        if (in_array(strtoupper($formField), $existingFields) && !in_array($dbField, $existingFields)) {
            echo "<p style='color: orange;'>⚠️ '$formField' alanı '$dbField' olarak değiştirilmeli</p>";
        }
    }
    
    echo "<h3>Güncellenmiş Tablo Yapısı:</h3>";
    $sql = "DESCRIBE lab_numuneler";
    $updatedColumns = $db->fetchAll($sql);
    
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Alan</th><th>Tip</th><th>Null</th><th>Default</th></tr>";
    foreach ($updatedColumns as $col) {
        echo "<tr><td>{$col['Field']}</td><td>{$col['Type']}</td><td>{$col['Null']}</td><td>{$col['Default']}</td></tr>";
    }
    echo "</table>";
    
    echo "<div style='margin-top: 20px; padding: 10px; background: #e8f5e8; border: 1px solid #4caf50;'>";
    echo "<strong>✅ Tablo güncelleme tamamlandı!</strong><br>";
    echo "Artık numune kaydetme işlemi çalışmalı.";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='color: red;'>Genel Hata: " . $e->getMessage() . "</div>";
}
?>

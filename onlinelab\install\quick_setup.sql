-- <PERSON><PERSON><PERSON><PERSON><PERSON> Kurulum SQL Scripti
-- Bu dosyayı phpMyAdmin'de çalıştırın

-- Veritabanını oluştur
CREATE DATABASE IF NOT EXISTS laboratuvar_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE laboratuvar_db;

-- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tablosu
CREATE TABLE IF NOT EXISTS kullanicilar (
    id INT AUTO_INCREMENT PRIMARY KEY,
    kullaniciadi VARCHAR(50) UNIQUE NOT NULL,
    sifresi VARCHAR(255) NOT NULL,
    adisoyadi VARCHAR(100) NOT NULL,
    email VARCHAR(100),
    telefon VARCHAR(20),
    aktif TINYINT(1) DEFAULT 1,
    kayit_tarihi TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    son_giris TIMESTAMP NULL,

    -- <PERSON><PERSON> al<PERSON>UMUNE_KABUL_YAPABILIR TINYINT(1) DEFAULT 0,
    NUMUNE_LISTESI_GOREBILIR TINYINT(1) DEFAULT 0,
    NUMUNE_ONHAZIRLIK_YAPABILIR TINYINT(1) DEFAULT 0,
    NUMUNE_SONUC_GIREBILIR TINYINT(1) DEFAULT 0,
    TANIMLAMA_YAPABILIR TINYINT(1) DEFAULT 0,
    NUMUNE_GENEL_GORUNUM TINYINT(1) DEFAULT 0,
    NUMUNE_ATAMA_YAPABILIR TINYINT(1) DEFAULT 0
);

-- Genel tanımlar tablosu
CREATE TABLE IF NOT EXISTS geneltanim (
    id INT AUTO_INCREMENT PRIMARY KEY,
    sahibi INT NOT NULL,
    izahat VARCHAR(255) NOT NULL,
    aciklama TEXT
);

-- Ürünler tablosu
CREATE TABLE IF NOT EXISTS urunler (
    id INT AUTO_INCREMENT PRIMARY KEY,
    urunadi_t VARCHAR(255) NOT NULL,
    aciklama TEXT,
    aktif TINYINT(1) DEFAULT 1
);

-- Laboratuvar birimleri tablosu
CREATE TABLE IF NOT EXISTS laboratuvar_birimler (
    id INT AUTO_INCREMENT PRIMARY KEY,
    birimadi VARCHAR(100) NOT NULL,
    aciklama TEXT,
    aktif TINYINT(1) DEFAULT 1
);

-- Numuneler ana tablosu
CREATE TABLE IF NOT EXISTS lab_numuneler (
    id INT AUTO_INCREMENT PRIMARY KEY,
    LAB_KAYITNO VARCHAR(50),
    numune_turu ENUM('İç Karantina', 'Dış Karantina') NOT NULL,
    numune_alt_turu VARCHAR(100),
    urun_id INT,
    firma_id INT,
    gonderen VARCHAR(255),
    gonderen_personel VARCHAR(100),
    mensei VARCHAR(100),
    basvuru_no VARCHAR(50),
    muhur_no VARCHAR(50),
    etiket_no VARCHAR(50),
    miktar DECIMAL(10,2),
    birim VARCHAR(20),
    aciklama TEXT,
    barkod VARCHAR(20),
    durumu VARCHAR(100) DEFAULT 'Numune Kabul',
    ucret_durumu VARCHAR(50),
    gelis_tarihi TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    silindi TINYINT(1) DEFAULT 0
);

-- Laboratuvar atamalar tablosu
CREATE TABLE IF NOT EXISTS lab_atamalar (
    id INT AUTO_INCREMENT PRIMARY KEY,
    numune_id INT NOT NULL,
    birim_id INT NOT NULL,
    atama_tarihi TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    analiz_tarihi TIMESTAMP NULL,
    analiz_sonucu VARCHAR(100),
    analiz_methodu VARCHAR(255),
    atayan_kullanici INT
);

-- Sonuç etmenleri tablosu
CREATE TABLE IF NOT EXISTS lab_sonuc_etmenler (
    id INT AUTO_INCREMENT PRIMARY KEY,
    numune_id INT NOT NULL,
    ETMEN_ADI VARCHAR(255) NOT NULL,
    BULASIKMI ENUM('Evet', 'Hayır') DEFAULT 'Hayır',
    deger VARCHAR(100),
    birim VARCHAR(50)
);

-- Log tablosu
CREATE TABLE IF NOT EXISTS lab_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    numune_id INT,
    islem VARCHAR(255) NOT NULL,
    islemi_yapan VARCHAR(100),
    onceki_deger TEXT,
    tarih TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Parametreler tablosu
CREATE TABLE IF NOT EXISTS parametreler (
    id INT AUTO_INCREMENT PRIMARY KEY,
    LAB_Ic_NO INT DEFAULT 1,
    LAB_dis_NO INT DEFAULT 1
);

-- Varsayılan veriler
INSERT IGNORE INTO parametreler (LAB_Ic_NO, LAB_dis_NO) VALUES (1, 1);

-- Admin kullanıcısı (şifre: admin123)
INSERT IGNORE INTO kullanicilar (
    kullaniciadi, sifresi, adisoyadi,
    NUMUNE_KABUL_YAPABILIR, NUMUNE_LISTESI_GOREBILIR,
    NUMUNE_ONHAZIRLIK_YAPABILIR, NUMUNE_SONUC_GIREBILIR,
    TANIMLAMA_YAPABILIR, NUMUNE_GENEL_GORUNUM, NUMUNE_ATAMA_YAPABILIR
) VALUES (
    'admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Sistem Yöneticisi',
    1, 1, 1, 1, 1, 1, 1
);

-- Örnek ürünler
INSERT IGNORE INTO urunler (id, urunadi_t) VALUES
(1, 'Buğday'),
(2, 'Mısır'),
(3, 'Arpa'),
(4, 'Pirinç'),
(5, 'Fasulye');

-- Örnek firmalar
INSERT IGNORE INTO geneltanim (id, sahibi, izahat) VALUES
(1, 89, 'ABC Gıda San. Tic. Ltd. Şti.'),
(2, 89, 'XYZ İhracat A.Ş.'),
(3, 89, 'DEF Tarım Ürünleri Ltd.');

-- Örnek ülkeler
INSERT IGNORE INTO geneltanim (id, sahibi, izahat) VALUES
(10, 85, 'Türkiye'),
(11, 85, 'Almanya'),
(12, 85, 'Fransa');

-- Örnek laboratuvar birimleri
INSERT IGNORE INTO laboratuvar_birimler (id, birimadi) VALUES
(1, 'Mikrobiyoloji'),
(2, 'Kimya'),
(3, 'Pestisit');

-- İç karantina türleri
INSERT IGNORE INTO geneltanim (id, sahibi, izahat) VALUES
(20, 180, 'İthalat'),
(21, 180, 'İhracat');

-- Dış karantina türleri
INSERT IGNORE INTO geneltanim (id, sahibi, izahat) VALUES
(30, 181, 'Transit'),
(31, 181, 'Gümrük');

SELECT 'Veritabanı başarıyla kuruldu!' as Mesaj;

<?php
/**
 * Numune Controller Sınıfı
 */

class NumuneController {
    private $numuneModel;
    private $urunModel;
    private $genelTanimModel;
    private $birimModel;
    
    public function __construct() {
        AuthController::checkSession();
        
        $this->numuneModel = new Numune();
        $this->urunModel = new Urun();
        $this->genelTanimModel = new GenelTanim();
        $this->birimModel = new LaboratuvarBirim();
    }
    
    // Numune listesi
    public function index() {
        AuthController::requirePermission('NUMUNE_LISTESI_GOREBILIR');
        
        $page = $_GET['page_num'] ?? 1;
        $filters = [
            'numune_turu' => $_GET['numune_turu'] ?? '',
            'durumu' => $_GET['durumu'] ?? '',
            'lab_kayitno' => $_GET['lab_kayitno'] ?? '',
            'gonderen' => $_GET['gonderen'] ?? '',
            'baslangic_tarihi' => $_GET['baslangic_tarihi'] ?? '',
            'bitis_tarihi' => $_GET['bitis_tarihi'] ?? ''
        ];
        
        $result = $this->numuneModel->getNumuneList($page, $filters);
        
        // Dropdown verileri
        $data = [
            'numuneler' => $result['numuneler'],
            'pagination' => [
                'total' => $result['total'],
                'pages' => $result['pages'],
                'current_page' => $result['current_page']
            ],
            'filters' => $filters,
            'numune_turleri' => NUMUNE_TURLERI,
            'numune_durumlari' => NUMUNE_DURUMLARI,
            'user' => AuthController::getCurrentUser()
        ];
        
        require_once 'views/numune/liste.php';
    }
    
    // Numune kabul formu
    public function kabul() {
        AuthController::requirePermission('NUMUNE_KABUL_YAPABILIR');
        
        $id = $_GET['id'] ?? 0;
        $numune = null;
        $logs = [];

        if ($id > 0) {
            $numune = $this->numuneModel->getNumuneDetails($id);
            if (!$numune) {
                $_SESSION['error'] = ERROR_MESSAGES['record_not_found'];
                header('Location: index.php?page=numune-listesi');
                exit;
            }

            // Düzenleme yetkisi kontrolü - sadece "Numune Kabul" durumunda düzenlenebilir
            if (!in_array($numune['durumu'], ['Numune Kabul'])) {
                $_SESSION['error'] = 'Bu numune artık düzenlenemez. Sadece detay görüntüleyebilirsiniz.';
                header('Location: index.php?page=numune-detay&id=' . $id);
                exit;
            }

            // İşlem geçmişini getir
            $logSql = "SELECT islem, islemi_yapan as kullanici, onceki_deger, tarih
                       FROM lab_log
                       WHERE numune_id = ?
                       ORDER BY tarih DESC";
            $logs = $this->numuneModel->fetchAll($logSql, [$id]);

            // Debug: Numune verilerini logla
            error_log("Numune düzenleme verileri: " . json_encode($numune));
        }
        
        // Dropdown verileri
        $data = [
            'numune' => $numune,
            'logs' => $logs,
            'urunler' => $this->urunModel->getOptionsForSelect(),
            'firmalar' => $this->genelTanimModel->getOptionsForSelect(GenelTanim::FIRMALAR),
            'ulkeler' => $this->genelTanimModel->getOptionsForSelect(GenelTanim::ULKELER),
            'alindigiyer' => $this->genelTanimModel->getOptionsForSelect(GenelTanim::ALINDIGIYER),
            'ic_karantina_turleri' => $this->genelTanimModel->getOptionsForSelect(GenelTanim::IC_KARANTINA_TURLERI),
            'dis_karantina_turleri' => $this->genelTanimModel->getOptionsForSelect(GenelTanim::DIS_KARANTINA_TURLERI),
            'user' => AuthController::getCurrentUser()
        ];
        
        require_once 'views/numune/kabul.php';
    }

    // Numune detay görüntüleme
    public function detay() {
        AuthController::requirePermission('NUMUNE_LISTESI_GOREBILIR');

        $id = $_GET['id'] ?? 0;

        if ($id <= 0) {
            $_SESSION['error'] = 'Geçersiz numune ID.';
            header('Location: index.php?page=numune-listesi');
            exit;
        }

        // Numune detaylarını getir
        $numune = $this->numuneModel->getNumuneDetails($id);
        if (!$numune) {
            $_SESSION['error'] = ERROR_MESSAGES['record_not_found'];
            header('Location: index.php?page=numune-listesi');
            exit;
        }

        // İşlem geçmişini getir
        $logSql = "SELECT islem, islemi_yapan as kullanici, onceki_deger, tarih
                   FROM lab_log
                   WHERE numune_id = ?
                   ORDER BY tarih DESC";
        $logs = $this->numuneModel->fetchAll($logSql, [$id]);

        $data = [
            'numune' => $numune,
            'logs' => $logs
        ];

        require_once 'views/numune/detay.php';
    }

    // Numune kabul kaydet
    public function saveKabul() {
        AuthController::requirePermission('NUMUNE_KABUL_YAPABILIR');
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Location: index.php?page=numune-kabul');
            exit;
        }
        
        $data = [
            'id' => $_POST['id'] ?? 0,
            'NUMUNE_TURU' => $_POST['numune_turu'] ?? '',
            'NUMUNE_ALT_TURU' => $_POST['numune_alt_turu'] ?? '',
            'LAB_KAYITNO' => $_POST['LAB_KAYITNO'] ?? '',
            'GONDEREN' => $_POST['gonderen'] ?? '',
            'GONDEREN_PERSONEL' => $_POST['gonderen_personel'] ?? '',
            'FIRMA_ID' => !empty($_POST['firma_id']) ? $_POST['firma_id'] : null,
            'URUN_ID' => !empty($_POST['urun_id']) ? $_POST['urun_id'] : null,
            'MENSEI' => $_POST['mensei'] ?? '',
            'ULKE' => $_POST['ulke'] ?? '',
            'NUMUNE_ALINDIGIYER' => $_POST['numune_alindigiyer'] ?? '',
            'NUMUNE_SAHIBI' => $_POST['numune_sahibi'] ?? '',
            'BASVURU_NO' => $_POST['basvuru_no'] ?? '',
            'MUHUR_NO' => $_POST['muhur_no'] ?? '',
            'ETIKET_NO' => $_POST['etiket_no'] ?? '',
            'BARKOD' => $_POST['barkod'] ?? '',
            'LOTNO' => $_POST['lotno'] ?? '',
            'URUN_MIKTARI' => !empty($_POST['miktar']) ? floatval($_POST['miktar']) : 0,
            'MIKTAR_BIRIM' => $_POST['birim'] ?? '',
            'GELIS_SAATI' => date('H:i:s'),
            'TESLIM_EDEN' => $_POST['teslim_eden'] ?? '',
            'TESLIM_ALAN' => $_POST['teslim_alan'] ?? '',
            'UCRET_DURUMU' => $_POST['ucret_durumu'] ?? 'Ödenmedi',
            'ACIKLAMALAR' => $_POST['aciklama'] ?? ''
        ];

        // Boş string'leri null'a çevir (veritabanı için)
        foreach ($data as $key => $value) {
            if ($value === '' && in_array($key, ['firma_id', 'urun_id'])) {
                $data[$key] = null;
            }
        }

        // İç Karantina için başvuru no'yu temizle
        if ($data['NUMUNE_TURU'] === 'İç Karantina') {
            $data['BASVURU_NO'] = '';
        }

        // Debug: Form verilerini logla
        error_log("POST verileri: " . json_encode($_POST));
        error_log("İşlenmiş form verileri: " . json_encode($data));

        $result = $this->numuneModel->saveNumune($data);
        
        if ($result['success']) {
            $_SESSION['success'] = SUCCESS_MESSAGES['save_success'];
            header('Location: index.php?page=numune-listesi');
        } else {
            if (isset($result['errors'])) {
                $_SESSION['form_errors'] = $result['errors'];
                $_SESSION['form_data'] = $data;
            } else {
                // Debug için detaylı hata mesajı
                $errorDetails = $result['error'] ?? ERROR_MESSAGES['save_error'];
                if (isset($result['sql_error'])) {
                    $errorDetails .= ' SQL Error: ' . $result['sql_error'];
                }
                if (isset($result['debug_data'])) {
                    $errorDetails .= ' Debug: ' . json_encode($result['debug_data']);
                }
                $_SESSION['error'] = $errorDetails;
            }
            
            $redirectUrl = $data['id'] > 0 ? 
                "index.php?page=numune-kabul&id={$data['id']}" : 
                "index.php?page=numune-kabul";
            header("Location: $redirectUrl");
        }
        exit;
    }
    
    // Numune düzenleme
    public function edit($id) {
        AuthController::requirePermission('NUMUNE_KABUL_YAPABILIR');
        
        $numune = $this->numuneModel->getNumuneDetails($id);
        if (!$numune) {
            $_SESSION['error'] = ERROR_MESSAGES['record_not_found'];
            header('Location: index.php?page=numune-listesi');
            exit;
        }
        
        // Kabul formunu göster
        $_GET['id'] = $id;
        $this->kabul();
    }
    
    // Numune silme
    public function delete($id) {
        AuthController::requirePermission('NUMUNE_KABUL_YAPABILIR');
        
        $numune = $this->numuneModel->getById($id);
        if (!$numune) {
            $_SESSION['error'] = ERROR_MESSAGES['record_not_found'];
        } else {
            $result = $this->numuneModel->softDelete($id);
            if ($result) {
                $_SESSION['success'] = SUCCESS_MESSAGES['delete_success'];
            } else {
                $_SESSION['error'] = ERROR_MESSAGES['delete_error'];
            }
        }
        
        header('Location: index.php?page=numune-listesi');
        exit;
    }
    
    // Numune sonuç girişi
    public function sonuc($id) {
        AuthController::requirePermission('NUMUNE_SONUC_GIREBILIR');
        
        $numune = $this->numuneModel->getNumuneDetails($id);
        if (!$numune) {
            $_SESSION['error'] = ERROR_MESSAGES['record_not_found'];
            header('Location: index.php?page=numune-listesi');
            exit;
        }
        
        // Atamalar ve sonuçlar
        $atamalar = $this->numuneModel->getAtamalar($id);
        
        // Etmen sonuçları
        $sql = "SELECT * FROM lab_sonuc_etmenler WHERE numune_id = ? ORDER BY ETMEN_ADI";
        $etmenSonuclari = $this->numuneModel->fetchAll($sql, [$id]);
        
        $data = [
            'numune' => $numune,
            'atamalar' => $atamalar,
            'etmen_sonuclari' => $etmenSonuclari,
            'etmenler' => $this->genelTanimModel->getEtmenler(),
            'user' => AuthController::getCurrentUser()
        ];
        
        require_once 'views/numune/sonuc.php';
    }
    
    // Etmen sonucu kaydet
    public function saveSonuc() {
        AuthController::requirePermission('NUMUNE_SONUC_GIREBILIR');
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Location: index.php?page=numune-listesi');
            exit;
        }
        
        $numuneId = $_POST['numune_id'] ?? 0;
        $etmenAdi = $_POST['etmen_adi'] ?? '';
        $bulasikMi = $_POST['bulasik_mi'] ?? 'Hayır';
        $deger = $_POST['deger'] ?? '';
        $birim = $_POST['birim'] ?? '';
        
        if ($numuneId > 0 && !empty($etmenAdi)) {
            try {
                // Önce mevcut kaydı kontrol et
                $sql = "SELECT id FROM lab_sonuc_etmenler WHERE numune_id = ? AND ETMEN_ADI = ?";
                $existing = $this->numuneModel->fetchOne($sql, [$numuneId, $etmenAdi]);
                
                if ($existing) {
                    // Güncelle
                    $sql = "UPDATE lab_sonuc_etmenler 
                            SET BULASIKMI = ?, deger = ?, birim = ? 
                            WHERE id = ?";
                    $this->numuneModel->query($sql, [$bulasikMi, $deger, $birim, $existing['id']]);
                } else {
                    // Yeni kayıt
                    $sql = "INSERT INTO lab_sonuc_etmenler (numune_id, ETMEN_ADI, BULASIKMI, deger, birim) 
                            VALUES (?, ?, ?, ?, ?)";
                    $this->numuneModel->query($sql, [$numuneId, $etmenAdi, $bulasikMi, $deger, $birim]);
                }
                
                $_SESSION['success'] = 'Etmen sonucu kaydedildi.';
            } catch (Exception $e) {
                $_SESSION['error'] = 'Kayıt sırasında hata oluştu.';
            }
        } else {
            $_SESSION['error'] = 'Gerekli alanlar eksik.';
        }
        
        header("Location: index.php?page=numune-sonuc&id=$numuneId");
        exit;
    }

    // Etmen sonucu sil
    public function deleteEtmen() {
        AuthController::requirePermission('NUMUNE_SONUC_GIREBILIR');

        $id = $_GET['id'] ?? 0;
        $numuneId = $_GET['numune_id'] ?? 0;

        if ($id > 0) {
            try {
                $sql = "DELETE FROM lab_sonuc_etmenler WHERE id = ?";
                $this->numuneModel->query($sql, [$id]);
                $_SESSION['success'] = 'Etmen sonucu silindi.';
            } catch (Exception $e) {
                $_SESSION['error'] = 'Silme işlemi sırasında hata oluştu.';
            }
        }

        header("Location: index.php?page=numune-sonuc&id=$numuneId");
        exit;
    }

    // Numune durumu güncelle
    public function updateDurum() {
        AuthController::requirePermission('NUMUNE_SONUC_GIREBILIR');

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Location: index.php?page=numune-listesi');
            exit;
        }

        $numuneId = $_POST['numune_id'] ?? 0;
        $durum = $_POST['durum'] ?? '';

        if ($numuneId > 0 && !empty($durum)) {
            try {
                $sql = "UPDATE lab_numuneler SET durumu = ? WHERE id = ?";
                $this->numuneModel->query($sql, [$durum, $numuneId]);

                // Eğer analiz tamamlandı ise, atama tarihlerini güncelle
                if ($durum === 'Analiz Tamamlandı') {
                    $sql = "UPDATE lab_atamalar
                            SET analiz_tarihi = NOW()
                            WHERE numune_id = ? AND analiz_tarihi IS NULL";
                    $this->numuneModel->query($sql, [$numuneId]);
                }

                $_SESSION['success'] = 'Numune durumu güncellendi.';
            } catch (Exception $e) {
                $_SESSION['error'] = 'Güncelleme sırasında hata oluştu.';
            }
        }

        header("Location: index.php?page=numune-sonuc&id=$numuneId");
        exit;
    }

    // Ödeme durumu toggle
    public function toggleOdeme() {
        AuthController::requirePermission('NUMUNE_KABUL_YAPABILIR');

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            echo json_encode(['success' => false, 'message' => 'Geçersiz istek']);
            exit;
        }

        $numuneId = $_POST['numune_id'] ?? 0;

        if ($numuneId > 0) {
            try {
                // Mevcut durumu al
                $sql = "SELECT UCRET_DURUMU FROM lab_numuneler WHERE id = ?";
                $current = $this->numuneModel->fetch($sql, [$numuneId]);

                if ($current) {
                    // Durumu toggle et
                    $newStatus = ($current['UCRET_DURUMU'] === 'Ödendi') ? 'Ödenmedi' : 'Ödendi';

                    // Güncelle
                    $sql = "UPDATE lab_numuneler SET UCRET_DURUMU = ? WHERE id = ?";
                    $this->numuneModel->query($sql, [$newStatus, $numuneId]);

                    // Log ekle
                    $this->numuneModel->addLog($numuneId, "Ödeme durumu değiştirildi: {$current['UCRET_DURUMU']} → $newStatus", $_SESSION['user_name'] ?? 'Sistem');

                    echo json_encode([
                        'success' => true,
                        'new_status' => $newStatus,
                        'badge_class' => get_payment_badge_class($newStatus)
                    ]);
                } else {
                    echo json_encode(['success' => false, 'message' => 'Kayıt bulunamadı']);
                }
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'message' => 'Güncelleme hatası: ' . $e->getMessage()]);
            }
        } else {
            echo json_encode(['success' => false, 'message' => 'Geçersiz numune ID']);
        }
        exit;
    }
}
?>

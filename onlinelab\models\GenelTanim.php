<?php
/**
 * Genel Tanım Model Sınıfı
 * Ülkeler, Firmalar, Müdürlükler vb. i<PERSON><PERSON> kullan<PERSON>lır
 */

class GenelTanim extends BaseModel {
    protected $table = 'geneltanim';
    
    // <PERSON><PERSON> kodlarına göre tanımlar
    const ULKELER = 85;
    const MUDURLULER = 80;
    const FIRMALAR = 89;
    const ALINDIGIYER = 88;
    const IC_KARANTINA_TURLERI = 180;
    const DIS_KARANTINA_TURLERI = 181;
    const ETMENLER = 184;
    
    // Sahibi koduna göre kayıtları getir
    public function getBySahibi($sahibi, $orderBy = 'izahat') {
        $sql = "SELECT * FROM {$this->table} WHERE sahibi = ? ORDER BY $orderBy";
        return $this->db->fetchAll($sql, [$sahibi]);
    }
    
    // Ülkeleri getir
    public function getUlkeler() {
        return $this->getBySahibi(self::ULKELER);
    }
    
    // Müdürlükleri getir
    public function getMudurluler() {
        return $this->getBySahibi(self::MUDURLULER);
    }
    
    // Firmaları getir
    public function getFirmalar() {
        return $this->getBySahibi(self::FIRMALAR);
    }
    
    // Alındığı yerleri getir
    public function getAlindigiYerler() {
        return $this->getBySahibi(self::ALINDIGIYER);
    }
    
    // İç karantina türlerini getir
    public function getIcKarantinaTurleri() {
        return $this->getBySahibi(self::IC_KARANTINA_TURLERI);
    }
    
    // Dış karantina türlerini getir
    public function getDisKarantinaTurleri() {
        return $this->getBySahibi(self::DIS_KARANTINA_TURLERI);
    }
    
    // Etmenleri getir
    public function getEtmenler() {
        return $this->getBySahibi(self::ETMENLER);
    }
    
    // Yeni tanım ekle
    public function addTanim($sahibi, $izahat, $aciklama = '') {
        $data = [
            'sahibi' => $sahibi,
            'izahat' => $izahat,
            'aciklama' => $aciklama
        ];
        
        // Aynı sahibi ve izahat var mı kontrol et
        if ($this->exists('sahibi = ? AND izahat = ?', [$sahibi, $izahat])) {
            return ['success' => false, 'error' => 'Bu tanım zaten mevcut.'];
        }
        
        try {
            $id = $this->insert($data);
            return ['success' => true, 'id' => $id];
        } catch (Exception $e) {
            return ['success' => false, 'error' => 'Kayıt sırasında hata oluştu.'];
        }
    }
    
    // Tanım güncelle
    public function updateTanim($id, $izahat, $aciklama = '') {
        $data = [
            'izahat' => $izahat,
            'aciklama' => $aciklama
        ];
        
        // Mevcut kaydı al
        $current = $this->getById($id);
        if (!$current) {
            return ['success' => false, 'error' => 'Kayıt bulunamadı.'];
        }
        
        // Aynı sahibi ve izahat var mı kontrol et (kendisi hariç)
        if ($this->exists('sahibi = ? AND izahat = ? AND id != ?', [$current['sahibi'], $izahat, $id])) {
            return ['success' => false, 'error' => 'Bu tanım zaten mevcut.'];
        }
        
        try {
            $this->update($id, $data);
            return ['success' => true];
        } catch (Exception $e) {
            return ['success' => false, 'error' => 'Güncelleme sırasında hata oluştu.'];
        }
    }
    
    // Sahibi koduna göre liste (sayfalama ile)
    public function getListBySahibi($sahibi, $page = 1, $search = '') {
        $where = 'sahibi = ?';
        $params = [$sahibi];
        
        if (!empty($search)) {
            $where .= ' AND izahat LIKE ?';
            $params[] = "%$search%";
        }
        
        $records = $this->getPaginated($page, RECORDS_PER_PAGE, $where, $params, 'izahat');
        $total = $this->getCount($where, $params);
        
        return [
            'records' => $records,
            'total' => $total,
            'pages' => ceil($total / RECORDS_PER_PAGE),
            'current_page' => $page
        ];
    }
    
    // Sahibi koduna göre dropdown için options
    public function getOptionsForSelect($sahibi) {
        $records = $this->getBySahibi($sahibi);
        $options = [];
        
        foreach ($records as $record) {
            $options[$record['id']] = $record['izahat'];
        }
        
        return $options;
    }
    
    // Sahibi kodlarının açıklamalarını getir
    public function getSahibiAciklama($sahibi) {
        $aciklamalar = [
            self::ULKELER => 'Ülkeler',
            self::MUDURLULER => 'Müdürlükler',
            self::FIRMALAR => 'Firmalar',
            self::ALINDIGIYER => 'Alındığı Yerler',
            self::IC_KARANTINA_TURLERI => 'İç Karantina Türleri',
            self::DIS_KARANTINA_TURLERI => 'Dış Karantina Türleri',
            self::ETMENLER => 'Etmenler'
        ];
        
        return $aciklamalar[$sahibi] ?? 'Bilinmeyen';
    }
    
    // Tüm sahibi kodlarını getir
    public function getAllSahibiKodlari() {
        return [
            self::ULKELER => 'Ülkeler',
            self::MUDURLULER => 'Müdürlükler',
            self::FIRMALAR => 'Firmalar',
            self::ALINDIGIYER => 'Alındığı Yerler',
            self::IC_KARANTINA_TURLERI => 'İç Karantina Türleri',
            self::DIS_KARANTINA_TURLERI => 'Dış Karantina Türleri',
            self::ETMENLER => 'Etmenler'
        ];
    }
}
?>

<?php
/**
 * Online Laboratuvar Yönetim Sistemi
 * MV (Model-View) Yapısı
 * Ana Giriş <PERSON>
 */

// Hata raporlamayı aç (geliştirme aşamasında)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Oturum başlat
session_start();

// Temel konfigürasyon
require_once 'config/config.php';
require_once 'config/database.php';

// Autoloader - Model ve diğer sınıfları otomatik yükle
spl_autoload_register(function ($class) {
    $paths = [
        'models/',
        'controllers/',
        'core/'
    ];
    
    foreach ($paths as $path) {
        $file = $path . $class . '.php';
        if (file_exists($file)) {
            require_once $file;
            return;
        }
    }
});

// Router - URL yönlendirme
$page = $_GET['page'] ?? 'dashboard';
$action = $_GET['action'] ?? 'index';

// G<PERSON>ş kontrolü
if (!isset($_SESSION['user_id']) && $page !== 'login') {
    $page = 'login';
}

// Sayfa yönlendirme
switch ($page) {
    case 'login':
        if ($action === 'authenticate') {
            require_once 'controllers/AuthController.php';
            $auth = new AuthController();
            $auth->authenticate();
        } else {
            require_once 'views/login.php';
        }
        break;
        
    case 'logout':
        require_once 'controllers/AuthController.php';
        $auth = new AuthController();
        $auth->logout();
        break;
        
    case 'dashboard':
        require_once 'controllers/DashboardController.php';
        $controller = new DashboardController();
        $controller->index();
        break;
        
    case 'numune-listesi':
        require_once 'controllers/NumuneController.php';
        $controller = new NumuneController();
        if ($action === 'delete') {
            $controller->delete($_GET['id'] ?? 0);
        } elseif ($action === 'detay') {
            $controller->detay();
        } else {
            $controller->index();
        }
        break;

    case 'numune-detay':
        require_once 'controllers/NumuneController.php';
        $controller = new NumuneController();
        $controller->detay();
        break;
        
    case 'numune-kabul':
        require_once 'controllers/NumuneController.php';
        $controller = new NumuneController();
        if ($action === 'save') {
            $controller->saveKabul();
        } else {
            $controller->kabul();
        }
        break;
        
    case 'numune-sonuc':
        require_once 'controllers/NumuneController.php';
        $controller = new NumuneController();
        if ($action === 'save') {
            $controller->saveSonuc();
        } elseif ($action === 'delete-etmen') {
            $controller->deleteEtmen();
        } elseif ($action === 'update-durum') {
            $controller->updateDurum();
        } elseif ($action === 'toggle-odeme') {
            $controller->toggleOdeme();
        } elseif ($action === 'etmen') {
            $controller->etmenSonuc($_GET['id'] ?? 0);
        } else {
            $controller->sonuc($_GET['id'] ?? 0);
        }
        break;
        
    case 'onhazirlik':
        require_once 'controllers/OnhazirlikController.php';
        $controller = new OnhazirlikController();
        if ($action === 'atama') {
            $controller->birimAtama($_GET['id'] ?? 0);
        } elseif ($action === 'save-atama') {
            $controller->saveAtama();
        } elseif ($action === 'toplu-atama') {
            $controller->topluAtama();
        } elseif ($action === 'update-durum') {
            $controller->updateDurum();
        } elseif ($action === 'delete-atama') {
            $controller->deleteAtama();
        } elseif ($action === 'kabul-et') {
            $controller->kabulEt();
        } elseif ($action === 'hazirlik-yapildi') {
            $controller->hazirlikYapildi();
        } elseif ($action === 'geri-gonder') {
            $controller->geriGonder();
        } elseif ($action === 'stats') {
            $controller->getStats();
        } else {
            $controller->index();
        }
        break;

    case 'atama':
        require_once 'controllers/AtamaController.php';
        $controller = new AtamaController();
        if ($action === 'ata') {
            $controller->atamaYap();
        } elseif ($action === 'toplu-atama') {
            $controller->topluAtama();
        } else {
            $controller->index();
        }
        break;

    case 'sonuc-listesi':
        require_once 'controllers/SonucListesiController.php';
        $controller = new SonucListesiController();
        if ($action === 'gir') {
            $controller->sonucGir($_GET['id'] ?? 0);
        } elseif ($action === 'kaydet') {
            $controller->sonucKaydet();
        } elseif ($action === 'analiz-kabul') {
            $controller->analizKabulEt();
        } elseif ($action === 'analiz-tamamla') {
            $controller->analizTamamla();
        } elseif ($action === 'detay') {
            $controller->detay($_GET['id'] ?? 0);
        } elseif ($action === 'export') {
            $controller->export();
        } else {
            $controller->index();
        }
        break;

    case 'tanimlar':
        require_once 'controllers/TanimController.php';
        $controller = new TanimController();
        if ($action === 'urunler') {
            $controller->urunler();
        } elseif ($action === 'birimler') {
            $controller->birimler();
        } elseif ($action === 'personel') {
            $controller->personel();
        } elseif ($action === 'save') {
            $controller->save();
        } else {
            $controller->index();
        }
        break;
        
    case 'kullanicilar':
        require_once 'controllers/KullaniciController.php';
        $controller = new KullaniciController();
        if ($action === 'add') {
            $controller->add();
        } elseif ($action === 'edit') {
            $controller->edit($_GET['id'] ?? 0);
        } elseif ($action === 'save') {
            $controller->save();
        } else {
            $controller->index();
        }
        break;
        
    case 'raporlar':
        require_once 'controllers/RaporController.php';
        $controller = new RaporController();
        $controller->index();
        break;
        
    case 'log':
    case 'log-listesi':
        require_once 'controllers/LogController.php';
        $controller = new LogController();
        if ($action === 'export') {
            $controller->export();
        } else {
            $controller->index();
        }
        break;

    case 'log-detay':
        require_once 'controllers/LogController.php';
        $controller = new LogController();
        $id = $_GET['id'] ?? 0;
        $controller->details($id);
        break;
        
    default:
        require_once 'views/404.php';
        break;
}
?>

<?php
/**
 * <PERSON><PERSON> Hazırlık Controller Sınıfı
 * Numune birim atama ve ön hazırlık işlemleri
 */

class OnhazirlikController {
    private $numuneModel;
    private $birimModel;
    
    public function __construct() {
        AuthController::checkSession();
        
        $this->numuneModel = new Numune();
        $this->birimModel = new LaboratuvarBirim();
    }
    
    // Ön hazırlık ana sayfası
    public function index() {
        AuthController::requirePermission('NUMUNE_ONHAZIRLIK_YAPABILIR');

        $page = $_GET['page_num'] ?? 1;

        // Filtreleme parametreleri
        $durumFilter = $_GET['durum_filter'] ?? 'yapilmayanlar';
        $baslangicTarihi = $_GET['baslangic_tarihi'] ?? '';
        $bitisTarihi = $_GET['bitis_tarihi'] ?? '';
        $kayitNo = $_GET['kayit_no'] ?? '';
        $muhurNo = $_GET['muhur_no'] ?? '';

        // WHERE koşulları
        $whereConditions = ["n.silindi = 0"];
        $params = [];

        // Durum filtresi
        if ($durumFilter === 'yapilmayanlar') {
            $whereConditions[] = "n.durumu = 'Ön Hazırlık'";
            $whereConditions[] = "a.durum IN ('Atandi', 'Kabul Edildi')";
        } elseif ($durumFilter === 'yapilanlar') {
            $whereConditions[] = "a.durum LIKE '%Hazirlik%'";
            // Hazırlık yapıldı kayıtlar hem Ön Hazırlık hem de Analiz Aşamasında olabilir
            $whereConditions[] = "(n.durumu = 'Ön Hazırlık' OR n.durumu = 'Analiz Aşamasında')";
        } else {
            // Tümü seçilirse hem Ön Hazırlık hem de Analiz Aşamasındaki kayıtları göster
            $whereConditions[] = "(n.durumu = 'Ön Hazırlık' OR n.durumu = 'Analiz Aşamasında')";
        }

        // Tarih aralığı filtresi
        if (!empty($baslangicTarihi)) {
            $whereConditions[] = "DATE(n.gelis_tarihi) >= ?";
            $params[] = $baslangicTarihi;
        }
        if (!empty($bitisTarihi)) {
            $whereConditions[] = "DATE(n.gelis_tarihi) <= ?";
            $params[] = $bitisTarihi;
        }

        // Kayıt no filtresi
        if (!empty($kayitNo)) {
            $whereConditions[] = "n.LAB_KAYITNO LIKE ?";
            $params[] = "%$kayitNo%";
        }

        // Mühür no filtresi
        if (!empty($muhurNo)) {
            $whereConditions[] = "n.MUHUR_NO LIKE ?";
            $params[] = "%$muhurNo%";
        }

        $whereClause = implode(' AND ', $whereConditions);

        // Ön hazırlık aşamasındaki numuneleri getir (atama durumu ile birlikte)
        $sql = "SELECT n.*, u.urunadi_t as urun_adi, f.izahat as firma_adi,
                       a.id as atama_id, a.durum as atama_durum, a.atama_tarihi,
                       a.kabul_tarihi, a.hazirlik_tarihi,
                       lb.birimadi, lb.id as birim_id
                FROM lab_numuneler n
                LEFT JOIN urunler u ON n.urun_id = u.id
                LEFT JOIN geneltanim f ON n.firma_id = f.id
                INNER JOIN lab_atamalar a ON n.id = a.numune_id
                INNER JOIN laboratuvar_birimler lb ON a.birim_id = lb.id
                WHERE $whereClause
                ORDER BY a.atama_tarihi DESC
                LIMIT " . (($page - 1) * RECORDS_PER_PAGE) . ", " . RECORDS_PER_PAGE;

        $numuneler = $this->numuneModel->fetchAll($sql, $params);

        // Toplam sayı
        $countSql = "SELECT COUNT(*) as total
                     FROM lab_numuneler n
                     INNER JOIN lab_atamalar a ON n.id = a.numune_id
                     WHERE $whereClause";
        $totalResult = $this->numuneModel->fetchOne($countSql, $params);
        $total = $totalResult['total'];

        // Aktif birimleri getir
        $birimler = $this->birimModel->getActiveBirimler();

        $data = [
            'numuneler' => $numuneler,
            'pagination' => [
                'total' => $total,
                'pages' => ceil($total / RECORDS_PER_PAGE),
                'current_page' => $page
            ],
            'birimler' => $birimler,
            'user' => AuthController::getCurrentUser()
        ];

        require_once 'views/onhazirlik/index.php';
    }
    
    // Birim atama sayfası
    public function birimAtama($numuneId) {
        AuthController::requirePermission('NUMUNE_ATAMA_YAPABILIR');
        
        // Numune detaylarını getir
        $numune = $this->numuneModel->getNumuneDetails($numuneId);
        if (!$numune) {
            $_SESSION['error'] = ERROR_MESSAGES['record_not_found'];
            header('Location: index.php?page=onhazirlik');
            exit;
        }
        
        // Mevcut atamaları getir
        $atamalar = $this->numuneModel->getAtamalar($numuneId);
        
        // Aktif birimleri getir
        $birimler = $this->birimModel->getActiveBirimler();
        
        $data = [
            'numune' => $numune,
            'atamalar' => $atamalar,
            'birimler' => $birimler,
            'user' => AuthController::getCurrentUser()
        ];
        
        require_once 'views/onhazirlik/atama.php';
    }
    
    // Birim atama kaydet
    public function saveAtama() {
        AuthController::requirePermission('NUMUNE_ATAMA_YAPABILIR');
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Location: index.php?page=onhazirlik');
            exit;
        }
        
        $numuneId = $_POST['numune_id'] ?? 0;
        $birimId = $_POST['birim_id'] ?? 0;
        $atayanKullanici = $_SESSION['user_id'];
        
        if ($numuneId <= 0 || $birimId <= 0) {
            $_SESSION['error'] = 'Geçersiz numune veya birim seçimi.';
            header("Location: index.php?page=onhazirlik&action=atama&id=$numuneId");
            exit;
        }
        
        // Numune var mı kontrol et
        $numune = $this->numuneModel->getById($numuneId);
        if (!$numune) {
            $_SESSION['error'] = ERROR_MESSAGES['record_not_found'];
            header('Location: index.php?page=onhazirlik');
            exit;
        }
        
        // Birim var mı kontrol et
        $birim = $this->birimModel->getById($birimId);
        if (!$birim) {
            $_SESSION['error'] = 'Seçilen birim bulunamadı.';
            header("Location: index.php?page=onhazirlik&action=atama&id=$numuneId");
            exit;
        }
        
        // Daha önce bu birime atanmış mı kontrol et
        $mevcutAtama = $this->numuneModel->fetchOne(
            "SELECT id FROM lab_atamalar WHERE numune_id = ? AND birim_id = ?",
            [$numuneId, $birimId]
        );
        
        if ($mevcutAtama) {
            $_SESSION['warning'] = 'Bu numune zaten seçilen birime atanmış.';
            header("Location: index.php?page=onhazirlik&action=atama&id=$numuneId");
            exit;
        }
        
        // Atama yap
        $result = $this->numuneModel->assignToBirim($numuneId, $birimId, $atayanKullanici);
        
        if ($result) {
            $_SESSION['success'] = "Numune '{$birim['birimadi']}' birimine başarıyla atandı.";
        } else {
            $_SESSION['error'] = 'Atama sırasında hata oluştu.';
        }
        
        header("Location: index.php?page=onhazirlik&action=atama&id=$numuneId");
        exit;
    }
    
    // Atama silme
    public function deleteAtama() {
        AuthController::requirePermission('NUMUNE_ATAMA_YAPABILIR');
        
        $atamaId = $_GET['atama_id'] ?? 0;
        $numuneId = $_GET['numune_id'] ?? 0;
        
        if ($atamaId <= 0) {
            $_SESSION['error'] = 'Geçersiz atama ID.';
            header('Location: index.php?page=onhazirlik');
            exit;
        }
        
        try {
            // Atama kaydını sil
            $this->numuneModel->query("DELETE FROM lab_atamalar WHERE id = ?", [$atamaId]);
            
            // Log kaydı
            $this->numuneModel->query(
                "INSERT INTO lab_log (numune_id, islem, islemi_yapan, tarih) VALUES (?, ?, ?, ?)",
                [$numuneId, 'Birim ataması silindi', $_SESSION['user_name'] ?? 'Sistem', date('Y-m-d H:i:s')]
            );
            
            $_SESSION['success'] = 'Atama başarıyla silindi.';
            
        } catch (Exception $e) {
            $_SESSION['error'] = 'Atama silme sırasında hata oluştu.';
        }
        
        if ($numuneId > 0) {
            header("Location: index.php?page=onhazirlik&action=atama&id=$numuneId");
        } else {
            header('Location: index.php?page=onhazirlik');
        }
        exit;
    }
    
    // Toplu atama
    public function topluAtama() {
        AuthController::requirePermission('NUMUNE_ATAMA_YAPABILIR');
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Location: index.php?page=onhazirlik');
            exit;
        }
        
        $numuneIds = $_POST['numune_ids'] ?? [];
        $birimId = $_POST['birim_id'] ?? 0;
        $atayanKullanici = $_SESSION['user_id'];
        
        if (empty($numuneIds) || $birimId <= 0) {
            $_SESSION['error'] = 'Numune ve birim seçimi yapılmalıdır.';
            header('Location: index.php?page=onhazirlik');
            exit;
        }
        
        $basarili = 0;
        $hatali = 0;
        
        foreach ($numuneIds as $numuneId) {
            // Daha önce atanmış mı kontrol et
            $mevcutAtama = $this->numuneModel->fetchOne(
                "SELECT id FROM lab_atamalar WHERE numune_id = ? AND birim_id = ?",
                [$numuneId, $birimId]
            );
            
            if (!$mevcutAtama) {
                $result = $this->numuneModel->assignToBirim($numuneId, $birimId, $atayanKullanici);
                if ($result) {
                    $basarili++;
                } else {
                    $hatali++;
                }
            }
        }
        
        if ($basarili > 0) {
            $_SESSION['success'] = "$basarili numune başarıyla atandı.";
        }
        
        if ($hatali > 0) {
            $_SESSION['warning'] = "$hatali numune atanırken hata oluştu.";
        }
        
        header('Location: index.php?page=onhazirlik');
        exit;
    }
    
    // Numune durumunu güncelle
    public function updateDurum() {
        AuthController::requirePermission('NUMUNE_ONHAZIRLIK_YAPABILIR');
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Location: index.php?page=onhazirlik');
            exit;
        }
        
        $numuneId = $_POST['numune_id'] ?? 0;
        $yeniDurum = $_POST['durum'] ?? '';
        $aciklama = $_POST['aciklama'] ?? '';
        
        if ($numuneId <= 0 || empty($yeniDurum)) {
            $_SESSION['error'] = 'Geçersiz numune veya durum seçimi.';
            header('Location: index.php?page=onhazirlik');
            exit;
        }
        
        $result = $this->numuneModel->updateDurum($numuneId, $yeniDurum, $aciklama);
        
        if ($result) {
            $_SESSION['success'] = 'Numune durumu başarıyla güncellendi.';
        } else {
            $_SESSION['error'] = 'Durum güncelleme sırasında hata oluştu.';
        }
        
        header('Location: index.php?page=onhazirlik');
        exit;
    }
    
    // İstatistikler (AJAX)
    public function getStats() {
        header('Content-Type: application/json');
        
        $stats = [
            'bekleyen_atama' => $this->numuneModel->getCount("durumu = 'Numune Kabul' AND silindi = 0"),
            'onhazirlik' => $this->numuneModel->getCount("durumu = 'Ön Hazırlık' AND silindi = 0"),
            'analiz_asamasinda' => $this->numuneModel->getCount("durumu = 'Analiz Aşamasında' AND silindi = 0"),
            'toplam_atama' => $this->numuneModel->fetchOne("SELECT COUNT(*) as count FROM lab_atamalar")['count'] ?? 0
        ];
        
        echo json_encode($stats);
        exit;
    }

    // Atamayı kabul et
    public function kabulEt() {
        AuthController::requirePermission('NUMUNE_ONHAZIRLIK_YAPABILIR');

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Location: index.php?page=onhazirlik');
            exit;
        }

        $atamaIds = $_POST['atama_ids'] ?? [];
        $kullaniciId = $_SESSION['user_id'];

        if (empty($atamaIds)) {
            $_SESSION['error'] = 'Kabul edilecek atama seçilmedi.';
            header('Location: index.php?page=onhazirlik');
            exit;
        }

        $basarili = 0;
        $hatali = 0;

        foreach ($atamaIds as $atamaId) {
            try {
                // Önce mevcut durumu kontrol et
                $checkSql = "SELECT id, durum FROM lab_atamalar WHERE id = ?";
                $currentAtama = $this->numuneModel->fetchOne($checkSql, [$atamaId]);

                if (!$currentAtama) {
                    $hatali++;
                    continue;
                }

                if ($currentAtama['durum'] !== 'Atandi') {
                    $hatali++;
                    continue;
                }

                // Atama durumunu güncelle
                $sql = "UPDATE lab_atamalar
                        SET durum = 'Kabul Edildi',
                            kabul_tarihi = NOW(),
                            kabul_eden_kullanici = ?
                        WHERE id = ? AND durum = 'Atandi'";

                $result = $this->numuneModel->query($sql, [$kullaniciId, $atamaId]);

                if ($result) {
                    // Log ekle
                    $atamaSql = "SELECT a.numune_id, lb.birimadi
                                FROM lab_atamalar a
                                INNER JOIN laboratuvar_birimler lb ON a.birim_id = lb.id
                                WHERE a.id = ?";
                    $atama = $this->numuneModel->fetchOne($atamaSql, [$atamaId]);

                    if ($atama) {
                        $this->numuneModel->addLog(
                            $atama['numune_id'],
                            "Ön hazırlık kabul edildi - Birim: " . $atama['birimadi'],
                            $_SESSION['user_name'] ?? 'Sistem'
                        );
                    }

                    $basarili++;
                } else {
                    $hatali++;
                }
            } catch (Exception $e) {
                $hatali++;
            }
        }

        if ($basarili > 0) {
            $_SESSION['success'] = "$basarili atama başarıyla kabul edildi.";
        }

        if ($hatali > 0) {
            $_SESSION['warning'] = "$hatali atama kabul edilirken hata oluştu. Detaylar için log dosyasını kontrol edin.";
        }

        header('Location: index.php?page=onhazirlik');
        exit;
    }

    // Hazırlık yapıldı işaretle
    public function hazirlikYapildi() {
        AuthController::requirePermission('NUMUNE_ONHAZIRLIK_YAPABILIR');

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Location: index.php?page=onhazirlik');
            exit;
        }

        $atamaIds = $_POST['atama_ids'] ?? [];
        $kullaniciId = $_SESSION['user_id'];

        if (empty($atamaIds)) {
            $_SESSION['error'] = 'Hazırlığı yapılacak atama seçilmedi.';
            header('Location: index.php?page=onhazirlik');
            exit;
        }

        $basarili = 0;
        $hatali = 0;

        foreach ($atamaIds as $atamaId) {
            try {
                // Önce mevcut durumu kontrol et
                $checkSql = "SELECT durum FROM lab_atamalar WHERE id = ?";
                $currentAtama = $this->numuneModel->fetchOne($checkSql, [$atamaId]);

                if (!$currentAtama || $currentAtama['durum'] !== 'Kabul Edildi') {
                    $hatali++;
                    continue;
                }

                // Atama durumunu güncelle
                $sql = "UPDATE lab_atamalar
                        SET durum = 'Hazirlik Yapildi',
                            hazirlik_tarihi = NOW(),
                            hazirlik_yapan_kullanici = ?
                        WHERE id = ? AND durum = 'Kabul Edildi'";

                $result = $this->numuneModel->query($sql, [$kullaniciId, $atamaId]);

                if ($result) {
                    // Numune durumunu güncelle
                    $atamaSql = "SELECT numune_id, lb.birimadi
                                FROM lab_atamalar a
                                INNER JOIN laboratuvar_birimler lb ON a.birim_id = lb.id
                                WHERE a.id = ?";
                    $atama = $this->numuneModel->fetchOne($atamaSql, [$atamaId]);

                    if ($atama) {
                        // Numune durumunu "Analiz Aşamasında" yap
                        $this->numuneModel->update($atama['numune_id'], ['durumu' => 'Analiz Aşamasında']);

                        // Log ekle
                        $this->numuneModel->addLog(
                            $atama['numune_id'],
                            "Ön hazırlık tamamlandı - Birim: " . $atama['birimadi'],
                            $_SESSION['user_name'] ?? 'Sistem'
                        );
                    }

                    $basarili++;
                } else {
                    $hatali++;
                }
            } catch (Exception $e) {
                $hatali++;
                error_log("Hazırlık Yapıldı Hatası: " . $e->getMessage());
            }
        }

        if ($basarili > 0) {
            $_SESSION['success'] = "$basarili numune hazırlığı tamamlandı ve analiz aşamasına geçti.";
        }

        if ($hatali > 0) {
            $_SESSION['warning'] = "$hatali numune hazırlığı tamamlanırken hata oluştu.";
        }

        header('Location: index.php?page=onhazirlik');
        exit;
    }

    // Geri gönder işlemi
    public function geriGonder() {
        AuthController::requirePermission('NUMUNE_ONHAZIRLIK_YAPABILIR');

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Location: index.php?page=onhazirlik');
            exit;
        }

        $atamaIds = $_POST['atama_ids'] ?? [];
        $kullaniciId = $_SESSION['user_id'];

        if (empty($atamaIds)) {
            $_SESSION['error'] = 'Geri gönderilecek atama seçilmedi.';
            header('Location: index.php?page=onhazirlik');
            exit;
        }

        $basarili = 0;
        $hatali = 0;

        foreach ($atamaIds as $atamaId) {
            try {
                // Önce atama bilgilerini al
                $atamaSql = "SELECT a.numune_id, a.durum, lb.birimadi
                            FROM lab_atamalar a
                            INNER JOIN laboratuvar_birimler lb ON a.birim_id = lb.id
                            WHERE a.id = ?";
                $atama = $this->numuneModel->fetchOne($atamaSql, [$atamaId]);

                if (!$atama || $atama['durum'] !== 'Kabul Edildi') {
                    $hatali++;
                    continue;
                }

                $numuneId = $atama['numune_id'];

                // Tüm atamaları sil
                $deleteResult = $this->numuneModel->query("DELETE FROM lab_atamalar WHERE numune_id = ?", [$numuneId]);

                if ($deleteResult) {
                    // Numune durumunu "Numune Kabul" olarak geri al
                    $updateResult = $this->numuneModel->query(
                        "UPDATE lab_numuneler SET durumu = 'Numune Kabul' WHERE id = ?",
                        [$numuneId]
                    );

                    if ($updateResult) {
                        // Log ekle
                        $this->numuneModel->addLog(
                            $numuneId,
                            "Ön hazırlık geri gönderildi - Tüm atamalar silindi - Birim: " . $atama['birimadi'],
                            $_SESSION['user_name'] ?? 'Sistem'
                        );

                        $basarili++;
                    } else {
                        $hatali++;
                    }
                } else {
                    $hatali++;
                }
            } catch (Exception $e) {
                $hatali++;
            }
        }

        if ($basarili > 0) {
            $_SESSION['success'] = "$basarili atama başarıyla geri gönderildi.";
        }

        if ($hatali > 0) {
            $_SESSION['warning'] = "$hatali atama geri gönderilirken hata oluştu.";
        }

        header('Location: index.php?page=onhazirlik');
        exit;
    }
}
?>

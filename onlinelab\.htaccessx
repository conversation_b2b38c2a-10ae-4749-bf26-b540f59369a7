# Apache Rewrite Rules
RewriteEngine On

# HTTPS yönlendirmesi (production için)
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Gü<PERSON>lik başlıkları
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    Header always set Permissions-Policy "geolocation=(), microphone=(), camera=()"
</IfModule>

# Dosya uzantılarını gizle
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_FILENAME} !-f
RewriteRule ^([^\.]+)$ $1.php [NC,L]

# Hassas dosyalara erişimi engelle
<FilesMatch "\.(ini|log|conf|sql|bak)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Config dizinine erişimi engelle
<Directory "config">
    Order Allow,Deny
    Deny from all
</Directory>

# Install dizinine erişimi engelle (production için)
# <Directory "install">
#     Order Allow,Deny
#     Deny from all
# </Directory>

# Gzip sıkıştırma
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Cache ayarları
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/ico "access plus 1 month"
    ExpiresByType image/icon "access plus 1 month"
    ExpiresByType text/plain "access plus 1 month"
    ExpiresByType application/pdf "access plus 1 month"
</IfModule>

# PHP ayarları
<IfModule mod_php7.c>
    php_value upload_max_filesize 5M
    php_value post_max_size 5M
    php_value max_execution_time 300
    php_value max_input_vars 3000
    php_value memory_limit 128M
</IfModule>

# Error pages
ErrorDocument 404 /laboratuvar/onlinelab/index.php?page=404
ErrorDocument 403 /laboratuvar/onlinelab/index.php?page=403
ErrorDocument 500 /laboratuvar/onlinelab/index.php?page=500
